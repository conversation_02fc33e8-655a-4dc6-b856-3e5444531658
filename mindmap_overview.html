<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>Markmap</title>
<style>
* {
  margin: 0;
  padding: 0;
}
#mindmap {
  display: block;
  width: 100vw;
  height: 100vh;
}
</style>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/markmap-toolbar@0.18.10/dist/style.css"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@highlightjs/cdn-assets@11.11.1/styles/default.min.css">
</head>
<body>
<svg id="mindmap"></svg>
<script src="https://cdn.jsdelivr.net/npm/d3@7.9.0/dist/d3.min.js"></script><script src="https://cdn.jsdelivr.net/npm/markmap-view@0.18.10/dist/browser/index.js"></script><script src="https://cdn.jsdelivr.net/npm/markmap-toolbar@0.18.10/dist/index.js"></script><script>(r => {
              setTimeout(r);
            })(function renderToolbar() {
  const {
    markmap,
    mm
  } = window;
  const {
    el
  } = markmap.Toolbar.create(mm);
  el.setAttribute('style', 'position:absolute;bottom:20px;right:20px');
  document.body.append(el);
})</script><script>((getMarkmap, getOptions, root2, jsonOptions) => {
              const markmap = getMarkmap();
              window.mm = markmap.Markmap.create(
                "svg#mindmap",
                (getOptions || markmap.deriveOptions)(jsonOptions),
                root2
              );
            })(() => window.markmap,null,{"content":"Training v&#x1ec1; AI","children":[{"content":"Quy tr&#xec;nh ph&#xe1;t tri&#x1ec3;n ph&#x1ea7;n m&#x1ec1;m ph&#x1ed5; bi&#x1ebf;n hi&#x1ec7;n nay","children":[{"content":"(1) T&#x1ed5;ng h&#x1ee3;p requirement t&#x1eeb; kh&#xe1;ch h&#xe0;ng","children":[{"content":"Role: BA","children":[],"payload":{"tag":"li","lines":"17,18"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"GoogleNoteBookLM","children":[],"payload":{"tag":"li","lines":"19,20"}},{"content":"Confluence","children":[],"payload":{"tag":"li","lines":"20,21"}},{"content":"Notion","children":[],"payload":{"tag":"li","lines":"21,22"}}],"payload":{"tag":"li","lines":"18,22"}},{"content":"Input:","children":[{"content":"T&#xe0;i li&#x1ec7;u kh&#xe1;ch h&#xe0;ng g&#x1eed;i","children":[],"payload":{"tag":"li","lines":"23,24"}},{"content":"H&#x1ec7; th&#x1ed1;ng tham kh&#x1ea3;o (n&#x1ebf;u c&#xf3;, d&#x1ea1;ng &#x1ea3;nh ch&#x1ee5;p m&#xe0;n h&#xec;nh)","children":[],"payload":{"tag":"li","lines":"24,25"}},{"content":"N&#x1ed9;i dung c&#xe1;c cu&#x1ed9;c h&#x1ecd;p","children":[],"payload":{"tag":"li","lines":"25,26"}}],"payload":{"tag":"li","lines":"22,26"}},{"content":"Output:","children":[{"content":"File t&#xe0;i li&#x1ec7;u requirement t&#x1ed5;ng h&#x1ee3;p t&#x1eeb; t&#x1ea5;t c&#x1ea3; c&#xe1;c ngu&#x1ed3;n (ho&#x1eb7;c c&#xf3; th&#x1ec3; tra c&#x1ee9;u l&#x1ea1;i v&#x1ec1; sau)","children":[],"payload":{"tag":"li","lines":"27,28"}}],"payload":{"tag":"li","lines":"26,28"}}],"payload":{"tag":"li","lines":"16,28"}},{"content":"(2) T&#x1ea1;o t&#xe0;i li&#x1ec7;u SRS (Software Requirement Specification)","children":[{"content":"Role: BA","children":[],"payload":{"tag":"li","lines":"29,30"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"GoogleNoteBookLM - &#x111;&#x1ec3; tra c&#x1ee9;u","children":[],"payload":{"tag":"li","lines":"31,32"}},{"content":"Custom GPT (ChatGPT Plus) - &#x111;&#x1ec3; bi&#xea;n t&#x1ead;p","children":[],"payload":{"tag":"li","lines":"32,33"}}],"payload":{"tag":"li","lines":"30,33"}},{"content":"Input:","children":[{"content":"T&#xe0;i li&#x1ec7;u requirement &#x1edf; b&#x1b0;&#x1edb;c (1)","children":[],"payload":{"tag":"li","lines":"34,35"}}],"payload":{"tag":"li","lines":"33,35"}},{"content":"Output:","children":[{"content":"T&#xe0;i li&#x1ec7;u SRS, s&#x1eb5;n s&#xe0;ng cho dev break task,  thi&#x1ebf;t k&#x1ebf; ki&#x1ebf;n tr&#xfa;c h&#x1ec7; th&#x1ed1;ng, thi&#x1ebf;t k&#x1ebf; giao di&#x1ec7;n v&#xe0; implement ch&#x1ee9;c n&#x103;ng","children":[],"payload":{"tag":"li","lines":"36,37"}}],"payload":{"tag":"li","lines":"35,37"}}],"payload":{"tag":"li","lines":"28,37"}},{"content":"(3) Thi&#x1ebf;t k&#x1ebf; ki&#x1ebf;n tr&#xfa;c h&#x1ec7; th&#x1ed1;ng. (Architecture Design)","children":[{"content":"Role: System Architect","children":[],"payload":{"tag":"li","lines":"38,39"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"Custom GTP (ChatGPT)","children":[],"payload":{"tag":"li","lines":"40,41"}},{"content":"Github Copilot","children":[],"payload":{"tag":"li","lines":"41,42"}},{"content":"Cursor","children":[],"payload":{"tag":"li","lines":"42,43"}},{"content":"Augment","children":[],"payload":{"tag":"li","lines":"43,44"}}],"payload":{"tag":"li","lines":"39,44"}},{"content":"Input:","children":[{"content":"T&#xe0;i li&#x1ec7;u SRS &#x1edf; b&#x1b0;&#x1edb;c (2)","children":[],"payload":{"tag":"li","lines":"45,46"}},{"content":"T&#xe0;i li&#x1ec7;u requirement &#x1edf; b&#x1b0;&#x1edb;c (1): v&#xed; d&#x1ee5; c&#xe1;c quy &#x111;&#x1ecb;nh v&#x1ec1; k&#x1ef9; thu&#x1ead;t trong d&#x1ef1; &#xe1;n","children":[],"payload":{"tag":"li","lines":"46,47"}},{"content":"M&#x1eab;u ki&#x1ebf;n tr&#xfa;c c&#x1ee7;a d&#x1ef1; &#xe1;n t&#x1b0;&#x1a1;ng t&#x1ef1; tr&#x1b0;&#x1edb;c &#x111;&#xf3; &#x111;&#x1ec3; tham kh&#x1ea3;o","children":[],"payload":{"tag":"li","lines":"47,48"}},{"content":"M&#x1eab;u project d&#x1ef1; &#xe1;n tr&#x1b0;&#x1edb;c &#x111;&#xf3; &#x111;&#xe3; c&#xf3; &#x111;&#x1ec3; tham kh&#x1ea3;o","children":[],"payload":{"tag":"li","lines":"48,49"}},{"content":"M&#x1eab;u CI/CD d&#x1ef1; &#xe1;n tr&#x1b0;&#x1edb;c &#x111;&#xf3; &#x111;&#xe3; c&#xf3; &#x111;&#x1ec3; tham kh&#x1ea3;o","children":[],"payload":{"tag":"li","lines":"49,50"}}],"payload":{"tag":"li","lines":"44,50"}},{"content":"Output:","children":[{"content":"T&#xe0;i li&#x1ec7;u thi&#x1ebf;t k&#x1ebf; ki&#x1ebf;n tr&#xfa;c theo chu&#x1ea9;n thi&#x1ebf;t k&#x1ebf; C4 ho&#x1eb7;c UML","children":[],"payload":{"tag":"li","lines":"51,52"}},{"content":"T&#x1ea1;o project structure ban &#x111;&#x1ea7;u cho d&#x1ef1; &#xe1;n","children":[],"payload":{"tag":"li","lines":"52,53"}},{"content":"Generate CI/CD cho d&#x1ef1; &#xe1;n","children":[],"payload":{"tag":"li","lines":"53,54"}},{"content":"T&#xe0;i li&#x1ec7;u coding convention","children":[],"payload":{"tag":"li","lines":"54,55"}},{"content":"T&#xe0;i li&#x1ec7;u guideline t&#x1ea1;o test","children":[],"payload":{"tag":"li","lines":"55,56"}}],"payload":{"tag":"li","lines":"50,56"}}],"payload":{"tag":"li","lines":"37,56"}},{"content":"(4) Thi&#x1ebf;t k&#x1ebf; giao di&#x1ec7;n / prototype","children":[{"content":"Role: Designer","children":[],"payload":{"tag":"li","lines":"57,58"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"Figma","children":[],"payload":{"tag":"li","lines":"59,60"}},{"content":"V0","children":[],"payload":{"tag":"li","lines":"60,61"}},{"content":"UX pilot","children":[],"payload":{"tag":"li","lines":"61,62"}}],"payload":{"tag":"li","lines":"58,62"}},{"content":"Input:","children":[{"content":"Requirement c&#x1ee7;a kh&#xe1;ch h&#xe0;ng &#x1edf; b&#x1b0;&#x1edb;c (1) (y&#xea;u c&#x1ea7;u v&#x1ec1; giao di&#x1ec7;n)","children":[],"payload":{"tag":"li","lines":"63,64"}},{"content":"T&#xe0;i li&#x1ec7;u SRS &#x1edf; b&#x1b0;&#x1edb;c (2)","children":[],"payload":{"tag":"li","lines":"64,65"}},{"content":"T&#xe0;i li&#x1ec7;u architecture design &#x1edf; b&#x1b0;&#x1edb;c (3)","children":[],"payload":{"tag":"li","lines":"65,66"}}],"payload":{"tag":"li","lines":"62,66"}},{"content":"Output:","children":[{"content":"Giao di&#x1ec7;n c&#xe1;c page theo SRS","children":[],"payload":{"tag":"li","lines":"67,68"}}],"payload":{"tag":"li","lines":"66,68"}}],"payload":{"tag":"li","lines":"56,68"}},{"content":"(5) Thi&#x1ebf;t k&#x1ebf; chi ti&#x1ebf;t ph&#x1ea7;n implement c&#xe1;c ch&#x1ee9;c n&#x103;ng. (Detailed Design)","children":[{"content":"Role: System Architect, Developer","children":[],"payload":{"tag":"li","lines":"69,70"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"Custom GTP (ChatGPT)","children":[],"payload":{"tag":"li","lines":"71,72"}},{"content":"Grok workspace","children":[],"payload":{"tag":"li","lines":"72,73"}},{"content":"Custom Gem (Gemini).","children":[],"payload":{"tag":"li","lines":"73,74"}},{"content":"Github Copilot","children":[],"payload":{"tag":"li","lines":"74,75"}},{"content":"Cursor","children":[],"payload":{"tag":"li","lines":"75,76"}},{"content":"Augment","children":[],"payload":{"tag":"li","lines":"76,77"}}],"payload":{"tag":"li","lines":"70,77"}},{"content":"Input:","children":[{"content":"T&#xe0;i li&#x1ec7;u SRS &#x1edf; b&#x1b0;&#x1edb;c (2)","children":[],"payload":{"tag":"li","lines":"78,79"}},{"content":"T&#xe0;i li&#x1ec7;u architecture design &#x1edf; b&#x1b0;&#x1edb;c (3)","children":[],"payload":{"tag":"li","lines":"79,80"}},{"content":"T&#xe0;i li&#x1ec7;u thi&#x1ebf;t k&#x1ebf; giao di&#x1ec7;n &#x1edf; b&#x1b0;&#x1edb;c (4)","children":[],"payload":{"tag":"li","lines":"80,81"}}],"payload":{"tag":"li","lines":"77,81"}},{"content":"Output:","children":[{"content":"T&#xe0;i li&#x1ec7;u thi&#x1ebf;t k&#x1ebf; implement chi ti&#x1ebf;t c&#xe1;c t&#xed;nh n&#x103;ng, bao g&#x1ed3;m:","children":[{"content":"C&#xe1;c API c&#x1ea7;n thi&#x1ebf;t","children":[],"payload":{"tag":"li","lines":"83,84"}},{"content":"C&#xe1;c module c&#x1ea7;n thi&#x1ebf;t","children":[],"payload":{"tag":"li","lines":"84,85"}},{"content":"C&#xe1;c class c&#x1ea7;n thi&#x1ebf;t","children":[],"payload":{"tag":"li","lines":"85,86"}},{"content":"C&#xe1;c database c&#x1ea7;n thi&#x1ebf;t","children":[],"payload":{"tag":"li","lines":"86,87"}}],"payload":{"tag":"li","lines":"82,87"}},{"content":"Estimate th&#x1edd;i gian implement c&#xe1;c t&#xed;nh n&#x103;ng","children":[],"payload":{"tag":"li","lines":"87,88"}},{"content":"T&#x1ea1;o task (Jira ho&#x1eb7;c Gitlab issues ho&#x1eb7;c TaskMaster)","children":[],"payload":{"tag":"li","lines":"88,89"}}],"payload":{"tag":"li","lines":"81,89"}}],"payload":{"tag":"li","lines":"68,89"}},{"content":"(6) Ph&#xe2;n task, l&#xea;n plan, qu&#x1ea3;n l&#xfd; plan","children":[{"content":"Role: PM / TeamLeader","children":[],"payload":{"tag":"li","lines":"90,91"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"Jira","children":[],"payload":{"tag":"li","lines":"92,93"}},{"content":"TaskMaster","children":[],"payload":{"tag":"li","lines":"93,94"}},{"content":"Gitlab issues","children":[],"payload":{"tag":"li","lines":"94,95"}}],"payload":{"tag":"li","lines":"91,95"}},{"content":"Input:","children":[{"content":"T&#xe0;i li&#x1ec7;u thi&#x1ebf;t k&#x1ebf; chi ti&#x1ebf;t ph&#x1ea7;n implement c&#xe1;c ch&#x1ee9;c n&#x103;ng &#x1edf; b&#x1b0;&#x1edb;c (5)","children":[],"payload":{"tag":"li","lines":"96,97"}},{"content":"T&#xe0;i li&#x1ec7;u thi&#x1ebf;t k&#x1ebf; ki&#x1ebf;n tr&#xfa;c h&#x1ec7; th&#x1ed1;ng &#x1edf; b&#x1b0;&#x1edb;c (3)","children":[],"payload":{"tag":"li","lines":"97,98"}},{"content":"T&#xe0;i li&#x1ec7;u thi&#x1ebf;t k&#x1ebf; giao di&#x1ec7;n &#x1edf; b&#x1b0;&#x1edb;c (4)","children":[],"payload":{"tag":"li","lines":"98,99"}}],"payload":{"tag":"li","lines":"95,99"}},{"content":"Output:","children":[{"content":"Ph&#xe2;n chia task cho c&#xe1;c developer","children":[],"payload":{"tag":"li","lines":"100,101"}},{"content":"L&#xea;n k&#x1ebf; ho&#x1ea1;ch th&#x1ef1;c hi&#x1ec7;n c&#xe1;c task","children":[],"payload":{"tag":"li","lines":"101,102"}},{"content":"Qu&#x1ea3;n l&#xfd; ti&#x1ebf;n &#x111;&#x1ed9; th&#x1ef1;c hi&#x1ec7;n c&#xe1;c task","children":[],"payload":{"tag":"li","lines":"102,103"}},{"content":"Theo d&#xf5;i bug v&#xe0; issue trong qu&#xe1; tr&#xec;nh ph&#xe1;t tri&#x1ec3;n","children":[],"payload":{"tag":"li","lines":"103,104"}}],"payload":{"tag":"li","lines":"99,104"}}],"payload":{"tag":"li","lines":"89,104"}},{"content":"(7) Vi&#x1ebf;t code","children":[{"content":"Role: Developer","children":[],"payload":{"tag":"li","lines":"105,106"}},{"content":"C&#xf4;ng c&#x1ee5; (L&#x1ea5;y c&#xe1;c task / bug th&#xf4;ng qua MCP):","children":[{"content":"Github Copilot","children":[],"payload":{"tag":"li","lines":"107,108"}},{"content":"Cursor","children":[],"payload":{"tag":"li","lines":"108,109"}}],"payload":{"tag":"li","lines":"106,109"}},{"content":"Input:","children":[{"content":"Task / Issue ticket &#x1edf; b&#x1b0;&#x1edb;c (6)","children":[],"payload":{"tag":"li","lines":"110,111"}},{"content":"Coding convention &#x1edf; b&#x1b0;&#x1edb;c (2)","children":[],"payload":{"tag":"li","lines":"111,112"}}],"payload":{"tag":"li","lines":"109,112"}},{"content":"Output:","children":[{"content":"Source code","children":[],"payload":{"tag":"li","lines":"113,114"}}],"payload":{"tag":"li","lines":"112,114"}}],"payload":{"tag":"li","lines":"104,114"}},{"content":"(8) Vi&#x1ebf;t test case","children":[{"content":"UT","children":[{"content":"Role: Developer","children":[],"payload":{"tag":"li","lines":"116,117"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"Github Copilot","children":[],"payload":{"tag":"li","lines":"118,119"}},{"content":"Cursor","children":[],"payload":{"tag":"li","lines":"119,120"}},{"content":"Augment","children":[],"payload":{"tag":"li","lines":"120,121"}}],"payload":{"tag":"li","lines":"117,121"}}],"payload":{"tag":"li","lines":"115,121"}},{"content":"E2E test","children":[{"content":"Role: Tester / Developer","children":[],"payload":{"tag":"li","lines":"122,123"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"Github Copilot","children":[],"payload":{"tag":"li","lines":"124,125"}},{"content":"Cursor","children":[],"payload":{"tag":"li","lines":"125,126"}},{"content":"Augment","children":[],"payload":{"tag":"li","lines":"126,127"}}],"payload":{"tag":"li","lines":"123,127"}}],"payload":{"tag":"li","lines":"121,127"}},{"content":"Input:","children":[{"content":"Source code &#x1edf; b&#x1b0;&#x1edb;c (7)","children":[],"payload":{"tag":"li","lines":"128,129"}},{"content":"T&#xe0;i li&#x1ec7;u thi&#x1ebf;t k&#x1ebf; chi ti&#x1ebf;t ph&#x1ea7;n implement c&#xe1;c ch&#x1ee9;c n&#x103;ng &#x1edf; b&#x1b0;&#x1edb;c (5)","children":[],"payload":{"tag":"li","lines":"129,130"}},{"content":"T&#xe0;i li&#x1ec7;u guideline t&#x1ea1;o test &#x1edf; b&#x1b0;&#x1edb;c (3)","children":[],"payload":{"tag":"li","lines":"130,131"}}],"payload":{"tag":"li","lines":"127,131"}},{"content":"Output:","children":[{"content":"Script test case","children":[],"payload":{"tag":"li","lines":"132,133"}}],"payload":{"tag":"li","lines":"131,133"}}],"payload":{"tag":"li","lines":"114,133"}},{"content":"(9) Review","children":[{"content":"Role: DevOps","children":[],"payload":{"tag":"li","lines":"134,135"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"CI/CD + n8n workflow + AI Agent + LLM API","children":[],"payload":{"tag":"li","lines":"136,137"}}],"payload":{"tag":"li","lines":"135,137"}},{"content":"Input:","children":[{"content":"Pull request","children":[],"payload":{"tag":"li","lines":"138,139"}}],"payload":{"tag":"li","lines":"137,139"}},{"content":"Output:","children":[{"content":"K&#x1ebf;t qu&#x1ea3; review code","children":[],"payload":{"tag":"li","lines":"140,141"}},{"content":"K&#x1ebf;t qu&#x1ea3; ch&#x1ea1;y UT","children":[],"payload":{"tag":"li","lines":"141,142"}},{"content":"K&#x1ebf;t qu&#x1ea3; ch&#x1ea1;y E2E test","children":[],"payload":{"tag":"li","lines":"142,143"}}],"payload":{"tag":"li","lines":"139,143"}}],"payload":{"tag":"li","lines":"133,143"}},{"content":"(10) Vi&#x1ebf;t t&#xe0;i li&#x1ec7;u h&#x1b0;&#x1edb;ng d&#x1eab;n s&#x1eed; d&#x1ee5;ng","children":[{"content":"Role: BA / Developer","children":[],"payload":{"tag":"li","lines":"144,145"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"Custom GTP (ChatGPT)","children":[],"payload":{"tag":"li","lines":"146,147"}},{"content":"Grok workspace","children":[],"payload":{"tag":"li","lines":"147,148"}},{"content":"Custom Gem (Gemini).","children":[],"payload":{"tag":"li","lines":"148,149"}},{"content":"Github Copilot","children":[],"payload":{"tag":"li","lines":"149,150"}},{"content":"Cursor","children":[],"payload":{"tag":"li","lines":"150,151"}},{"content":"Augment","children":[],"payload":{"tag":"li","lines":"151,152"}}],"payload":{"tag":"li","lines":"145,152"}},{"content":"Input:","children":[{"content":"Source code &#x1edf; b&#x1b0;&#x1edb;c (7)","children":[],"payload":{"tag":"li","lines":"153,154"}},{"content":"T&#xe0;i li&#x1ec7;u thi&#x1ebf;t k&#x1ebf; chi ti&#x1ebf;t ph&#x1ea7;n implement c&#xe1;c ch&#x1ee9;c n&#x103;ng &#x1edf; b&#x1b0;&#x1edb;c (5)","children":[],"payload":{"tag":"li","lines":"154,155"}},{"content":"T&#xe0;i li&#x1ec7;u SRS &#x1edf; b&#x1b0;&#x1edb;c (2)","children":[],"payload":{"tag":"li","lines":"155,156"}},{"content":"T&#xe0;i li&#x1ec7;u thi&#x1ebf;t k&#x1ebf; giao di&#x1ec7;n &#x1edf; b&#x1b0;&#x1edb;c (4)","children":[],"payload":{"tag":"li","lines":"156,157"}}],"payload":{"tag":"li","lines":"152,157"}},{"content":"Output:","children":[{"content":"T&#xe0;i li&#x1ec7;u h&#x1b0;&#x1edb;ng d&#x1eab;n s&#x1eed; d&#x1ee5;ng h&#x1ec7; th&#x1ed1;ng, bao g&#x1ed3;m:","children":[{"content":"H&#x1b0;&#x1edb;ng d&#x1eab;n c&#xe0;i &#x111;&#x1eb7;t","children":[],"payload":{"tag":"li","lines":"159,160"}},{"content":"H&#x1b0;&#x1edb;ng d&#x1eab;n s&#x1eed; d&#x1ee5;ng c&#xe1;c ch&#x1ee9;c n&#x103;ng","children":[],"payload":{"tag":"li","lines":"160,161"}}],"payload":{"tag":"li","lines":"158,161"}}],"payload":{"tag":"li","lines":"157,161"}}],"payload":{"tag":"li","lines":"143,161"}},{"content":"(11) Deploy h&#x1ec7; th&#x1ed1;ng / CI/CD (Gitlab / Docker / Kubernetes)","children":[{"content":"Role: DevOps","children":[],"payload":{"tag":"li","lines":"162,163"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"Github Copilot","children":[],"payload":{"tag":"li","lines":"164,165"}},{"content":"Cursor","children":[],"payload":{"tag":"li","lines":"165,166"}},{"content":"Augment","children":[],"payload":{"tag":"li","lines":"166,167"}}],"payload":{"tag":"li","lines":"163,167"}},{"content":"Input:","children":[{"content":"T&#xe0;i li&#x1ec7;u thi&#x1ebf;t k&#x1ebf; ki&#x1ebf;n tr&#xfa;c h&#x1ec7; th&#x1ed1;ng &#x1edf; b&#x1b0;&#x1edb;c (3)","children":[],"payload":{"tag":"li","lines":"168,169"}},{"content":"T&#xe0;i li&#x1ec7;u thi&#x1ebf;t k&#x1ebf; chi ti&#x1ebf;t ph&#x1ea7;n implement c&#xe1;c ch&#x1ee9;c n&#x103;ng &#x1edf; b&#x1b0;&#x1edb;c (5)","children":[],"payload":{"tag":"li","lines":"169,170"}},{"content":"Source code &#x1edf; b&#x1b0;&#x1edb;c (7)","children":[],"payload":{"tag":"li","lines":"170,171"}}],"payload":{"tag":"li","lines":"167,171"}},{"content":"Output:","children":[{"content":"H&#x1ec7; th&#x1ed1;ng &#x111;&#x1b0;&#x1ee3;c deploy l&#xea;n m&#xf4;i tr&#x1b0;&#x1edd;ng staging / production","children":[],"payload":{"tag":"li","lines":"172,173"}},{"content":"CI/CD pipeline &#x111;&#x1b0;&#x1ee3;c thi&#x1ebf;t l&#x1ead;p v&#xe0; ho&#x1ea1;t &#x111;&#x1ed9;ng","children":[],"payload":{"tag":"li","lines":"173,174"}},{"content":"T&#xe0;i li&#x1ec7;u h&#x1b0;&#x1edb;ng d&#x1eab;n deploy h&#x1ec7; th&#x1ed1;ng","children":[],"payload":{"tag":"li","lines":"174,175"}}],"payload":{"tag":"li","lines":"171,175"}}],"payload":{"tag":"li","lines":"161,175"}},{"content":"(12) Monitoring / Tracing / Logging","children":[{"content":"Role: DevOps","children":[],"payload":{"tag":"li","lines":"176,177"}},{"content":"C&#xf4;ng c&#x1ee5;:","children":[{"content":"Signoz","children":[],"payload":{"tag":"li","lines":"178,179"}},{"content":"Jeager","children":[],"payload":{"tag":"li","lines":"179,180"}},{"content":"ELK stack","children":[],"payload":{"tag":"li","lines":"180,181"}},{"content":"Prometheus &amp; Grafana","children":[],"payload":{"tag":"li","lines":"181,182"}}],"payload":{"tag":"li","lines":"177,182"}},{"content":"Input:","children":[{"content":"H&#x1ec7; th&#x1ed1;ng &#x111;&#xe3; &#x111;&#x1b0;&#x1ee3;c deploy &#x1edf; b&#x1b0;&#x1edb;c (11)","children":[],"payload":{"tag":"li","lines":"183,184"}}],"payload":{"tag":"li","lines":"182,184"}},{"content":"Output:","children":[{"content":"H&#x1ec7; th&#x1ed1;ng &#x111;&#x1b0;&#x1ee3;c gi&#xe1;m s&#xe1;t, theo d&#xf5;i v&#xe0; ghi log","children":[],"payload":{"tag":"li","lines":"185,186"}},{"content":"T&#xe0;i li&#x1ec7;u h&#x1b0;&#x1edb;ng d&#x1eab;n gi&#xe1;m s&#xe1;t h&#x1ec7; th&#x1ed1;ng","children":[],"payload":{"tag":"li","lines":"186,188"}}],"payload":{"tag":"li","lines":"184,188"}}],"payload":{"tag":"li","lines":"175,188"}}],"payload":{"tag":"h2","lines":"14,15"}},{"content":"Templates &amp; Standards","children":[{"content":"T&#xe0;i li&#x1ec7;u Template","children":[{"content":"\n<p data-lines=\"191,192\"><strong>SRS Template</strong>: Template chu&#x1ea9;n cho Software Requirement Specification v&#x1edb;i c&#xe1;c ph&#x1ea7;n:</p>","children":[{"content":"T&#xf3;m t&#x1eaf;t &#x111;i&#x1ec1;u h&#xe0;nh","children":[],"payload":{"tag":"li","lines":"192,193"}},{"content":"Y&#xea;u c&#x1ea7;u ch&#x1ee9;c n&#x103;ng","children":[],"payload":{"tag":"li","lines":"193,194"}},{"content":"Y&#xea;u c&#x1ea7;u phi ch&#x1ee9;c n&#x103;ng","children":[],"payload":{"tag":"li","lines":"194,195"}},{"content":"T&#x1ed5;ng quan ki&#x1ebf;n tr&#xfa;c h&#x1ec7; th&#x1ed1;ng","children":[],"payload":{"tag":"li","lines":"195,196"}},{"content":"Y&#xea;u c&#x1ea7;u giao di&#x1ec7;n ng&#x1b0;&#x1edd;i d&#xf9;ng","children":[],"payload":{"tag":"li","lines":"196,197"}},{"content":"Y&#xea;u c&#x1ea7;u Database","children":[],"payload":{"tag":"li","lines":"197,198"}},{"content":"Y&#xea;u c&#x1ea7;u t&#xed;ch h&#x1ee3;p","children":[],"payload":{"tag":"li","lines":"198,199"}},{"content":"Y&#xea;u c&#x1ea7;u b&#x1ea3;o m&#x1ead;t","children":[],"payload":{"tag":"li","lines":"199,200"}},{"content":"Y&#xea;u c&#x1ea7;u hi&#x1ec7;u su&#x1ea5;t","children":[],"payload":{"tag":"li","lines":"200,201"}},{"content":"Ti&#xea;u ch&#xed; ch&#x1ea5;p nh&#x1ead;n","children":[],"payload":{"tag":"li","lines":"201,203"}}],"payload":{"tag":"li","lines":"191,203"}},{"content":"\n<p data-lines=\"203,204\"><strong>Architecture Design Template</strong> (C4 Model):</p>","children":[{"content":"Context Diagram","children":[],"payload":{"tag":"li","lines":"204,205"}},{"content":"Container Diagram","children":[],"payload":{"tag":"li","lines":"205,206"}},{"content":"Component Diagram","children":[],"payload":{"tag":"li","lines":"206,207"}},{"content":"Code Diagram","children":[],"payload":{"tag":"li","lines":"207,208"}},{"content":"Deployment Diagram","children":[],"payload":{"tag":"li","lines":"208,209"}},{"content":"Quy&#x1ebf;t &#x111;&#x1ecb;nh Technology Stack","children":[],"payload":{"tag":"li","lines":"209,210"}},{"content":"Ph&#xe2;n t&#xed;ch thu&#x1ed9;c t&#xed;nh ch&#x1ea5;t l&#x1b0;&#x1ee3;ng","children":[],"payload":{"tag":"li","lines":"210,212"}}],"payload":{"tag":"li","lines":"203,212"}},{"content":"\n<p data-lines=\"212,213\"><strong>API Design Template</strong> (OpenAPI/Swagger):</p>","children":[{"content":"T&#x1ed5;ng quan v&#xe0; m&#x1ee5;c &#x111;&#xed;ch API","children":[],"payload":{"tag":"li","lines":"213,214"}},{"content":"Authentication &amp; Authorization","children":[],"payload":{"tag":"li","lines":"214,215"}},{"content":"&#x110;&#x1eb7;c t&#x1ea3; Endpoint","children":[],"payload":{"tag":"li","lines":"215,216"}},{"content":"V&#xed; d&#x1ee5; Request/Response","children":[],"payload":{"tag":"li","lines":"216,217"}},{"content":"X&#x1eed; l&#xfd; l&#x1ed7;i","children":[],"payload":{"tag":"li","lines":"217,218"}},{"content":"Rate Limiting","children":[],"payload":{"tag":"li","lines":"218,219"}},{"content":"Chi&#x1ebf;n l&#x1b0;&#x1ee3;c versioning","children":[],"payload":{"tag":"li","lines":"219,221"}}],"payload":{"tag":"li","lines":"212,221"}},{"content":"\n<p data-lines=\"221,222\"><strong>Test Case Template</strong>:</p>","children":[{"content":"Ph&#x1ea1;m vi v&#xe0; m&#x1ee5;c ti&#xea;u ki&#x1ec3;m th&#x1eed;","children":[],"payload":{"tag":"li","lines":"222,223"}},{"content":"Thi&#x1ebf;t l&#x1ead;p m&#xf4;i tr&#x1b0;&#x1edd;ng test","children":[],"payload":{"tag":"li","lines":"223,224"}},{"content":"C&#x1ea5;u tr&#xfa;c Unit Test","children":[],"payload":{"tag":"li","lines":"224,225"}},{"content":"Test Case t&#xed;ch h&#x1ee3;p","children":[],"payload":{"tag":"li","lines":"225,226"}},{"content":"K&#x1ecb;ch b&#x1ea3;n E2E Test","children":[],"payload":{"tag":"li","lines":"226,227"}},{"content":"Test Case hi&#x1ec7;u su&#x1ea5;t","children":[],"payload":{"tag":"li","lines":"227,228"}},{"content":"Test Case b&#x1ea3;o m&#x1ead;t","children":[],"payload":{"tag":"li","lines":"228,230"}}],"payload":{"tag":"li","lines":"221,230"}},{"content":"\n<p data-lines=\"230,231\"><strong>T&#xe0;i li&#x1ec7;u h&#x1b0;&#x1edb;ng d&#x1eab;n ng&#x1b0;&#x1edd;i d&#xf9;ng Template</strong>:</p>","children":[{"content":"H&#x1b0;&#x1edb;ng d&#x1eab;n c&#xe0;i &#x111;&#x1eb7;t","children":[],"payload":{"tag":"li","lines":"231,232"}},{"content":"S&#x1ed5; tay ng&#x1b0;&#x1edd;i d&#xf9;ng","children":[],"payload":{"tag":"li","lines":"232,233"}},{"content":"T&#xe0;i li&#x1ec7;u API","children":[],"payload":{"tag":"li","lines":"233,234"}},{"content":"H&#x1b0;&#x1edb;ng d&#x1eab;n kh&#x1eaf;c ph&#x1ee5;c s&#x1ef1; c&#x1ed1;","children":[],"payload":{"tag":"li","lines":"234,235"}},{"content":"Ph&#x1ea7;n c&#xe2;u h&#x1ecf;i th&#x1b0;&#x1edd;ng g&#x1eb7;p","children":[],"payload":{"tag":"li","lines":"235,237"}}],"payload":{"tag":"li","lines":"230,237"}}],"payload":{"tag":"h3","lines":"190,191"}},{"content":"Ti&#xea;u chu&#x1ea9;n Coding","children":[{"content":"\n<p data-lines=\"238,239\"><strong>Ti&#xea;u chu&#x1ea9;n JavaScript/TypeScript</strong>:</p>","children":[{"content":"C&#x1ea5;u h&#xec;nh ESLint","children":[],"payload":{"tag":"li","lines":"239,240"}},{"content":"Quy t&#x1eaf;c &#x111;&#x1ecb;nh d&#x1ea1;ng Prettier","children":[],"payload":{"tag":"li","lines":"240,241"}},{"content":"Quy &#x1b0;&#x1edb;c &#x111;&#x1eb7;t t&#xea;n (camelCase cho variables, PascalCase cho classes)","children":[],"payload":{"tag":"li","lines":"241,242"}},{"content":"C&#x1ea5;u tr&#xfa;c v&#xe0; t&#x1ed5; ch&#x1ee9;c file","children":[],"payload":{"tag":"li","lines":"242,243"}},{"content":"Quy &#x1b0;&#x1edb;c Import/Export","children":[],"payload":{"tag":"li","lines":"243,244"}},{"content":"M&#x1eab;u x&#x1eed; l&#xfd; l&#x1ed7;i","children":[],"payload":{"tag":"li","lines":"244,245"}},{"content":"Best practices cho Async/await","children":[],"payload":{"tag":"li","lines":"245,247"}}],"payload":{"tag":"li","lines":"238,247"}},{"content":"\n<p data-lines=\"247,248\"><strong>Ti&#xea;u chu&#x1ea9;n Java</strong>:</p>","children":[{"content":"Quy t&#x1eaf;c &#x111;&#x1eb7;t t&#xea;n (camelCase cho variables, PascalCase cho classes)","children":[],"payload":{"tag":"li","lines":"248,249"}},{"content":"Quy &#x1b0;&#x1edb;c Import/Export","children":[],"payload":{"tag":"li","lines":"249,250"}},{"content":"C&#x1ea5;u tr&#xfa;c package","children":[],"payload":{"tag":"li","lines":"250,251"}},{"content":"M&#x1eab;u x&#x1eed; l&#xfd; l&#x1ed7;i","children":[],"payload":{"tag":"li","lines":"251,252"}},{"content":"Best practices cho Exception handling","children":[],"payload":{"tag":"li","lines":"252,253"}},{"content":"Quy t&#x1eaf;c Javadoc","children":[],"payload":{"tag":"li","lines":"253,255"}}],"payload":{"tag":"li","lines":"247,255"}},{"content":"\n<p data-lines=\"255,256\"><strong>Ti&#xea;u chu&#x1ea9;n thi&#x1ebf;t k&#x1ebf; Database</strong>:</p>","children":[{"content":"Quy &#x1b0;&#x1edb;c &#x111;&#x1eb7;t t&#xea;n Table (snake_case)","children":[],"payload":{"tag":"li","lines":"256,257"}},{"content":"Quy &#x1b0;&#x1edb;c Primary key","children":[],"payload":{"tag":"li","lines":"257,258"}},{"content":"&#x110;&#x1eb7;t t&#xea;n Foreign key","children":[],"payload":{"tag":"li","lines":"258,259"}},{"content":"M&#x1eab;u &#x111;&#x1eb7;t t&#xea;n Index","children":[],"payload":{"tag":"li","lines":"259,260"}},{"content":"Quy &#x1b0;&#x1edb;c &#x111;&#x1eb7;t t&#xea;n Migration","children":[],"payload":{"tag":"li","lines":"260,261"}},{"content":"Ti&#xea;u chu&#x1ea9;n ki&#x1ec3;u d&#x1eef; li&#x1ec7;u","children":[],"payload":{"tag":"li","lines":"261,262"}},{"content":"H&#x1b0;&#x1edb;ng d&#x1eab;n Normalization","children":[],"payload":{"tag":"li","lines":"262,264"}}],"payload":{"tag":"li","lines":"255,264"}},{"content":"\n<p data-lines=\"264,265\"><strong>Ti&#xea;u chu&#x1ea9;n thi&#x1ebf;t k&#x1ebf; API</strong>:</p>","children":[{"content":"Quy &#x1b0;&#x1edb;c &#x111;&#x1eb7;t t&#xea;n RESTful","children":[],"payload":{"tag":"li","lines":"265,266"}},{"content":"S&#x1eed; d&#x1ee5;ng HTTP status codes","children":[],"payload":{"tag":"li","lines":"266,267"}},{"content":"&#x110;&#x1ecb;nh d&#x1ea1;ng Request/Response","children":[],"payload":{"tag":"li","lines":"267,268"}},{"content":"Ti&#xea;u chu&#x1ea9;n Pagination","children":[],"payload":{"tag":"li","lines":"268,269"}},{"content":"&#x110;&#x1ecb;nh d&#x1ea1;ng Error response","children":[],"payload":{"tag":"li","lines":"269,270"}},{"content":"Chi&#x1ebf;n l&#x1b0;&#x1ee3;c Versioning","children":[],"payload":{"tag":"li","lines":"270,271"}},{"content":"Security headers","children":[],"payload":{"tag":"li","lines":"271,273"}}],"payload":{"tag":"li","lines":"264,273"}}],"payload":{"tag":"h3","lines":"237,238"}},{"content":"Git Workflow","children":[{"content":"\n<p data-lines=\"274,275\"><strong>Branch Naming Convention</strong>:</p>","children":[{"content":"<code>feature/ABC-123-short-description</code> cho feature branches","children":[],"payload":{"tag":"li","lines":"275,276"}},{"content":"<code>bugfix/ABC-456-bug-description</code> cho bug fixes","children":[],"payload":{"tag":"li","lines":"276,277"}},{"content":"<code>hotfix/ABC-789-critical-fix</code> cho hotfixes","children":[],"payload":{"tag":"li","lines":"277,278"}},{"content":"<code>release/v1.2.0</code> cho release branches","children":[],"payload":{"tag":"li","lines":"278,280"}}],"payload":{"tag":"li","lines":"274,280"}},{"content":"\n<p data-lines=\"280,281\"><strong>Commit Message Format</strong>:</p>\n<pre data-lines=\"281,290\"><code data-lines=\"281,290\">[ABC-<span class=\"hljs-number\">123</span>] <span class=\"hljs-keyword\">Short</span> <span class=\"hljs-keyword\">description</span> (&#x2264;<span class=\"hljs-number\">50</span> chars)\n\nDetailed explanation of changes <span class=\"hljs-keyword\">if</span> needed.\nCan <span class=\"hljs-keyword\">include</span> multiple lines.\n\n- List specific changes\n- Reference related issues\n</code></pre>","children":[],"payload":{"tag":"li","lines":"280,291"}},{"content":"\n<p data-lines=\"291,292\"><strong>Pull Request Template</strong>:</p>","children":[{"content":"M&#xf4; t&#x1ea3; c&#xe1;c thay &#x111;&#x1ed5;i","children":[],"payload":{"tag":"li","lines":"292,293"}},{"content":"Danh s&#xe1;ch ki&#x1ec3;m tra Testing","children":[],"payload":{"tag":"li","lines":"293,294"}},{"content":"C&#x1ead;p nh&#x1ead;t t&#xe0;i li&#x1ec7;u","children":[],"payload":{"tag":"li","lines":"294,295"}},{"content":"Th&#xf4;ng b&#xe1;o Breaking changes","children":[],"payload":{"tag":"li","lines":"295,296"}},{"content":"Quy t&#x1eaf;c ph&#xe2;n c&#xf4;ng Reviewer","children":[],"payload":{"tag":"li","lines":"296,298"}}],"payload":{"tag":"li","lines":"291,298"}}],"payload":{"tag":"h3","lines":"273,274"}}],"payload":{"tag":"h2","lines":"188,189"}},{"content":"&#x110;&#x1ea3;m b&#x1ea3;o Ch&#x1ea5;t l&#x1b0;&#x1ee3;ng","children":[{"content":"C&#x1ed5;ng Ki&#x1ec3;m so&#xe1;t Ch&#x1ea5;t l&#x1b0;&#x1ee3;ng Code","children":[{"content":"\n<p data-lines=\"301,302\"><strong>Y&#xea;u c&#x1ea7;u Coverage</strong>:</p>","children":[{"content":"Unit test coverage &#x2265; 80%","children":[],"payload":{"tag":"li","lines":"302,303"}},{"content":"Integration test coverage &#x2265; 70%","children":[],"payload":{"tag":"li","lines":"303,304"}},{"content":"E2E test coverage cho critical paths","children":[],"payload":{"tag":"li","lines":"304,306"}}],"payload":{"tag":"li","lines":"301,306"}},{"content":"\n<p data-lines=\"306,307\"><strong>Y&#xea;u c&#x1ea7;u B&#x1ea3;o m&#x1ead;t</strong>:</p>","children":[{"content":"Kh&#xf4;ng c&#xf3; l&#x1ed7; h&#x1ed5;ng b&#x1ea3;o m&#x1ead;t nghi&#xea;m tr&#x1ecd;ng","children":[],"payload":{"tag":"li","lines":"307,308"}},{"content":"Tu&#xe2;n th&#x1ee7; OWASP Top 10","children":[],"payload":{"tag":"li","lines":"308,309"}},{"content":"Qu&#xe9;t l&#x1ed7; h&#x1ed5;ng Dependency","children":[],"payload":{"tag":"li","lines":"309,310"}},{"content":"V&#x1b0;&#x1ee3;t qua Static code analysis","children":[],"payload":{"tag":"li","lines":"310,312"}}],"payload":{"tag":"li","lines":"306,312"}},{"content":"\n<p data-lines=\"312,313\"><strong>Y&#xea;u c&#x1ea7;u Hi&#x1ec7;u su&#x1ea5;t</strong>:</p>","children":[{"content":"Metrics &#x111;&#x1ed9; ph&#x1ee9;c t&#x1ea1;p code (Cyclomatic complexity &#x2264; 10)","children":[],"payload":{"tag":"li","lines":"313,314"}},{"content":"Benchmarks hi&#x1ec7;u su&#x1ea5;t","children":[],"payload":{"tag":"li","lines":"314,315"}},{"content":"Gi&#x1edb;i h&#x1ea1;n s&#x1eed; d&#x1ee5;ng b&#x1ed9; nh&#x1edb;","children":[],"payload":{"tag":"li","lines":"315,316"}},{"content":"Y&#xea;u c&#x1ea7;u th&#x1edd;i gian ph&#x1ea3;n h&#x1ed3;i","children":[],"payload":{"tag":"li","lines":"316,318"}}],"payload":{"tag":"li","lines":"312,318"}},{"content":"\n<p data-lines=\"318,319\"><strong>Metrics Ch&#x1ea5;t l&#x1b0;&#x1ee3;ng Code</strong>:</p>","children":[{"content":"V&#x1b0;&#x1ee3;t qua SonarQube quality gate","children":[],"payload":{"tag":"li","lines":"319,320"}},{"content":"Kh&#xf4;ng c&#xf3; code smells &#x111;&#x1b0;&#x1ee3;c &#x111;&#xe1;nh gi&#xe1; &quot;Major&quot; tr&#x1edf; l&#xea;n","children":[],"payload":{"tag":"li","lines":"320,321"}},{"content":"X&#x1ebf;p h&#x1ea1;ng kh&#x1ea3; n&#x103;ng b&#x1ea3;o tr&#xec; &#x2265; A","children":[],"payload":{"tag":"li","lines":"321,322"}},{"content":"X&#x1ebf;p h&#x1ea1;ng &#x111;&#x1ed9; tin c&#x1ead;y &#x2265; A","children":[],"payload":{"tag":"li","lines":"322,324"}}],"payload":{"tag":"li","lines":"318,324"}}],"payload":{"tag":"h3","lines":"300,301"}},{"content":"Quy tr&#xec;nh Review c&#xf3; h&#x1ed7; tr&#x1ee3; AI","children":[{"content":"\n<p data-lines=\"325,326\"><strong>Review Code T&#x1ef1; &#x111;&#x1ed9;ng</strong>:</p>","children":[{"content":"AI-powered code review v&#x1edb;i GitHub Copilot","children":[],"payload":{"tag":"li","lines":"326,327"}},{"content":"G&#x1ee3;i &#xfd; t&#x1ef1; &#x111;&#x1ed9;ng cho c&#x1ea3;i thi&#x1ec7;n code","children":[],"payload":{"tag":"li","lines":"327,328"}},{"content":"Nh&#x1ead;n di&#x1ec7;n pattern cho c&#xe1;c v&#x1ea5;n &#x111;&#x1ec1; th&#x1b0;&#x1edd;ng g&#x1eb7;p","children":[],"payload":{"tag":"li","lines":"328,329"}},{"content":"&#xc1;p d&#x1ee5;ng best practices","children":[],"payload":{"tag":"li","lines":"329,331"}}],"payload":{"tag":"li","lines":"325,331"}},{"content":"\n<p data-lines=\"331,332\"><strong>Qu&#xe9;t B&#x1ea3;o m&#x1ead;t</strong>:</p>","children":[{"content":"Ph&#xe1;t hi&#x1ec7;n l&#x1ed7; h&#x1ed5;ng b&#x1ea3;o m&#x1ead;t b&#x1eb1;ng AI","children":[],"payload":{"tag":"li","lines":"332,333"}},{"content":"Ki&#x1ec3;m tra dependency t&#x1ef1; &#x111;&#x1ed9;ng","children":[],"payload":{"tag":"li","lines":"333,334"}},{"content":"Ph&#xe2;n t&#xed;ch b&#x1ea3;o m&#x1ead;t c&#x1ea5;u h&#xec;nh","children":[],"payload":{"tag":"li","lines":"334,335"}},{"content":"Review pattern b&#x1ea3;o m&#x1ead;t code","children":[],"payload":{"tag":"li","lines":"335,337"}}],"payload":{"tag":"li","lines":"331,337"}},{"content":"\n<p data-lines=\"337,338\"><strong>Ph&#xe2;n t&#xed;ch Hi&#x1ec7;u su&#x1ea5;t</strong>:</p>","children":[{"content":"Ph&#xe1;t hi&#x1ec7;n bottleneck hi&#x1ec7;u su&#x1ea5;t b&#x1eb1;ng AI","children":[],"payload":{"tag":"li","lines":"338,339"}},{"content":"G&#x1ee3;i &#xfd; t&#x1ed1;i &#x1b0;u h&#xf3;a s&#x1eed; d&#x1ee5;ng t&#xe0;i nguy&#xea;n","children":[],"payload":{"tag":"li","lines":"339,340"}},{"content":"T&#x1ed1;i &#x1b0;u h&#xf3;a Database query","children":[],"payload":{"tag":"li","lines":"340,341"}},{"content":"G&#x1ee3;i &#xfd; chi&#x1ebf;n l&#x1b0;&#x1ee3;c Caching","children":[],"payload":{"tag":"li","lines":"341,343"}}],"payload":{"tag":"li","lines":"337,343"}},{"content":"\n<p data-lines=\"343,344\"><strong>Review T&#xe0;i li&#x1ec7;u</strong>:</p>","children":[{"content":"Ki&#x1ec3;m tra t&#xed;nh &#x111;&#x1ea7;y &#x111;&#x1ee7; t&#xe0;i li&#x1ec7;u t&#x1ef1; &#x111;&#x1ed9;ng","children":[],"payload":{"tag":"li","lines":"344,345"}},{"content":"G&#x1ee3;i &#xfd; t&#x1ea1;o t&#xe0;i li&#x1ec7;u b&#x1eb1;ng AI","children":[],"payload":{"tag":"li","lines":"345,346"}},{"content":"Ph&#xe2;n t&#xed;ch ch&#x1ea5;t l&#x1b0;&#x1ee3;ng comment code","children":[],"payload":{"tag":"li","lines":"346,347"}},{"content":"X&#xe1;c th&#x1ef1;c t&#xe0;i li&#x1ec7;u API","children":[],"payload":{"tag":"li","lines":"347,349"}}],"payload":{"tag":"li","lines":"343,349"}}],"payload":{"tag":"h3","lines":"324,325"}}],"payload":{"tag":"h2","lines":"298,299"}},{"content":"&#x110;&#xe0;o t&#x1ea1;o &amp; Onboarding","children":[{"content":"L&#x1ed9; tr&#xec;nh &#x110;&#xe0;o t&#x1ea1;o theo Vai tr&#xf2;","children":[{"content":"Business Analyst (BA)","children":[{"content":"\n<p data-lines=\"354,355\"><strong>Th&#xe0;nh th&#x1ea1;o GoogleNoteBookLM</strong>:</p>","children":[{"content":"Best practices t&#x1ed5; ch&#x1ee9;c t&#xe0;i li&#x1ec7;u","children":[],"payload":{"tag":"li","lines":"355,356"}},{"content":"K&#x1ef9; thu&#x1ead;t t&#x1ed1;i &#x1b0;u h&#xf3;a query","children":[],"payload":{"tag":"li","lines":"356,357"}},{"content":"T&#x1ed5;ng h&#x1ee3;p th&#xf4;ng tin t&#x1eeb; nhi&#x1ec1;u ngu&#x1ed3;n","children":[],"payload":{"tag":"li","lines":"357,358"}},{"content":"Phi&#xea;n &#xe2;m v&#xe0; ph&#xe2;n t&#xed;ch audio","children":[],"payload":{"tag":"li","lines":"358,360"}}],"payload":{"tag":"li","lines":"354,360"}},{"content":"\n<p data-lines=\"360,361\"><strong>S&#x1eed; d&#x1ee5;ng Custom GPT</strong>:</p>","children":[{"content":"K&#x1ef9; thu&#x1ead;t t&#x1ea1;o SRS","children":[],"payload":{"tag":"li","lines":"361,362"}},{"content":"Prompts l&#xe0;m r&#xf5; requirement","children":[],"payload":{"tag":"li","lines":"362,363"}},{"content":"Ph&#xe2;n t&#xed;ch ph&#x1ecf;ng v&#x1ea5;n stakeholder","children":[],"payload":{"tag":"li","lines":"363,364"}},{"content":"T&#x1ea1;o user story","children":[],"payload":{"tag":"li","lines":"364,366"}}],"payload":{"tag":"li","lines":"360,366"}}],"payload":{"tag":"h4","lines":"353,354"}},{"content":"Developer","children":[{"content":"\n<p data-lines=\"367,368\"><strong>Cursor IDE</strong>:</p>","children":[{"content":"Workflow coding h&#x1ed7; tr&#x1ee3; AI","children":[],"payload":{"tag":"li","lines":"368,369"}},{"content":"Code completion theo ng&#x1eef; c&#x1ea3;nh","children":[],"payload":{"tag":"li","lines":"369,370"}},{"content":"Refactoring v&#x1edb;i h&#x1ed7; tr&#x1ee3; AI","children":[],"payload":{"tag":"li","lines":"370,371"}},{"content":"Debug v&#x1edb;i AI insights","children":[],"payload":{"tag":"li","lines":"371,373"}}],"payload":{"tag":"li","lines":"367,373"}},{"content":"\n<p data-lines=\"373,374\"><strong>GitHub Copilot</strong>:</p>","children":[{"content":"Best practices t&#x1ea1;o code","children":[],"payload":{"tag":"li","lines":"374,375"}},{"content":"T&#x1ea1;o test case","children":[],"payload":{"tag":"li","lines":"375,376"}},{"content":"T&#x1ea1;o t&#xe0;i li&#x1ec7;u","children":[],"payload":{"tag":"li","lines":"376,377"}},{"content":"H&#x1ed7; tr&#x1ee3; code review","children":[],"payload":{"tag":"li","lines":"377,379"}}],"payload":{"tag":"li","lines":"373,379"}},{"content":"\n<p data-lines=\"379,380\"><strong>Augment</strong>:</p>","children":[{"content":"Hi&#x1ec3;u codebase","children":[],"payload":{"tag":"li","lines":"380,381"}},{"content":"Hi&#x1ec7;n &#x111;&#x1ea1;i h&#xf3;a legacy code","children":[],"payload":{"tag":"li","lines":"381,382"}},{"content":"Ph&#xe2;n t&#xed;ch ki&#x1ebf;n tr&#xfa;c","children":[],"payload":{"tag":"li","lines":"382,383"}},{"content":"T&#x1ed1;i &#x1b0;u h&#xf3;a hi&#x1ec7;u su&#x1ea5;t","children":[],"payload":{"tag":"li","lines":"383,385"}}],"payload":{"tag":"li","lines":"379,385"}}],"payload":{"tag":"h4","lines":"366,367"}},{"content":"Tester","children":[{"content":"<strong>T&#x1ea1;o Test v&#x1edb;i h&#x1ed7; tr&#x1ee3; AI</strong>:","children":[{"content":"T&#x1ea1;o test case t&#x1ef1; &#x111;&#x1ed9;ng","children":[],"payload":{"tag":"li","lines":"387,388"}},{"content":"T&#x1ea1;o test data","children":[],"payload":{"tag":"li","lines":"388,389"}},{"content":"Nh&#x1ead;n di&#x1ec7;n edge case","children":[],"payload":{"tag":"li","lines":"389,390"}},{"content":"T&#x1ed1;i &#x1b0;u h&#xf3;a regression test","children":[],"payload":{"tag":"li","lines":"390,392"}}],"payload":{"tag":"li","lines":"386,392"}}],"payload":{"tag":"h4","lines":"385,386"}},{"content":"DevOps Engineer","children":[{"content":"<strong>Infrastructure h&#x1ed7; tr&#x1ee3; AI</strong>:","children":[{"content":"T&#x1ed1;i &#x1b0;u h&#xf3;a deployment t&#x1ef1; &#x111;&#x1ed9;ng","children":[],"payload":{"tag":"li","lines":"394,395"}},{"content":"Thi&#x1ebf;t l&#x1ead;p monitoring v&#x1edb;i AI insights","children":[],"payload":{"tag":"li","lines":"395,396"}},{"content":"&#x110;i&#x1ec1;u ch&#x1ec9;nh hi&#x1ec7;u su&#x1ea5;t","children":[],"payload":{"tag":"li","lines":"396,397"}},{"content":"C&#x1ea5;u h&#xec;nh b&#x1ea3;o m&#x1ead;t","children":[],"payload":{"tag":"li","lines":"397,399"}}],"payload":{"tag":"li","lines":"393,399"}}],"payload":{"tag":"h4","lines":"392,393"}}],"payload":{"tag":"h3","lines":"351,352"}},{"content":"Quy tr&#xec;nh Ch&#x1ee9;ng ch&#x1ec9;","children":[{"content":"\n<p data-lines=\"400,401\"><strong>B&#xe0;i t&#x1ead;p Th&#x1ef1;c h&#xe0;nh</strong>:</p>","children":[{"content":"Assignments th&#x1ef1;c t&#x1ebf; cho m&#x1ed7;i AI tool","children":[],"payload":{"tag":"li","lines":"401,402"}},{"content":"M&#xf4; ph&#x1ecf;ng d&#x1ef1; &#xe1;n th&#x1ef1;c t&#x1ebf;","children":[],"payload":{"tag":"li","lines":"402,403"}},{"content":"Phi&#xea;n review &#x111;&#x1ed3;ng &#x111;&#x1eb3;ng","children":[],"payload":{"tag":"li","lines":"403,404"}},{"content":"Thuy&#x1ebf;t tr&#xec;nh chia s&#x1ebb; ki&#x1ebf;n th&#x1ee9;c","children":[],"payload":{"tag":"li","lines":"404,406"}}],"payload":{"tag":"li","lines":"400,406"}},{"content":"\n<p data-lines=\"406,407\"><strong>Ti&#xea;u ch&#xed; &#x110;&#xe1;nh gi&#xe1;</strong>:</p>","children":[{"content":"Th&#x1ec3; hi&#x1ec7;n th&#xe0;nh th&#x1ea1;o c&#xf4;ng c&#x1ee5;","children":[],"payload":{"tag":"li","lines":"407,408"}},{"content":"&#xc1;p d&#x1ee5;ng best practices","children":[],"payload":{"tag":"li","lines":"408,409"}},{"content":"Kh&#x1ea3; n&#x103;ng gi&#x1ea3;i quy&#x1ebf;t v&#x1ea5;n &#x111;&#x1ec1;","children":[],"payload":{"tag":"li","lines":"409,410"}},{"content":"X&#xe1;c th&#x1ef1;c nh&#x1ead;n th&#x1ee9;c b&#x1ea3;o m&#x1ead;t","children":[],"payload":{"tag":"li","lines":"410,412"}}],"payload":{"tag":"li","lines":"406,412"}}],"payload":{"tag":"h3","lines":"399,400"}}],"payload":{"tag":"h2","lines":"349,350"}},{"content":"Metrics &amp; KPIs","children":[{"content":"Metrics N&#x103;ng su&#x1ea5;t","children":[{"content":"\n<p data-lines=\"415,416\"><strong>T&#x1ed1;c &#x111;&#x1ed9; Ph&#xe1;t tri&#x1ec3;n</strong>:</p>","children":[{"content":"Gi&#x1ea3;m th&#x1edd;i gian cho m&#x1ed7;i giai &#x111;o&#x1ea1;n ph&#xe1;t tri&#x1ec3;n","children":[],"payload":{"tag":"li","lines":"416,417"}},{"content":"T&#x103;ng s&#x1ed1; d&#xf2;ng code m&#x1ed7;i gi&#x1edd;","children":[],"payload":{"tag":"li","lines":"417,418"}},{"content":"T&#x1ed1;c &#x111;&#x1ed9; delivery t&#xed;nh n&#x103;ng","children":[],"payload":{"tag":"li","lines":"418,419"}},{"content":"Th&#x1edd;i gian x&#x1eed; l&#xfd; bug","children":[],"payload":{"tag":"li","lines":"419,421"}}],"payload":{"tag":"li","lines":"415,421"}},{"content":"\n<p data-lines=\"421,422\"><strong>C&#x1ea3;i thi&#x1ec7;n Ch&#x1ea5;t l&#x1b0;&#x1ee3;ng</strong>:</p>","children":[{"content":"T&#x1ef7; l&#x1ec7; gi&#x1ea3;m bug","children":[],"payload":{"tag":"li","lines":"422,423"}},{"content":"Hi&#x1ec7;u qu&#x1ea3; code review","children":[],"payload":{"tag":"li","lines":"423,424"}},{"content":"C&#x1ea3;i thi&#x1ec7;n test coverage","children":[],"payload":{"tag":"li","lines":"424,425"}},{"content":"&#x110;i&#x1ec3;m ho&#xe0;n thi&#x1ec7;n t&#xe0;i li&#x1ec7;u","children":[],"payload":{"tag":"li","lines":"425,427"}}],"payload":{"tag":"li","lines":"421,427"}},{"content":"\n<p data-lines=\"427,428\"><strong>H&#x1ee3;p t&#xe1;c Nh&#xf3;m</strong>:</p>","children":[{"content":"T&#x1ea7;n su&#x1ea5;t chia s&#x1ebb; ki&#x1ebf;n th&#x1ee9;c","children":[],"payload":{"tag":"li","lines":"428,429"}},{"content":"Ch&#x1ec9; s&#x1ed1; h&#x1ee3;p t&#xe1;c cross-team","children":[],"payload":{"tag":"li","lines":"429,430"}},{"content":"Metrics t&#xe1;i s&#x1eed; d&#x1ee5;ng code","children":[],"payload":{"tag":"li","lines":"430,431"}},{"content":"Gi&#x1ea3;m th&#x1edd;i gian onboarding","children":[],"payload":{"tag":"li","lines":"431,433"}}],"payload":{"tag":"li","lines":"427,433"}}],"payload":{"tag":"h3","lines":"414,415"}},{"content":"ROI c&#x1ee7;a AI Tools","children":[{"content":"\n<p data-lines=\"434,435\"><strong>Ph&#xe2;n t&#xed;ch Chi ph&#xed;</strong>:</p>","children":[{"content":"Chi ph&#xed; subscription tools vs t&#x103;ng n&#x103;ng su&#x1ea5;t","children":[],"payload":{"tag":"li","lines":"435,436"}},{"content":"&#x110;&#x1ea7;u t&#x1b0; &#x111;&#xe0;o t&#x1ea1;o vs c&#x1ea3;i thi&#x1ec7;n hi&#x1ec7;u qu&#x1ea3;","children":[],"payload":{"tag":"li","lines":"436,437"}},{"content":"T&#x1ed1;i &#x1b0;u h&#xf3;a chi ph&#xed; infrastructure","children":[],"payload":{"tag":"li","lines":"437,438"}},{"content":"Hi&#x1ec7;u qu&#x1ea3; ph&#xe2;n b&#x1ed5; t&#xe0;i nguy&#xea;n","children":[],"payload":{"tag":"li","lines":"438,440"}}],"payload":{"tag":"li","lines":"434,440"}},{"content":"\n<p data-lines=\"440,441\"><strong>Metrics Time-to-Market</strong>:</p>","children":[{"content":"C&#x1ea3;i thi&#x1ec7;n timeline delivery d&#x1ef1; &#xe1;n","children":[],"payload":{"tag":"li","lines":"441,442"}},{"content":"T&#x103;ng t&#x1ed1;c ph&#xe1;t tri&#x1ec3;n t&#xed;nh n&#x103;ng","children":[],"payload":{"tag":"li","lines":"442,443"}},{"content":"T&#x103;ng t&#x1ed1;c &#x111;&#x1ed9; gi&#x1ea3;i quy&#x1ebf;t bug","children":[],"payload":{"tag":"li","lines":"443,444"}},{"content":"Gi&#x1ea3;m th&#x1edd;i gian t&#x1ea1;o t&#xe0;i li&#x1ec7;u","children":[],"payload":{"tag":"li","lines":"444,446"}}],"payload":{"tag":"li","lines":"440,446"}},{"content":"\n<p data-lines=\"446,447\"><strong>Metrics Ch&#x1ea5;t l&#x1b0;&#x1ee3;ng</strong>:</p>","children":[{"content":"C&#x1ea3;i thi&#x1ec7;n s&#x1ef1; h&#xe0;i l&#xf2;ng kh&#xe1;ch h&#xe0;ng","children":[],"payload":{"tag":"li","lines":"447,448"}},{"content":"Gi&#x1ea3;m incident production","children":[],"payload":{"tag":"li","lines":"448,449"}},{"content":"Gi&#x1ea3;m l&#x1ed7; h&#x1ed5;ng b&#x1ea3;o m&#x1ead;t","children":[],"payload":{"tag":"li","lines":"449,450"}},{"content":"T&#x103;ng hi&#x1ec7;u su&#x1ea5;t t&#x1ed1;i &#x1b0;u h&#xf3;a","children":[],"payload":{"tag":"li","lines":"450,452"}}],"payload":{"tag":"li","lines":"446,452"}},{"content":"\n<p data-lines=\"452,453\"><strong>S&#x1ef1; h&#xe0;i l&#xf2;ng c&#x1ee7;a Nh&#xf3;m</strong>:</p>","children":[{"content":"&#x110;i&#x1ec3;m tr&#x1ea3;i nghi&#x1ec7;m developer","children":[],"payload":{"tag":"li","lines":"453,454"}},{"content":"T&#x1ef7; l&#x1ec7; &#xe1;p d&#x1ee5;ng tool","children":[],"payload":{"tag":"li","lines":"454,455"}},{"content":"S&#x1ef1; h&#xe0;i l&#xf2;ng v&#x1edb;i learning curve","children":[],"payload":{"tag":"li","lines":"455,456"}},{"content":"C&#x1ea3;i thi&#x1ec7;n work-life balance","children":[],"payload":{"tag":"li","lines":"456,458"}}],"payload":{"tag":"li","lines":"452,458"}}],"payload":{"tag":"h3","lines":"433,434"}},{"content":"Framework &#x110;o l&#x1b0;&#x1edd;ng","children":[{"content":"\n<p data-lines=\"459,460\"><strong>Review H&#xe0;ng th&#xe1;ng</strong>:</p>","children":[{"content":"Ph&#xe2;n t&#xed;ch xu h&#x1b0;&#x1edb;ng n&#x103;ng su&#x1ea5;t","children":[],"payload":{"tag":"li","lines":"460,461"}},{"content":"Th&#x1ed1;ng k&#xea; s&#x1eed; d&#x1ee5;ng tool","children":[],"payload":{"tag":"li","lines":"461,462"}},{"content":"C&#x1ead;p nh&#x1ead;t t&#xed;nh to&#xe1;n ROI","children":[],"payload":{"tag":"li","lines":"462,463"}},{"content":"Thu th&#x1ead;p feedback nh&#xf3;m","children":[],"payload":{"tag":"li","lines":"463,465"}}],"payload":{"tag":"li","lines":"459,465"}},{"content":"\n<p data-lines=\"465,466\"><strong>&#x110;&#xe1;nh gi&#xe1; H&#xe0;ng qu&#xfd;</strong>:</p>","children":[{"content":"Alignment m&#x1ee5;c ti&#xea;u chi&#x1ebf;n l&#x1b0;&#x1ee3;c","children":[],"payload":{"tag":"li","lines":"466,467"}},{"content":"C&#x1a1; h&#x1ed9;i t&#x1ed1;i &#x1b0;u h&#xf3;a quy tr&#xec;nh","children":[],"payload":{"tag":"li","lines":"467,468"}},{"content":"&#x110;&#xe1;nh gi&#xe1; hi&#x1ec7;u qu&#x1ea3; tool","children":[],"payload":{"tag":"li","lines":"468,469"}},{"content":"Assessment nhu c&#x1ea7;u &#x111;&#xe0;o t&#x1ea1;o","children":[],"payload":{"tag":"li","lines":"469,471"}}],"payload":{"tag":"li","lines":"465,471"}},{"content":"\n<p data-lines=\"471,472\"><strong>&#x110;&#xe1;nh gi&#xe1; H&#xe0;ng n&#x103;m</strong>:</p>","children":[{"content":"T&#xe1;c &#x111;&#x1ed9;ng transformation t&#x1ed5;ng th&#x1ec3;","children":[],"payload":{"tag":"li","lines":"472,473"}},{"content":"Ph&#xe2;n t&#xed;ch ROI d&#xe0;i h&#x1ea1;n","children":[],"payload":{"tag":"li","lines":"473,474"}},{"content":"&#x110;i&#x1ec1;u ch&#x1ec9;nh k&#x1ebf; ho&#x1ea1;ch chi&#x1ebf;n l&#x1b0;&#x1ee3;c","children":[],"payload":{"tag":"li","lines":"474,475"}},{"content":"T&#xe0;i li&#x1ec7;u h&#xf3;a success stories","children":[],"payload":{"tag":"li","lines":"475,476"}}],"payload":{"tag":"li","lines":"471,476"}}],"payload":{"tag":"h3","lines":"458,459"}}],"payload":{"tag":"h2","lines":"412,413"}}],"payload":{"tag":"h1","lines":"0,1"}},{})</script>
</body>
</html>
