# Training về AI

Mục đích chính của tài liệu là lên được một bộ giải pháp ứng dụng các công cụ AI vào quy trình phát triển phần mềm, nhằm nâng cao năng suất làm việc của các thành viên trong nhóm phát triển phần mềm.

<PERSON><PERSON>u cầu cần đạt tới việc hỗ trợ tối đa có thể cho các thành viên ở tất cả các công đoạn.
Ngoài ra cũng phải quy định các convention phù hợp với từng công cụ, để đảm bảo tính bảo mật (nhân viên khó sao chép quy trình) và các bước có thể integrate với nhau (output của bước này có thể làm input của bước sau dưới định dạng file / api / mcp...)

### Bảo mật quy trình phát triển phần mềm với AI tools
- **Mục tiêu**: Không để cho nhân viên đánh cắp được toàn bộ quy trình mang ra ngoài công ty.
- **Giải pháp**: 
  - Đào tạo để các thành viên chỉ nắm được một phần của toàn bộ quy trình, ứng với công việc của họ.
  - Với các custom agent thì bảo mật system prompt.
  - Một số trường hợp có thể tự phát triển agent riêng và tự phát triển UI interface để giao tiếp. 

## Quy trình phát triển phần mềm phổ biến hiện nay

- (1) Tổng hợp requirement từ khách hàng
  - Role: BA
  - Công cụ: 
    - GoogleNoteBookLM
    - Confluence
    - Notion
  - Input: 
    - Tài liệu khách hàng gửi
    - Hệ thống tham khảo (nếu có, dạng ảnh chụp màn hình)
    - Nội dung các cuộc họp
  - Output:
    - File tài liệu requirement tổng hợp từ tất cả các nguồn (hoặc có thể tra cứu lại về sau)
- (2) Tạo tài liệu SRS (Software Requirement Specification)
  - Role: BA
  - Công cụ: 
    - GoogleNoteBookLM - để tra cứu
    - Custom GPT (ChatGPT Plus) - để biên tập
  - Input:
    - Tài liệu requirement ở bước (1)
  - Output:
    - Tài liệu SRS, sẵn sàng cho dev break task,  thiết kế kiến trúc hệ thống, thiết kế giao diện và implement chức năng
- (3) Thiết kế kiến trúc hệ thống. (Architecture Design)
  - Role: System Architect
  - Công cụ:
    - Custom GTP (ChatGPT)
    - Github Copilot
    - Cursor
    - Augment
  - Input:
    - Tài liệu SRS ở bước (2)
    - Tài liệu requirement ở bước (1): ví dụ các quy định về kỹ thuật trong dự án
    - Mẫu kiến trúc của dự án tương tự trước đó để tham khảo
    - Mẫu project dự án trước đó đã có để tham khảo
    - Mẫu CI/CD dự án trước đó đã có để tham khảo
  - Output:
    - Tài liệu thiết kế kiến trúc theo chuẩn thiết kế C4 hoặc UML
    - Tạo project structure ban đầu cho dự án
    - Generate CI/CD cho dự án
    - Tài liệu coding convention
    - Tài liệu guideline tạo test
- (4) Thiết kế giao diện / prototype
  - Role: Designer
  - Công cụ:
    - Figma
    - V0
    - UX pilot
  - Input:
    - Requirement của khách hàng ở bước (1) (yêu cầu về giao diện)
    - Tài liệu SRS ở bước (2)
    - Tài liệu architecture design ở bước (3)
  - Output: 
    - Giao diện các page theo SRS
- (5) Thiết kế chi tiết phần implement các chức năng. (Detailed Design)
  - Role: System Architect, Developer
  - Công cụ:
    - Custom GTP (ChatGPT)
    - Grok workspace
    - Custom Gem (Gemini).
    - Github Copilot
    - Cursor
    - Augment
  - Input:
    - Tài liệu SRS ở bước (2)
    - Tài liệu architecture design ở bước (3)
    - Tài liệu thiết kế giao diện ở bước (4)
  - Output:
    - Tài liệu thiết kế implement chi tiết các tính năng, bao gồm:
      - Các API cần thiết
      - Các module cần thiết
      - Các class cần thiết
      - Các database cần thiết
    - Estimate thời gian implement các tính năng
    - Tạo task (Jira hoặc Gitlab issues hoặc TaskMaster)
- (6) Phân task, lên plan, quản lý plan
  - Role: PM / TeamLeader
  - Công cụ:
    - Jira
    - TaskMaster
    - Gitlab issues
  - Input:
    - Tài liệu thiết kế chi tiết phần implement các chức năng ở bước (5)
    - Tài liệu thiết kế kiến trúc hệ thống ở bước (3)
    - Tài liệu thiết kế giao diện ở bước (4)
  - Output:
    - Phân chia task cho các developer
    - Lên kế hoạch thực hiện các task
    - Quản lý tiến độ thực hiện các task
    - Theo dõi bug và issue trong quá trình phát triển
- (7) Viết code
  - Role: Developer
  - Công cụ (Lấy các task / bug thông qua MCP):
    - Github Copilot
    - Cursor
  - Input:
    - Task / Issue ticket ở bước (6)
    - Coding convention ở bước (2)
  - Output:
    - Source code
- (8) Viết test case
  - UT
    - Role: Developer
    - Công cụ:
      - Github Copilot
      - Cursor
      - Augment 
  - E2E test
    - Role: Tester / Developer
    - Công cụ:
      - Github Copilot
      - Cursor
      - Augment
  - Input:
    - Source code ở bước (7)
    - Tài liệu thiết kế chi tiết phần implement các chức năng ở bước (5)
    - Tài liệu guideline tạo test ở bước (3)
  - Output:
    - Script test case
- (9) Review
  - Role: DevOps
  - Công cụ: 
    - CI/CD + n8n workflow + AI Agent + LLM API
  - Input:
    - Pull request
  - Output: 
    - Kết quả review code
    - Kết quả chạy UT
    - Kết quả chạy E2E test
- (10) Viết tài liệu hướng dẫn sử dụng
  - Role: BA / Developer
  - Công cụ:
    - Custom GTP (ChatGPT)
    - Grok workspace
    - Custom Gem (Gemini). 
    - Github Copilot
    - Cursor
    - Augment
  - Input:
    - Source code ở bước (7)
    - Tài liệu thiết kế chi tiết phần implement các chức năng ở bước (5)
    - Tài liệu SRS ở bước (2)
    - Tài liệu thiết kế giao diện ở bước (4)
  - Output:
    - Tài liệu hướng dẫn sử dụng hệ thống, bao gồm:
      - Hướng dẫn cài đặt
      - Hướng dẫn sử dụng các chức năng
- (11) Deploy hệ thống / CI/CD (Gitlab / Docker / Kubernetes)
  - Role: DevOps
  - Công cụ:
    - Github Copilot
    - Cursor
    - Augment
  - Input:
    - Tài liệu thiết kế kiến trúc hệ thống ở bước (3)
    - Tài liệu thiết kế chi tiết phần implement các chức năng ở bước (5)
    - Source code ở bước (7)
  - Output:
    - Hệ thống được deploy lên môi trường staging / production
    - CI/CD pipeline được thiết lập và hoạt động
    - Tài liệu hướng dẫn deploy hệ thống
- (12) Monitoring / Tracing / Logging
  - Role: DevOps
  - Công cụ:
    - Signoz
    - Jeager
    - ELK stack
    - Prometheus & Grafana
  - Input:
    - Hệ thống đã được deploy ở bước (11)
  - Output:
    - Hệ thống được giám sát, theo dõi và ghi log
    - Tài liệu hướng dẫn giám sát hệ thống

## Templates & Standards

### Tài liệu Template
- **SRS Template**: Template chuẩn cho Software Requirement Specification với các phần:
  - Tóm tắt điều hành
  - Yêu cầu chức năng
  - Yêu cầu phi chức năng
  - Tổng quan kiến trúc hệ thống
  - Yêu cầu giao diện người dùng
  - Yêu cầu Database
  - Yêu cầu tích hợp
  - Yêu cầu bảo mật
  - Yêu cầu hiệu suất
  - Tiêu chí chấp nhận

- **Architecture Design Template** (C4 Model):
  - Context Diagram
  - Container Diagram
  - Component Diagram
  - Code Diagram
  - Deployment Diagram
  - Quyết định Technology Stack
  - Phân tích thuộc tính chất lượng

- **API Design Template** (OpenAPI/Swagger):
  - Tổng quan và mục đích API
  - Authentication & Authorization
  - Đặc tả Endpoint
  - Ví dụ Request/Response
  - Xử lý lỗi
  - Rate Limiting
  - Chiến lược versioning

- **Test Case Template**:
  - Phạm vi và mục tiêu kiểm thử
  - Thiết lập môi trường test
  - Cấu trúc Unit Test
  - Test Case tích hợp
  - Kịch bản E2E Test
  - Test Case hiệu suất
  - Test Case bảo mật

- **Tài liệu hướng dẫn người dùng Template**:
  - Hướng dẫn cài đặt
  - Sổ tay người dùng
  - Tài liệu API
  - Hướng dẫn khắc phục sự cố
  - Phần câu hỏi thường gặp

### Tiêu chuẩn Coding
- **Tiêu chuẩn JavaScript/TypeScript**:
  - Cấu hình ESLint
  - Quy tắc định dạng Prettier
  - Quy ước đặt tên (camelCase cho variables, PascalCase cho classes)
  - Cấu trúc và tổ chức file
  - Quy ước Import/Export
  - Mẫu xử lý lỗi
  - Best practices cho Async/await

- **Tiêu chuẩn Java**:
  - Quy tắc đặt tên (camelCase cho variables, PascalCase cho classes)
  - Quy ước Import/Export
  - Cấu trúc package
  - Mẫu xử lý lỗi
  - Best practices cho Exception handling
  - Quy tắc Javadoc
  
- **Tiêu chuẩn thiết kế Database**:
  - Quy ước đặt tên Table (snake_case)
  - Quy ước Primary key
  - Đặt tên Foreign key
  - Mẫu đặt tên Index
  - Quy ước đặt tên Migration
  - Tiêu chuẩn kiểu dữ liệu
  - Hướng dẫn Normalization

- **Tiêu chuẩn thiết kế API**:
  - Quy ước đặt tên RESTful
  - Sử dụng HTTP status codes
  - Định dạng Request/Response
  - Tiêu chuẩn Pagination
  - Định dạng Error response
  - Chiến lược Versioning
  - Security headers

### Git Workflow
- **Branch Naming Convention**:
  - `feature/ABC-123-short-description` cho feature branches
  - `bugfix/ABC-456-bug-description` cho bug fixes
  - `hotfix/ABC-789-critical-fix` cho hotfixes
  - `release/v1.2.0` cho release branches

- **Commit Message Format**:
  ```
  [ABC-123] Short description (≤50 chars)
  
  Detailed explanation of changes if needed.
  Can include multiple lines.
  
  - List specific changes
  - Reference related issues
  ```

- **Pull Request Template**:
  - Mô tả các thay đổi
  - Danh sách kiểm tra Testing
  - Cập nhật tài liệu
  - Thông báo Breaking changes
  - Quy tắc phân công Reviewer

## Đảm bảo Chất lượng

### Cổng Kiểm soát Chất lượng Code
- **Yêu cầu Coverage**:
  - Unit test coverage ≥ 80%
  - Integration test coverage ≥ 70%
  - E2E test coverage cho critical paths

- **Yêu cầu Bảo mật**:
  - Không có lỗ hổng bảo mật nghiêm trọng
  - Tuân thủ OWASP Top 10
  - Quét lỗ hổng Dependency
  - Vượt qua Static code analysis

- **Yêu cầu Hiệu suất**:
  - Metrics độ phức tạp code (Cyclomatic complexity ≤ 10)
  - Benchmarks hiệu suất
  - Giới hạn sử dụng bộ nhớ
  - Yêu cầu thời gian phản hồi

- **Metrics Chất lượng Code**:
  - Vượt qua SonarQube quality gate
  - Không có code smells được đánh giá "Major" trở lên
  - Xếp hạng khả năng bảo trì ≥ A
  - Xếp hạng độ tin cậy ≥ A

### Quy trình Review có hỗ trợ AI
- **Review Code Tự động**:
  - AI-powered code review với GitHub Copilot
  - Gợi ý tự động cho cải thiện code
  - Nhận diện pattern cho các vấn đề thường gặp
  - Áp dụng best practices

- **Quét Bảo mật**:
  - Phát hiện lỗ hổng bảo mật bằng AI
  - Kiểm tra dependency tự động
  - Phân tích bảo mật cấu hình
  - Review pattern bảo mật code

- **Phân tích Hiệu suất**:
  - Phát hiện bottleneck hiệu suất bằng AI
  - Gợi ý tối ưu hóa sử dụng tài nguyên
  - Tối ưu hóa Database query
  - Gợi ý chiến lược Caching

- **Review Tài liệu**:
  - Kiểm tra tính đầy đủ tài liệu tự động
  - Gợi ý tạo tài liệu bằng AI
  - Phân tích chất lượng comment code
  - Xác thực tài liệu API

## Đào tạo & Onboarding

### Lộ trình Đào tạo theo Vai trò

#### Business Analyst (BA)
- **Thành thạo GoogleNoteBookLM**:
  - Best practices tổ chức tài liệu
  - Kỹ thuật tối ưu hóa query
  - Tổng hợp thông tin từ nhiều nguồn
  - Phiên âm và phân tích audio

- **Sử dụng Custom GPT**:
  - Kỹ thuật tạo SRS
  - Prompts làm rõ requirement
  - Phân tích phỏng vấn stakeholder
  - Tạo user story

#### Developer
- **Cursor IDE**:
  - Workflow coding hỗ trợ AI
  - Code completion theo ngữ cảnh
  - Refactoring với hỗ trợ AI
  - Debug với AI insights

- **GitHub Copilot**:
  - Best practices tạo code
  - Tạo test case
  - Tạo tài liệu
  - Hỗ trợ code review

- **Augment**:
  - Hiểu codebase
  - Hiện đại hóa legacy code
  - Phân tích kiến trúc
  - Tối ưu hóa hiệu suất

#### Tester
- **Tạo Test với hỗ trợ AI**:
  - Tạo test case tự động
  - Tạo test data
  - Nhận diện edge case
  - Tối ưu hóa regression test

#### DevOps Engineer
- **Infrastructure hỗ trợ AI**:
  - Tối ưu hóa deployment tự động
  - Thiết lập monitoring với AI insights
  - Điều chỉnh hiệu suất
  - Cấu hình bảo mật

### Quy trình Chứng chỉ
- **Bài tập Thực hành**:
  - Assignments thực tế cho mỗi AI tool
  - Mô phỏng dự án thực tế
  - Phiên review đồng đẳng
  - Thuyết trình chia sẻ kiến thức

- **Tiêu chí Đánh giá**:
  - Thể hiện thành thạo công cụ
  - Áp dụng best practices
  - Khả năng giải quyết vấn đề
  - Xác thực nhận thức bảo mật

## Metrics & KPIs

### Metrics Năng suất
- **Tốc độ Phát triển**:
  - Giảm thời gian cho mỗi giai đoạn phát triển
  - Tăng số dòng code mỗi giờ
  - Tốc độ delivery tính năng
  - Thời gian xử lý bug

- **Cải thiện Chất lượng**:
  - Tỷ lệ giảm bug
  - Hiệu quả code review
  - Cải thiện test coverage
  - Điểm hoàn thiện tài liệu

- **Hợp tác Nhóm**:
  - Tần suất chia sẻ kiến thức
  - Chỉ số hợp tác cross-team
  - Metrics tái sử dụng code
  - Giảm thời gian onboarding

### ROI của AI Tools
- **Phân tích Chi phí**:
  - Chi phí subscription tools vs tăng năng suất
  - Đầu tư đào tạo vs cải thiện hiệu quả
  - Tối ưu hóa chi phí infrastructure
  - Hiệu quả phân bổ tài nguyên

- **Metrics Time-to-Market**:
  - Cải thiện timeline delivery dự án
  - Tăng tốc phát triển tính năng
  - Tăng tốc độ giải quyết bug
  - Giảm thời gian tạo tài liệu

- **Metrics Chất lượng**:
  - Cải thiện sự hài lòng khách hàng
  - Giảm incident production
  - Giảm lỗ hổng bảo mật
  - Tăng hiệu suất tối ưu hóa

- **Sự hài lòng của Nhóm**:
  - Điểm trải nghiệm developer
  - Tỷ lệ áp dụng tool
  - Sự hài lòng với learning curve
  - Cải thiện work-life balance

### Framework Đo lường
- **Review Hàng tháng**:
  - Phân tích xu hướng năng suất
  - Thống kê sử dụng tool
  - Cập nhật tính toán ROI
  - Thu thập feedback nhóm

- **Đánh giá Hàng quý**:
  - Alignment mục tiêu chiến lược
  - Cơ hội tối ưu hóa quy trình
  - Đánh giá hiệu quả tool
  - Assessment nhu cầu đào tạo

- **Đánh giá Hàng năm**:
  - Tác động transformation tổng thể
  - Phân tích ROI dài hạn
  - Điều chỉnh kế hoạch chiến lược
  - Tài liệu hóa success stories
