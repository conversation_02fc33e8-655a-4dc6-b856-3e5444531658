# Detailed Input/Output Standards for AI-Enhanced Software Development Workflow

This document provides a detailed specification of the inputs and outputs for each step in the proposed AI-enhanced software development workflow. The standards defined here aim to ensure seamless data flow between stages, leveraging standardized formats and aligning with the integration strategies previously outlined. Adherence to these standards is crucial for maximizing the effectiveness of AI tools and automation.

## Step 1: Requirement Gathering (Tổng hợp requirement)

*   **Role:** Business Analyst (BA)
*   **AI Tool:** Google NotebookLM
*   **Input:** The process begins with raw inputs from various sources. These include customer-provided documents (accepted in formats like PDF, DOCX, TXT), recordings of meetings (MP3, MP4, WAV), and visual references such as system screenshots (PNG, JPG). These diverse materials are uploaded directly into Google NotebookLM.
*   **Process:** The BA utilizes NotebookLM's capabilities to process and consolidate information from all uploaded sources. NotebookLM assists in summarizing text, transcribing audio, and allowing semantic search across the entire knowledge base.
*   **Output:** The primary output is the curated knowledge base within NotebookLM itself, serving as a dynamic reference. For handoff to the next step, the BA exports key findings, consolidated requirements, and relevant source snippets into a structured Markdown file, designated as `/home/<USER>/project_data/consolidated_requirements.md`. This file acts as the formal input for SRS creation.

## Step 2: SRS Creation (Tạo tài liệu SRS)

*   **Role:** Business Analyst (BA)
*   **AI Tools:** Custom GPT (via secured agent/interface), Google NotebookLM (for lookup)
*   **Input:** The primary input is the `/home/<USER>/project_data/consolidated_requirements.md` file generated in Step 1. The BA may also perform additional ad-hoc queries directly within NotebookLM for clarification during the writing process.
*   **Process:** The BA interacts with a custom GPT agent. This agent is specifically prompted (using secured, predefined system prompts) to draft sections of the Software Requirement Specification based on the provided Markdown input and the standard SRS template structure outlined in `overview.md`. The BA guides the generation process, refines the AI-generated text, and ensures all requirements are accurately captured.
*   **Output:** The final deliverable is a comprehensive SRS document, saved as `/home/<USER>/project_data/srs_document.md`. This Markdown file strictly adheres to the company's standard SRS template, ensuring consistency and completeness for subsequent stages.

## Step 3: Architecture Design (Thiết kế kiến trúc hệ thống)

*   **Role:** System Architect
*   **AI Tools:** Custom GPT, Github Copilot, Cursor, Augment
*   **Input:** The core input is the `/home/<USER>/project_data/srs_document.md`. Optionally, paths to relevant reference materials like previous architecture documents (`/home/<USER>/reference_materials/ref_arch.md`), example project structures (`/home/<USER>/reference_materials/ref_project.zip`), or existing CI/CD configurations (`/home/<USER>/reference_materials/ref_cicd.yaml`) can be provided as context.
*   **Process:** The System Architect uses the Custom GPT agent to brainstorm architectural patterns, suggest technology stacks, and outline C4/UML model structures based on the SRS. Within the Cursor IDE, Copilot and Augment assist in generating boilerplate code for the initial project structure, drafting CI/CD pipeline configurations (YAML), and creating initial drafts of coding conventions and testing guidelines by referencing the SRS and architectural decisions.
*   **Output:** This step produces several key artifacts:
    *   A detailed architecture document `/home/<USER>/project_data/architecture_design.md`, written in Markdown, incorporating C4/UML descriptions (potentially using embedded PlantUML/Mermaid syntax for diagram generation).
    *   The initial project directory structure at `/home/<USER>/project_code/project_structure/`.
    *   The CI/CD pipeline definition file `/home/<USER>/project_code/cicd_pipeline.yaml`.
    *   A coding standards document `/home/<USER>/project_docs/coding_convention.md`.
    *   A testing guidelines document `/home/<USER>/project_docs/test_guideline.md`.

## Step 4: UI/Prototype Design (Thiết kế giao diện / prototype)

*   **Role:** Designer
*   **AI Tools:** Figma (with potential AI plugins), V0, UX pilot
*   **Input:** Relevant sections from `/home/<USER>/project_data/srs_document.md` focusing on user interface requirements and user stories. Textual descriptions or rough sketches of desired interfaces may also serve as input.
*   **Process:** The designer utilizes Figma as the primary design environment. AI tools like V0 can be fed textual descriptions or wireframes derived from the SRS to generate initial HTML/CSS/React code suggestions or interactive prototypes. UX pilot might analyze user stories from the SRS to propose optimal user flows or component layouts, which the designer then refines in Figma.
*   **Output:** The main output is a shareable Figma project link (e.g., `https://figma.com/file/...`). If V0 or similar tools are used, generated code snippets or components might be exported to `/home/<USER>/project_data/ui_components/` for reference by developers.

## Step 5: Detailed Design (Thiết kế chi tiết implement)

*   **Role:** System Architect, Developer
*   **AI Tools:** Custom GPT, Grok workspace, Custom Gem (Gemini), Github Copilot, Cursor, Augment
*   **Input:** The `/home/<USER>/project_data/srs_document.md`, `/home/<USER>/project_data/architecture_design.md`, and the Figma link (or exported UI specifications, perhaps summarized in `/home/<USER>/project_data/ui_specs.md`).
*   **Process:** Architects and developers use custom AI agents (GPT/Gemini wrappers) or tools like Grok, providing the input documents as context. These tools assist in generating detailed specifications, including API endpoint definitions (prompted to output in OpenAPI format), suggesting class and module structures, and drafting database schemas (SQL DDL or ORM models). Within the Cursor IDE, Copilot and Augment help refine these generated artifacts, analyze code complexity for effort estimation, and break down features into a structured list of development tasks.
*   **Output:** This step yields critical design details:
    *   A detailed implementation design document `/home/<USER>/project_data/detailed_design.md`.
    *   A formal API specification `/home/<USER>/project_data/api_spec.yaml` (OpenAPI format).
    *   The database schema definition `/home/<USER>/project_data/database_schema.sql`.
    *   A structured task list `/home/<USER>/project_data/task_list.json`, formatted as a JSON array suitable for automated import into task management systems.

## Step 6: Task Management & Planning (Phân task, lên plan)

*   **Role:** Project Manager (PM) / Team Leader
*   **Tools:** Jira/GitLab Issues/TaskMaster API (potentially orchestrated via n8n or custom scripts)
*   **Input:** The primary input is the `/home/<USER>/project_data/task_list.json` file generated in the previous step. Links to relevant documents like `/home/<USER>/project_data/architecture_design.md` and the Figma design link should be included within the JSON task descriptions.
*   **Process:** An automated script or an n8n workflow reads the `task_list.json` file. It then interacts with the API of the chosen task management system (Jira, GitLab Issues, TaskMaster) to create individual tasks/issues based on the JSON data. The PM/Team Leader oversees this process and performs manual assignments or adjustments within the task management tool as needed.
*   **Output:** The successful creation of tasks within the designated management system. The output can be represented by the list of generated task IDs or URLs (e.g., `ABC-124`, `https://gitlab.com/your_group/your_project/-/issues/5`). A log file confirming the creation process can be saved, e.g., `/home/<USER>/logs/task_creation.log`.

## Step 7: Coding (Viết code)

*   **Role:** Developer
*   **AI Tools:** Github Copilot, Cursor
*   **Input:** A specific Task ID/URL from the task management system, which links to the detailed description and requirements. Developers reference the `/home/<USER>/project_docs/coding_convention.md` document. Access to the project's Git repository is required.
*   **Process:** Developers work primarily within the Cursor IDE, which has integrated Github Copilot support. They fetch the assigned task details. Copilot provides real-time code suggestions, autocompletion, and boilerplate generation based on the task description, existing codebase context, and learned patterns. Linters and formatters configured according to the coding convention document provide immediate feedback. Developers commit their code to the appropriate feature branch.
*   **Output:** One or more Git commit hashes on the designated feature branch (e.g., `feature/ABC-124-implement-login`), representing the implemented source code.

## Step 8: Writing Test Cases (Viết test case)

*   **Role:** Developer, Tester
*   **AI Tools:** Github Copilot, Cursor, Augment
*   **Input:** The Git commit hash(es) containing the newly written source code from Step 7. The `/home/<USER>/project_data/detailed_design.md` and `/home/<USER>/project_docs/test_guideline.md` serve as references for test requirements and standards.
*   **Process:** Working within the Cursor IDE, developers or testers use Copilot to generate unit test skeletons and suggest assertions based on the source code. Augment can analyze the code to identify areas lacking coverage or suggest edge cases. For E2E tests, Copilot can assist in scripting interactions based on feature descriptions and UI element identifiers found in design documents or the code itself. All generated tests must align with the `test_guideline.md`.
*   **Output:** Git commit hash(es) on the same feature branch, adding the corresponding test scripts (e.g., `/tests/unit/test_login.py`, `/tests/e2e/login.spec.js`).

## Step 9: Review (Review code)

*   **Role:** DevOps, Automated System
*   **AI Tools:** CI/CD Pipeline, n8n workflow, Custom AI Agent (LLM API wrapper)
*   **Input:** A Pull Request (PR) URL linking the feature branch (containing both source code and test scripts) to the main development branch.
*   **Process:** The creation of a PR automatically triggers the CI/CD pipeline. The pipeline executes static analysis (linting, formatting), builds the code, and runs all unit and E2E tests. Concurrently or subsequently, the pipeline triggers an n8n workflow. This workflow fetches the code changes (diff) from the PR, sends it to the custom AI Agent (which wraps an LLM like GPT or Gemini with specific review-focused prompts), receives AI-generated review comments (covering potential bugs, security flaws, performance issues, convention adherence), and posts these comments back to the PR via the Git platform's API.
*   **Output:** Automated comments posted directly on the Pull Request interface. A clear Pass/Fail status indicator from the CI/CD pipeline associated with the PR. Optionally, a summary report could be generated by the workflow: `/home/<USER>/reports/review_summary_pr_X.md`.

## Step 10: Writing User Documentation (Viết tài liệu hướng dẫn)

*   **Role:** BA / Developer
*   **AI Tools:** Custom GPT, Grok workspace, Custom Gem (Gemini), Github Copilot, Cursor, Augment
*   **Input:** The Git commit hash of the merged code (post-review). Reference documents: `/home/<USER>/project_data/detailed_design.md`, `/home/<USER>/project_data/srs_document.md`, and the Figma link or UI specifications.
*   **Process:** Within the IDE, Copilot/Cursor/Augment assist developers in generating or improving code comments (Docstrings, Javadoc). For user-facing documentation, the BA or developer uses a custom AI agent (GPT/Gemini) or Grok, providing the source code and relevant design/SRS documents as context. The AI generates drafts for the user manual, installation guide, and potentially API documentation, following predefined templates. These drafts are then reviewed and refined by the assigned personnel.
*   **Output:** Finalized documentation files:
    *   `/home/<USER>/project_docs/user_manual.md`
    *   `/home/<USER>/project_docs/api_documentation.md` (or potentially auto-generated HTML)
    *   `/home/<USER>/project_docs/installation_guide.md`
    *   Updated source code files incorporating improved comments.

## Step 11: Deployment (Deploy hệ thống / CI/CD)

*   **Role:** DevOps
*   **AI Tools:** Github Copilot, Cursor, Augment (for script assistance), CI/CD Pipeline
*   **Input:** The Git commit hash of the final, merged, and documented code ready for release. The `/home/<USER>/project_data/architecture_design.md` (specifically the deployment section) and potentially `/home/<USER>/project_data/detailed_design.md` are used as references.
*   **Process:** DevOps engineers may use Copilot/Cursor/Augment within their IDE to write or refine deployment scripts (e.g., Dockerfiles, Kubernetes manifests, infrastructure-as-code files) based on the architecture specification. The primary deployment mechanism is the pre-configured CI/CD pipeline (from Step 3, possibly refined here), which automates the build, testing (final check), and deployment to the target environment (staging or production).
*   **Output:** A status message from the CI/CD pipeline indicating deployment success or failure. The URL of the newly deployed application environment (e.g., `https://staging.yourcompany.com` or `https://app.yourcompany.com`). A deployment guide document: `/home/<USER>/project_docs/deployment_guide.md`.

## Step 12: Monitoring / Tracing / Logging

*   **Role:** DevOps
*   **Tools:** Signoz, Jaeger, ELK Stack, Prometheus & Grafana
*   **Input:** The URL of the deployed system environment from Step 11.
*   **Process:** Standard monitoring tools, configured as part of the infrastructure (potentially defined in Step 3 and deployed in Step 11), automatically begin collecting data from the live application. DevOps engineers interact with the dashboards and logs provided by these tools to observe system health and performance.
*   **Output:** Continuous streams of monitoring data accessible via specific tool interfaces/dashboards (URLs provided to the team). Log files stored in a centralized location. Trace data available in Jaeger/Signoz. A guide on how to access and interpret the monitoring setup: `/home/<USER>/project_docs/monitoring_guide.md`.

