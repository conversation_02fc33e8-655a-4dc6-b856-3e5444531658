# Mapping of AI Tools to Software Development Process Steps

This document maps the AI tools mentioned in `overview.md` to the corresponding steps in the software development lifecycle and the roles involved.

## AI Tool Mapping

1.  **Step 1: Tổng hợp requirement từ khách hàng (Requirement Gathering)**
    *   **Role:** BA
    *   **AI Tools:** Google NotebookLM

2.  **Step 2: Tạo tài liệu SRS (Software Requirement Specification)**
    *   **Role:** BA
    *   **AI Tools:** Google NotebookLM (for lookup), Custom GPT (ChatGPT Plus) (for editing/generation)

3.  **Step 3: Thiết kế kiến trúc hệ thống (Architecture Design)**
    *   **Role:** System Architect
    *   **AI Tools:** Custom GPT (ChatGPT), Github Copilot, Cursor, Augment

4.  **Step 4: Thiết kế giao diện / prototype (UI/Prototype Design)**
    *   **Role:** Designer
    *   **AI Tools:** V0, UX pilot (Note: Figma is listed but primarily a design tool, though AI plugins exist)

5.  **Step 5: <PERSON>hiế<PERSON> kế chi tiết phần implement c<PERSON><PERSON> chức n<PERSON> (Detailed Design)**
    *   **Role:** System Architect, Developer
    *   **AI Tools:** Custom GPT (ChatGPT), Grok workspace, Custom Gem (Gemini), Github Copilot, Cursor, Augment

6.  **Step 6: Phân task, lên plan, quản lý plan (Task Management & Planning)**
    *   **Role:** PM / TeamLeader
    *   **AI Tools:** (None explicitly listed as primary AI tools for this step, though tools like Jira might have AI features)

7.  **Step 7: Viết code (Coding)**
    *   **Role:** Developer
    *   **AI Tools:** Github Copilot, Cursor

8.  **Step 8: Viết test case (Testing)**
    *   **Role:** Developer, Tester
    *   **AI Tools:** Github Copilot, Cursor, Augment

9.  **Step 9: Review (Code Review)**
    *   **Role:** DevOps
    *   **AI Tools:** CI/CD + n8n workflow + AI Agent + LLM API (Integrated solution)

10. **Step 10: Viết tài liệu hướng dẫn sử dụng (Documentation)**
    *   **Role:** BA / Developer
    *   **AI Tools:** Custom GPT (ChatGPT), Grok workspace, Custom Gem (Gemini), Github Copilot, Cursor, Augment

11. **Step 11: Deploy hệ thống / CI/CD (Deployment)**
    *   **Role:** DevOps
    *   **AI Tools:** Github Copilot, Cursor, Augment

12. **Step 12: Monitoring / Tracing / Logging**
    *   **Role:** DevOps
    *   **AI Tools:** (None explicitly listed as primary AI tools, standard monitoring tools mentioned)

## Summary of Identified AI Tools:

*   Google NotebookLM
*   Custom GPT (ChatGPT / ChatGPT Plus)
*   Github Copilot
*   Cursor
*   Augment
*   V0
*   UX pilot
*   Grok workspace
*   Custom Gem (Gemini)
*   n8n (as part of an automated workflow with AI Agents/LLMs)
*   AI Agent (Generic term used in Step 9)
*   LLM API (Generic term used in Step 9)

