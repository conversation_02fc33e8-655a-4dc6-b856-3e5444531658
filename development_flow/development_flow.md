---
markmap:
  color:
    - green
    - red
    - blue
    - yellow
  maxWidth: 250
  # other options
---
# <PERSON>uy trình phát triển phần mềm
1. PM d<PERSON> án quản lý task, milestones
   1. <PERSON><PERSON><PERSON><PERSON> lập master plan
   2. Milestone
   3. Task
   4. <PERSON><PERSON> công task
   5. <PERSON> dõi tiến độ
   6. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> tiến độ
   7. <PERSON><PERSON><PERSON><PERSON> lý rủi ro / problem
   8. <PERSON><PERSON><PERSON><PERSON> lý thay đổi
2. <PERSON> thu cập y<PERSON><PERSON> cầu của khách hàng
   1. <PERSON><PERSON><PERSON> lo<PERSON><PERSON> tài liệu
      1. MOM (meeting minutes)
      2. <PERSON><PERSON><PERSON> tà<PERSON> liệu sẵn có của khách hàng
      3. <PERSON><PERSON> thống cũ (tr<PERSON><PERSON><PERSON> hợ<PERSON> nâng cấp hệ thống)
      4. <PERSON><PERSON> trao đổi, lịch sử chat trao đổi
   2. <PERSON><PERSON><PERSON> yê<PERSON> cầu
      1. Y<PERSON><PERSON> cầ<PERSON> mới
      2. Change requirement
3. <PERSON> viế<PERSON> tà<PERSON> liệu SRS
   1. <PERSON><PERSON><PERSON> công việc
      1. Usecase mới
      2. <PERSON><PERSON><PERSON> nh<PERSON><PERSON> usecase cũ (Change requirement)
   2. <PERSON><PERSON><PERSON> tài liệu
      1. High level design
         1. List objects
            1. Relationships
         2. List Usecases
            1. Relationships
         3. List screens
            1. Relationships
         4. Transition State (Workflow)
         5. Transition
            1. Transition between screens
            2. Transition between usecases
         6. Others
            1. Non-functional requirements
            2. Common functions
      2. Detail level design
         1. Objects (OBJ)
            1. Properties
            2. Relationships
         2. Usecases (UC)
            1. Function
               1. Objective
               2. Actor
               3. Pre-condition
               4. Trigger
               5. Post-condition
            2. Acitivity flow
               1. Table with stepno, what user does, what system does
            3. Business rules (BR)
               1. BR-<number>
                  1. Step in activity flow
                  2. Logic
            4. Relationships (with other usecases, objects, screens)
         3. Screens (SCR)
            1. Components (COM)
            2. Relationships (with other screens, usecases, objects) 
         4. Others
            1. Messages (MSG)
            2. Templates (TPL)
4. SA dựng kiến trúc hệ thống
   1. System architecture
      1. System architecture diagram
      2. Techstack
      3. Non-functional requirements
         1. Security
         2. Performance
         3. Scalability
         4. Maintainability
         5. Availability
         6. Usability 
   2. Application architecture
      1. Application architecture diagram
      2. Project tree
   3. Infrastructure architecture
      1. Infrastructure architecture diagram
   4. Common handler
      1. Error handler
      2. Logging handler
   5. Coding convention
      1. Naming convention
      2. Code style
      3. Do and Don't
   6. Code review convention
      1. Code review checklist
      2. Code review process
   7. CI/CD convention
      1. CI/CD pipeline
      2. CI/CD tools
      3. CI/CD process
5. DevOps khởi tạo infra / project dự án
6. Dev viết tài liệu detailed design (theo usecase)
   1. Backend
      1. API docs
      2. Message queue
      3. Cache
      5. File storage
      6. Others
   2. Frontend
      1. Components
      2. Screens
      3. State management
      4. Routing
      5. Service
   3. Mobile
      1. Components
      2. Screens
      3. State management
      4. Routing
      5. Service
   4. Database
   5. Others
7. Dev implement chức năng
8. Dev viết unit test
9.  CI/CD chạy:
   - UT
   - Coding convention review
   - Code quality review (sonarqube)
   - Code review theo rule của dự án.
   - Chạy e2e / automation test (daily)
10. Triển khai lên test
11. Tester test thủ công
    1.  Nếu có bug
        1.  Dev fix bug
        2.  Tester test lại
    2.  Nếu không có bug thì kết thúc task
12. Triển khai lên staging
# Project structure

# Project conventions