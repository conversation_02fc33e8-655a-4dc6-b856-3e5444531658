<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> c<PERSON>o chuyên sâu về các công cụ AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            overflow: hidden;
            margin: 0;
            padding: 0;
            height: 100vh;
        }

        .slide {
            display: none !important;
            aspect-ratio: 16/9;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
            opacity: 0;
            transition: opacity 0.6s ease-in-out;
        }

        .slide.active {
            display: flex !important;
            opacity: 1;
        }

        .slide.fading-out {
            opacity: 0;
        }

        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 100;
            display: flex;
            align-items: center;
            gap: 8px;
        }
    </style>
</head>

<body class="bg-gray-900 font-sans">
    <!-- Navigation Controls -->
    <div class="nav-controls">
        <button id="prevBtn"
            class="bg-white/20 backdrop-blur-sm text-white p-3 rounded-full hover:bg-white/30 transition-all duration-200 cursor-pointer">
            <i class="fas fa-chevron-left text-lg"></i>
        </button>
        <span id="slideCounter"
            class="bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium">1 / 14</span>
        <button id="nextBtn"
            class="bg-white/20 backdrop-blur-sm text-white p-3 rounded-full hover:bg-white/30 transition-all duration-200 cursor-pointer">
            <i class="fas fa-chevron-right text-lg"></i>
        </button>
    </div>

    <!-- Slide 1: Welcome Slide -->
    <div class="slide active w-full h-screen bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-4xl mx-auto text-center text-white">
            <i class="fas fa-robot text-8xl mb-8 opacity-80"></i>
            <h1 class="text-6xl font-bold mb-6">Báo cáo chuyên sâu về các công cụ AI</h1>
            <p class="text-2xl mb-8">Tìm hiểu về những công cụ AI hàng đầu đang thay đổi thế giới</p>
            <div class="text-lg space-y-2">
                <p class="flex items-center justify-center gap-2">
                    <i class="fas fa-user"></i>
                    Tác giả: Manus AI
                </p>
                <p class="flex items-center justify-center gap-2">
                    <i class="fas fa-calendar"></i>
                    Tháng 6, 2025
                </p>
            </div>
        </div>
    </div>

    <!-- Slide 2: Table of Contents -->
    <div class="slide w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-6xl mx-auto w-full">
            <h1 class="text-4xl font-bold text-gray-900 mb-12 text-center">Nội dung trình bày</h1>
            <div class="grid grid-cols-2 gap-8">
                <div class="space-y-6">
                    <div
                        class="flex items-center space-x-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border-l-4 border-blue-500">
                        <div
                            class="bg-blue-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold">
                            1</div>
                        <span class="text-lg font-medium">Giới thiệu tổng quan</span>
                    </div>
                    <div
                        class="flex items-center space-x-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border-l-4 border-green-500">
                        <div
                            class="bg-green-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold">
                            2</div>
                        <span class="text-lg font-medium">ChatGPT - OpenAI</span>
                    </div>
                    <div
                        class="flex items-center space-x-4 p-4 bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl border-l-4 border-purple-500">
                        <div
                            class="bg-purple-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold">
                            3</div>
                        <span class="text-lg font-medium">Perplexity AI</span>
                    </div>
                    <div
                        class="flex items-center space-x-4 p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl border-l-4 border-orange-500">
                        <div
                            class="bg-orange-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold">
                            4</div>
                        <span class="text-lg font-medium">Grok AI - xAI</span>
                    </div>
                </div>
                <div class="space-y-6">
                    <div
                        class="flex items-center space-x-4 p-4 bg-gradient-to-r from-teal-50 to-cyan-50 rounded-xl border-l-4 border-teal-500">
                        <div
                            class="bg-teal-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold">
                            5</div>
                        <span class="text-lg font-medium">Gemini AI - Google</span>
                    </div>
                    <div
                        class="flex items-center space-x-4 p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-xl border-l-4 border-pink-500">
                        <div
                            class="bg-pink-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold">
                            6</div>
                        <span class="text-lg font-medium">So sánh tổng quan</span>
                    </div>
                    <div
                        class="flex items-center space-x-4 p-4 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-xl border-l-4 border-indigo-500">
                        <div
                            class="bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold">
                            7</div>
                        <span class="text-lg font-medium">Ứng dụng thực tế</span>
                    </div>
                    <div
                        class="flex items-center space-x-4 p-4 bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl border-l-4 border-gray-500">
                        <div
                            class="bg-gray-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold">
                            8</div>
                        <span class="text-lg font-medium">Kết luận & Khuyến nghị</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 3: Giới thiệu tổng quan -->
    <div class="slide w-full h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full">
            <div class="col-span-4 flex flex-col justify-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Bối cảnh AI hiện tại</h1>
                <h2 class="text-xl text-gray-600 mb-8">Cuộc cách mạng công nghệ đang diễn ra</h2>
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-rocket text-blue-600 text-2xl"></i>
                        <span class="font-semibold text-lg">Phát triển vũ bão</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-globe text-green-600 text-2xl"></i>
                        <span class="font-semibold text-lg">Đa dạng ứng dụng</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-chart-line text-purple-600 text-2xl"></i>
                        <span class="font-semibold text-lg">Tăng trưởng mạnh</span>
                    </div>
                </div>
            </div>
            <div class="col-span-8 flex items-center justify-center">
                <div class="relative w-full max-w-2xl">
                    <canvas id="aiGrowthChart" class="w-full h-96"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 4: ChatGPT Overview -->
    <div class="slide w-full h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full">
            <div class="col-span-4 flex flex-col justify-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">ChatGPT</h1>
                <h2 class="text-xl text-gray-600 mb-8">Người tiên phong trong AI đàm thoại</h2>
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-building text-green-600 text-2xl"></i>
                        <span class="font-medium text-lg">OpenAI</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-users text-blue-600 text-2xl"></i>
                        <span class="font-medium text-lg">1M users / 5 ngày</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-cog text-purple-600 text-2xl"></i>
                        <span class="font-medium text-lg">GPT-4 Model</span>
                    </div>
                </div>
            </div>
            <div class="col-span-8 flex items-center justify-center">
                <div class="grid grid-cols-2 gap-6 w-full max-w-xl">
                    <div
                        class="bg-white/80 backdrop-blur-sm p-6 rounded-xl text-center transform hover:scale-105 transition-all">
                        <i class="fas fa-comments text-4xl text-green-600 mb-4"></i>
                        <h3 class="font-bold mb-2">Đàm thoại</h3>
                        <p class="text-sm text-gray-600">Tự nhiên & Mạch lạc</p>
                    </div>
                    <div
                        class="bg-white/80 backdrop-blur-sm p-6 rounded-xl text-center transform hover:scale-105 transition-all">
                        <i class="fas fa-code text-4xl text-blue-600 mb-4"></i>
                        <h3 class="font-bold mb-2">Lập trình</h3>
                        <p class="text-sm text-gray-600">Code & Debug</p>
                    </div>
                    <div
                        class="bg-white/80 backdrop-blur-sm p-6 rounded-xl text-center transform hover:scale-105 transition-all">
                        <i class="fas fa-pen text-4xl text-purple-600 mb-4"></i>
                        <h3 class="font-bold mb-2">Sáng tạo</h3>
                        <p class="text-sm text-gray-600">Nội dung đa dạng</p>
                    </div>
                    <div
                        class="bg-white/80 backdrop-blur-sm p-6 rounded-xl text-center transform hover:scale-105 transition-all">
                        <i class="fas fa-language text-4xl text-orange-600 mb-4"></i>
                        <h3 class="font-bold mb-2">Đa ngôn ngữ</h3>
                        <p class="text-sm text-gray-600">Dịch & Hiểu</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 5: ChatGPT Timeline -->
    <div class="slide w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full">
            <div class="col-span-4 flex flex-col justify-center">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Lịch sử phát triển</h1>
                <h2 class="text-lg text-gray-600 mb-6">Từ GPT-1 đến GPT-4o</h2>
                <div class="space-y-3">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-rocket text-green-500 text-2xl"></i>
                        <span class="font-medium text-lg">2018 - GPT-1</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-chart-line text-blue-500 text-2xl"></i>
                        <span class="font-medium text-lg">2022 - ChatGPT</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-star text-purple-500 text-2xl"></i>
                        <span class="font-medium text-lg">2023 - GPT-4</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-bolt text-orange-500 text-2xl"></i>
                        <span class="font-medium text-lg">2024 - GPT-4o</span>
                    </div>
                </div>
            </div>
            <div class="col-span-8 flex items-center justify-center">
                <div class="w-full max-w-4xl">
                    <div class="relative">
                        <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-green-400 to-blue-600">
                        </div>
                        <div class="space-y-8">
                            <div class="flex items-center space-x-6">
                                <div
                                    class="bg-green-500 text-white w-16 h-16 rounded-full flex items-center justify-center font-bold text-sm">
                                    2018</div>
                                <div class="bg-white p-4 rounded-lg shadow-md flex-1">
                                    <h3 class="font-bold text-green-600">GPT-1</h3>
                                    <p class="text-sm">117M parameters - Nền móng transformer</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-6">
                                <div
                                    class="bg-blue-500 text-white w-16 h-16 rounded-full flex items-center justify-center font-bold text-sm">
                                    2019</div>
                                <div class="bg-white p-4 rounded-lg shadow-md flex-1">
                                    <h3 class="font-bold text-blue-600">GPT-2</h3>
                                    <p class="text-sm">1.5B parameters - Văn bản mạch lạc</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-6">
                                <div
                                    class="bg-purple-500 text-white w-16 h-16 rounded-full flex items-center justify-center font-bold text-sm">
                                    2020</div>
                                <div class="bg-white p-4 rounded-lg shadow-md flex-1">
                                    <h3 class="font-bold text-purple-600">GPT-3</h3>
                                    <p class="text-sm">175B parameters - Bước nhảy vọt</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-6">
                                <div
                                    class="bg-orange-500 text-white w-16 h-16 rounded-full flex items-center justify-center font-bold text-sm">
                                    2022</div>
                                <div class="bg-white p-4 rounded-lg shadow-md flex-1">
                                    <h3 class="font-bold text-orange-600">ChatGPT</h3>
                                    <p class="text-sm">Ra mắt công chúng - 1M users/5 ngày</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-6">
                                <div
                                    class="bg-indigo-500 text-white w-16 h-16 rounded-full flex items-center justify-center font-bold text-sm">
                                    2023</div>
                                <div class="bg-white p-4 rounded-lg shadow-md flex-1">
                                    <h3 class="font-bold text-indigo-600">GPT-4</h3>
                                    <p class="text-sm">Multimodal AI - Văn bản, hình ảnh, giọng nói</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-6">
                                <div
                                    class="bg-orange-500 text-white w-16 h-16 rounded-full flex items-center justify-center font-bold text-sm">
                                    2024</div>
                                <div class="bg-white p-4 rounded-lg shadow-md flex-1">
                                    <h3 class="font-bold text-orange-600">GPT-4o</h3>
                                    <p class="text-sm">Omni-modal - Nhanh hơn, rẻ hơn, mạnh hơn</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 6: Perplexity AI -->
    <div class="slide w-full h-screen bg-gradient-to-br from-purple-50 to-violet-100 flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full">
            <div class="col-span-4 flex flex-col justify-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Perplexity AI</h1>
                <h2 class="text-xl text-gray-600 mb-8">Tìm kiếm thông minh với trích dẫn</h2>
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-calendar text-purple-600 text-2xl"></i>
                        <span class="font-medium text-lg">Thành lập 2022</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-dollar-sign text-green-600 text-2xl"></i>
                        <span class="font-medium text-lg">Định giá $14B</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-quote-right text-blue-600 text-2xl"></i>
                        <span class="font-medium text-lg">Trích dẫn nguồn</span>
                    </div>
                </div>
            </div>
            <div class="col-span-8 flex items-center justify-center">
                <div class="w-full max-w-2xl space-y-6">
                    <div class="bg-white/90 backdrop-blur-sm p-6 rounded-xl">
                        <div class="flex items-center gap-4 mb-4">
                            <i class="fas fa-search text-2xl text-purple-600"></i>
                            <h3 class="font-bold text-lg">Tính năng nổi bật</h3>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-quote-right text-purple-500"></i>
                                <span class="text-sm">Trích dẫn nguồn</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-shopping-cart text-purple-500"></i>
                                <span class="text-sm">Shopping Hub</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-chart-line text-purple-500"></i>
                                <span class="text-sm">Finance Tools</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-file-alt text-purple-500"></i>
                                <span class="text-sm">Internal Search</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-500 to-violet-600 text-white p-6 rounded-xl">
                        <h3 class="font-bold mb-2 flex items-center gap-2">
                            <i class="fas fa-star"></i>
                            Ưu điểm cạnh tranh
                        </h3>
                        <p class="text-sm opacity-90">Kết hợp sức mạnh tìm kiếm web với AI đàm thoại, cung cấp trích dẫn
                            rõ ràng cho mọi thông tin</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 7: Grok AI -->
    <div class="slide w-full h-screen bg-gradient-to-br from-orange-50 to-red-100 flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full">
            <div class="col-span-4 flex flex-col justify-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Grok AI</h1>
                <h2 class="text-xl text-gray-600 mb-8">AI "nổi loạn" của Elon Musk</h2>
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <i class="fab fa-x-twitter text-orange-600 text-2xl"></i>
                        <span class="font-medium text-lg">xAI Platform</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-clock text-blue-600 text-2xl"></i>
                        <span class="font-medium text-lg">Real-time data</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-eye text-green-600 text-2xl"></i>
                        <span class="font-medium text-lg">Vision AI</span>
                    </div>
                </div>
            </div>
            <div class="col-span-8 flex items-center justify-center">
                <div class="grid grid-cols-2 gap-6 w-full max-w-2xl">
                    <div class="bg-white/90 backdrop-blur-sm p-6 rounded-xl border-l-4 border-orange-500">
                        <i class="fas fa-eye text-3xl text-orange-600 mb-3"></i>
                        <h3 class="font-bold mb-2">Vision</h3>
                        <p class="text-sm text-gray-600">Phân tích hình ảnh thực</p>
                    </div>
                    <div class="bg-white/90 backdrop-blur-sm p-6 rounded-xl border-l-4 border-red-500">
                        <i class="fas fa-search-plus text-3xl text-red-600 mb-3"></i>
                        <h3 class="font-bold mb-2">DeepSearch</h3>
                        <p class="text-sm text-gray-600">Tìm kiếm sâu</p>
                    </div>
                    <div class="bg-white/90 backdrop-blur-sm p-6 rounded-xl border-l-4 border-purple-500">
                        <i class="fas fa-brain text-3xl text-purple-600 mb-3"></i>
                        <h3 class="font-bold mb-2">Think Mode</h3>
                        <p class="text-sm text-gray-600">Quá trình suy luận</p>
                    </div>
                    <div class="bg-white/90 backdrop-blur-sm p-6 rounded-xl border-l-4 border-blue-500">
                        <i class="fas fa-microphone text-3xl text-blue-600 mb-3"></i>
                        <h3 class="font-bold mb-2">Voice Mode</h3>
                        <p class="text-sm text-gray-600">Đa ngôn ngữ</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 8: Gemini AI -->
    <div class="slide w-full h-screen bg-gradient-to-br from-teal-50 to-cyan-100 flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full">
            <div class="col-span-4 flex flex-col justify-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Gemini AI</h1>
                <h2 class="text-xl text-gray-600 mb-8">Đa phương thức từ Google</h2>
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <i class="fab fa-google text-teal-600 text-2xl"></i>
                        <span class="font-medium text-lg">Google Platform</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-layer-group text-blue-600 text-2xl"></i>
                        <span class="font-medium text-lg">Multimodal AI</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-mobile-alt text-green-600 text-2xl"></i>
                        <span class="font-medium text-lg">Ecosystem tích hợp</span>
                    </div>
                </div>
            </div>
            <div class="col-span-8 flex items-center justify-center">
                <div class="w-full max-w-2xl">
                    <div class="bg-white/90 backdrop-blur-sm p-8 rounded-xl">
                        <h3 class="font-bold text-xl mb-6 text-center text-teal-700">Khả năng đa phương thức</h3>
                        <div class="grid grid-cols-3 gap-6">
                            <div class="text-center">
                                <div
                                    class="bg-teal-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-font text-2xl text-teal-600"></i>
                                </div>
                                <span class="font-medium">Văn bản</span>
                            </div>
                            <div class="text-center">
                                <div
                                    class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-image text-2xl text-blue-600"></i>
                                </div>
                                <span class="font-medium">Hình ảnh</span>
                            </div>
                            <div class="text-center">
                                <div
                                    class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-volume-up text-2xl text-purple-600"></i>
                                </div>
                                <span class="font-medium">Âm thanh</span>
                            </div>
                            <div class="text-center">
                                <div
                                    class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-video text-2xl text-green-600"></i>
                                </div>
                                <span class="font-medium">Video</span>
                            </div>
                            <div class="text-center">
                                <div
                                    class="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-code text-2xl text-orange-600"></i>
                                </div>
                                <span class="font-medium">Code</span>
                            </div>
                            <div class="text-center">
                                <div
                                    class="bg-pink-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-layer-group text-2xl text-pink-600"></i>
                                </div>
                                <span class="font-medium">Kết hợp</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 9: So sánh tổng quan -->
    <div class="slide w-full h-screen bg-gradient-to-br from-pink-50 to-rose-100 flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full">
            <div class="col-span-4 flex flex-col justify-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">So sánh tổng quan</h1>
                <h2 class="text-xl text-gray-600 mb-8">Điểm mạnh của từng công cụ</h2>
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-chart-radar text-blue-600 text-2xl"></i>
                        <span class="font-medium text-lg">6 tiêu chí đánh giá</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-trophy text-yellow-600 text-2xl"></i>
                        <span class="font-medium text-lg">4 công cụ hàng đầu</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-balance-scale text-purple-600 text-2xl"></i>
                        <span class="font-medium text-lg">So sánh khách quan</span>
                    </div>
                </div>
            </div>
            <div class="col-span-8 flex items-center justify-center">
                <div class="w-full max-w-4xl">
                    <canvas id="comparisonChart" class="w-full h-96"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 10: Pricing Models -->
    <div class="slide w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full">
            <div class="col-span-4 flex flex-col justify-center">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Mô hình định giá</h1>
                <h2 class="text-lg text-gray-600 mb-6">Freemium vs Premium</h2>
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-free-code-camp text-green-600 text-2xl"></i>
                        <span class="font-medium text-lg">Freemium phổ biến</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-crown text-yellow-600 text-2xl"></i>
                        <span class="font-medium text-lg">Premium tính năng</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-dollar-sign text-blue-600 text-2xl"></i>
                        <span class="font-medium text-lg">$20/tháng trung bình</span>
                    </div>
                </div>
            </div>
            <div class="col-span-8 flex items-center justify-center">
                <div class="grid grid-cols-2 gap-6 w-full max-w-2xl">
                    <div class="bg-gradient-to-br from-green-100 to-emerald-100 p-6 rounded-xl border border-green-200">
                        <h3 class="font-bold text-green-800 mb-4 flex items-center gap-2">
                            <i class="fas fa-check-circle"></i>
                            Miễn phí
                        </h3>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center gap-2">
                                <i class="fas fa-dot-circle text-green-600 text-xs"></i>
                                ChatGPT: GPT-3.5
                            </li>
                            <li class="flex items-center gap-2">
                                <i class="fas fa-dot-circle text-green-600 text-xs"></i>
                                Perplexity: Basic search
                            </li>
                            <li class="flex items-center gap-2">
                                <i class="fas fa-dot-circle text-green-600 text-xs"></i>
                                Grok: X users
                            </li>
                            <li class="flex items-center gap-2">
                                <i class="fas fa-dot-circle text-green-600 text-xs"></i>
                                Gemini: Standard model
                            </li>
                        </ul>
                    </div>
                    <div class="bg-gradient-to-br from-blue-100 to-indigo-100 p-6 rounded-xl border border-blue-200">
                        <h3 class="font-bold text-blue-800 mb-4 flex items-center gap-2">
                            <i class="fas fa-star"></i>
                            Premium
                        </h3>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center gap-2">
                                <i class="fas fa-dot-circle text-blue-600 text-xs"></i>
                                ChatGPT Plus: $20/tháng
                            </li>
                            <li class="flex items-center gap-2">
                                <i class="fas fa-dot-circle text-blue-600 text-xs"></i>
                                Perplexity Pro: API access
                            </li>
                            <li class="flex items-center gap-2">
                                <i class="fas fa-dot-circle text-blue-600 text-xs"></i>
                                SuperGrok: Advanced features
                            </li>
                            <li class="flex items-center gap-2">
                                <i class="fas fa-dot-circle text-blue-600 text-xs"></i>
                                Gemini Pro: Enterprise
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 11: Ứng dụng thực tế -->
    <div class="slide w-full h-screen bg-gradient-to-br from-indigo-50 to-blue-100 flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full">
            <div class="col-span-4 flex flex-col justify-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Ứng dụng thực tế</h1>
                <h2 class="text-xl text-gray-600 mb-8">Các lĩnh vực áp dụng AI</h2>
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-graduation-cap text-blue-600 text-2xl"></i>
                        <span class="font-medium text-lg">Giáo dục</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-briefcase text-green-600 text-2xl"></i>
                        <span class="font-medium text-lg">Doanh nghiệp</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-heart text-red-600 text-2xl"></i>
                        <span class="font-medium text-lg">Y tế</span>
                    </div>
                </div>
            </div>
            <div class="col-span-8 flex items-center justify-center">
                <div class="grid grid-cols-3 gap-4 w-full max-w-4xl">
                    <div class="bg-white/90 backdrop-blur-sm p-4 rounded-lg text-center hover:shadow-lg transition-all">
                        <i class="fas fa-graduation-cap text-3xl text-blue-600 mb-3"></i>
                        <h3 class="font-bold mb-2 text-sm">Giáo dục</h3>
                        <p class="text-xs text-gray-600">Gia sư AI, học tập cá nhân hóa</p>
                    </div>
                    <div class="bg-white/90 backdrop-blur-sm p-4 rounded-lg text-center hover:shadow-lg transition-all">
                        <i class="fas fa-briefcase text-3xl text-green-600 mb-3"></i>
                        <h3 class="font-bold mb-2 text-sm">Doanh nghiệp</h3>
                        <p class="text-xs text-gray-600">Tự động hóa văn phòng</p>
                    </div>
                    <div class="bg-white/90 backdrop-blur-sm p-4 rounded-lg text-center hover:shadow-lg transition-all">
                        <i class="fas fa-heart text-3xl text-red-600 mb-3"></i>
                        <h3 class="font-bold mb-2 text-sm">Y tế</h3>
                        <p class="text-xs text-gray-600">Hỗ trợ chẩn đoán</p>
                    </div>
                    <div class="bg-white/90 backdrop-blur-sm p-4 rounded-lg text-center hover:shadow-lg transition-all">
                        <i class="fas fa-paint-brush text-3xl text-purple-600 mb-3"></i>
                        <h3 class="font-bold mb-2 text-sm">Sáng tạo</h3>
                        <p class="text-xs text-gray-600">Nội dung, nghệ thuật</p>
                    </div>
                    <div class="bg-white/90 backdrop-blur-sm p-4 rounded-lg text-center hover:shadow-lg transition-all">
                        <i class="fas fa-headset text-3xl text-orange-600 mb-3"></i>
                        <h3 class="font-bold mb-2 text-sm">Hỗ trợ KH</h3>
                        <p class="text-xs text-gray-600">Chatbot thông minh</p>
                    </div>
                    <div class="bg-white/90 backdrop-blur-sm p-4 rounded-lg text-center hover:shadow-lg transition-all">
                        <i class="fas fa-search text-3xl text-teal-600 mb-3"></i>
                        <h3 class="font-bold mb-2 text-sm">Nghiên cứu</h3>
                        <p class="text-xs text-gray-600">Phân tích dữ liệu</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 12: Xu hướng tương lai -->
    <div class="slide w-full h-screen bg-gradient-to-br from-violet-50 to-purple-100 flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full">
            <div class="col-span-4 flex flex-col justify-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Xu hướng tương lai</h1>
                <h2 class="text-xl text-gray-600 mb-8">AI sẽ phát triển như thế nào?</h2>
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-robot text-violet-600 text-2xl"></i>
                        <span class="font-medium text-lg">AI Agents</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-layer-group text-blue-600 text-2xl"></i>
                        <span class="font-medium text-lg">Multimodal AI</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-brain text-green-600 text-2xl"></i>
                        <span class="font-medium text-lg">Reasoning AI</span>
                    </div>
                </div>
            </div>
            <div class="col-span-8 flex items-center justify-center">
                <div class="space-y-6 w-full max-w-2xl">
                    <div class="bg-white/90 backdrop-blur-sm p-6 rounded-xl border-l-4 border-violet-500">
                        <h3 class="font-bold text-violet-700 mb-2 flex items-center gap-2">
                            <i class="fas fa-robot"></i>
                            AI Agents
                        </h3>
                        <p class="text-sm text-gray-600">Tasks phức tạp tự động</p>
                    </div>
                    <div class="bg-white/90 backdrop-blur-sm p-6 rounded-xl border-l-4 border-blue-500">
                        <h3 class="font-bold text-blue-700 mb-2 flex items-center gap-2">
                            <i class="fas fa-layer-group"></i>
                            Multimodal
                        </h3>
                        <p class="text-sm text-gray-600">Text, image, audio, video</p>
                    </div>
                    <div class="bg-white/90 backdrop-blur-sm p-6 rounded-xl border-l-4 border-green-500">
                        <h3 class="font-bold text-green-700 mb-2 flex items-center gap-2">
                            <i class="fas fa-brain"></i>
                            Reasoning
                        </h3>
                        <p class="text-sm text-gray-600">Suy luận logic nâng cao</p>
                    </div>
                    <div class="bg-white/90 backdrop-blur-sm p-6 rounded-xl border-l-4 border-orange-500">
                        <h3 class="font-bold text-orange-700 mb-2 flex items-center gap-2">
                            <i class="fas fa-shield-alt"></i>
                            AI Safety
                        </h3>
                        <p class="text-sm text-gray-600">An toàn & Alignment</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 13: Kết luận & Khuyến nghị -->
    <div class="slide w-full h-screen bg-gradient-to-br from-gray-50 to-slate-100 flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full">
            <div class="col-span-4 flex flex-col justify-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Kết luận</h1>
                <h2 class="text-xl text-gray-600 mb-8">Khuyến nghị sử dụng</h2>
                <div class="space-y-4">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-lightbulb text-yellow-600 text-2xl"></i>
                        <span class="font-medium text-lg">Chọn đúng công cụ</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-flask text-blue-600 text-2xl"></i>
                        <span class="font-medium text-lg">Thử nghiệm đa dạng</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-graduation-cap text-green-600 text-2xl"></i>
                        <span class="font-medium text-lg">Học cách prompt</span>
                    </div>
                </div>
            </div>
            <div class="col-span-8 flex items-center justify-center">
                <div class="grid grid-cols-2 gap-8 w-full max-w-3xl">
                    <div class="space-y-4">
                        <h3 class="font-bold text-lg text-gray-800 mb-4">🎯 Công cụ phù hợp</h3>
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg border-l-4 border-green-500">
                                <p class="font-medium text-green-700">ChatGPT</p>
                                <p class="text-sm text-gray-600">Đàm thoại & Coding</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg border-l-4 border-purple-500">
                                <p class="font-medium text-purple-700">Perplexity</p>
                                <p class="text-sm text-gray-600">Nghiên cứu có nguồn</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg border-l-4 border-orange-500">
                                <p class="font-medium text-orange-700">Grok</p>
                                <p class="text-sm text-gray-600">Real-time data</p>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <h3 class="font-bold text-lg text-gray-800 mb-4">💡 Best Practices</h3>
                        <div class="space-y-3">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <p class="text-sm font-medium text-blue-700 mb-1">Thử nghiệm</p>
                                <p class="text-xs text-blue-600">Đa dạng công cụ</p>
                            </div>
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <p class="text-sm font-medium text-yellow-700 mb-1">Xác minh</p>
                                <p class="text-xs text-yellow-600">Kiểm tra thông tin</p>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <p class="text-sm font-medium text-green-700 mb-1">Prompt</p>
                                <p class="text-xs text-green-600">Kỹ năng quan trọng</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 14: Thank You -->
    <div class="slide w-full h-screen bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center p-8 overflow-hidden"
        style="aspect-ratio: 16/9;">
        <div class="max-w-4xl mx-auto text-center text-white">
            <i class="fas fa-heart text-6xl mb-8 opacity-80"></i>
            <h1 class="text-5xl font-bold mb-8">Cảm ơn!</h1>
            <p class="text-xl mb-8">Hỏi đáp & Thảo luận</p>
            <div class="flex justify-center space-x-8 text-lg">
                <div class="flex items-center gap-2">
                    <i class="fas fa-envelope"></i>
                    <span><EMAIL></span>
                </div>
                <div class="flex items-center gap-2">
                    <i class="fas fa-globe"></i>
                    <span>www.manusai.com</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Wait for DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function () {
            // Slide navigation
            let currentSlide = 0;
            const slides = document.querySelectorAll('.slide');
            const totalSlides = slides.length;
            let chartsInitialized = {};

            console.log(`Found ${totalSlides} slides`);

            function showSlide(n) {
                if (slides.length === 0) {
                    console.error('No slides found!');
                    return;
                }

                // Calculate new slide index
                const newSlideIndex = ((n % totalSlides) + totalSlides) % totalSlides;

                if (newSlideIndex === currentSlide) {
                    return; // No change needed
                }

                // Add fading class and fade out current slide
                slides[currentSlide].classList.add('fading-out');

                // After fade out completes, switch slides
                setTimeout(() => {
                    slides[currentSlide].classList.remove('active', 'fading-out');
                    currentSlide = newSlideIndex;
                    slides[currentSlide].classList.add('active');
                }, 300);

                // Update counter
                const counter = document.getElementById('slideCounter');
                if (counter) {
                    counter.textContent = `${newSlideIndex + 1} / ${totalSlides}`;
                }

                console.log(`Showing slide ${newSlideIndex + 1}`);

                // Initialize charts for specific slides (only once)
                if (newSlideIndex === 2 && !chartsInitialized['aiGrowth']) {
                    setTimeout(() => {
                        initAIGrowthChart();
                        chartsInitialized['aiGrowth'] = true;
                    }, 600);
                }
                if (newSlideIndex === 8 && !chartsInitialized['comparison']) {
                    setTimeout(() => {
                        initComparisonChart();
                        chartsInitialized['comparison'] = true;
                    }, 400);
                }
            }

            function nextSlide() {
                console.log('Next slide clicked');
                showSlide(currentSlide + 1);
            }

            function prevSlide() {
                console.log('Previous slide clicked');
                showSlide(currentSlide - 1);
            }

            // Event listeners with error handling
            const nextBtn = document.getElementById('nextBtn');
            const prevBtn = document.getElementById('prevBtn');

            if (nextBtn) {
                nextBtn.addEventListener('click', function (e) {
                    e.preventDefault();
                    nextSlide();
                });
                console.log('Next button event listener added');
            } else {
                console.error('Next button not found!');
            }

            if (prevBtn) {
                prevBtn.addEventListener('click', function (e) {
                    e.preventDefault();
                    prevSlide();
                });
                console.log('Previous button event listener added');
            } else {
                console.error('Previous button not found!');
            }

            // Keyboard navigation
            document.addEventListener('keydown', function (e) {
                if (e.key === 'ArrowRight' || e.key === ' ') {
                    e.preventDefault();
                    nextSlide();
                } else if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    prevSlide();
                } else if (e.key === 'Home') {
                    e.preventDefault();
                    showSlide(0);
                } else if (e.key === 'End') {
                    e.preventDefault();
                    showSlide(totalSlides - 1);
                }
            });

            // Chart for AI Growth (Slide 3)
            function initAIGrowthChart() {
                const ctx = document.getElementById('aiGrowthChart');
                if (!ctx) {
                    console.log('AI Growth chart canvas not found');
                    return;
                }

                console.log('Initializing AI Growth chart');

                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['2015', '2018', '2019', '2020', '2022', '2023', '2024', '2025'],
                        datasets: [{
                            label: 'AI Tools Launch',
                            data: [1, 2, 3, 5, 15, 35, 60, 100],
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            title: {
                                display: true,
                                text: 'Sự phát triển của AI Tools',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Số lượng tools'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Năm'
                                }
                            }
                        }
                    }
                });
            }

            // Comparison Chart (Slide 9)
            function initComparisonChart() {
                const ctx = document.getElementById('comparisonChart');
                if (!ctx) {
                    console.log('Comparison chart canvas not found');
                    return;
                }

                console.log('Initializing Comparison chart');

                new Chart(ctx, {
                    type: 'radar',
                    data: {
                        labels: ['Đàm thoại', 'Tìm kiếm', 'Sáng tạo', 'Code', 'Real-time', 'Multimodal'],
                        datasets: [{
                            label: 'ChatGPT',
                            data: [95, 70, 90, 95, 60, 80],
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.2)',
                        }, {
                            label: 'Perplexity',
                            data: [75, 95, 60, 70, 85, 70],
                            borderColor: 'rgb(147, 51, 234)',
                            backgroundColor: 'rgba(147, 51, 234, 0.2)',
                        }, {
                            label: 'Grok',
                            data: [85, 80, 80, 75, 95, 85],
                            borderColor: 'rgb(249, 115, 22)',
                            backgroundColor: 'rgba(249, 115, 22, 0.2)',
                        }, {
                            label: 'Gemini',
                            data: [85, 75, 85, 80, 70, 95],
                            borderColor: 'rgb(6, 182, 212)',
                            backgroundColor: 'rgba(6, 182, 212, 0.2)',
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'So sánh khả năng các AI Tools',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            }
                        },
                        scales: {
                            r: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
            }

            // Initialize first slide
            console.log('Initializing presentation...');
            showSlide(0);
        });
    </script>
</body>

</html>