## Buổi 2: Prompt Engineering / Prompt Design và AI Agent

### 2.1. Prompt Engineering / Prompt Design

Prompt Engineering là quá trình thiết kế và tinh chỉnh các câu lệnh (prompts) để tương tác hiệu quả với các mô hình ngôn ngữ lớn (LLM) và các công cụ AI khác. Mục tiêu là để nhận được kết quả chính xác, phù hợp và hữu ích nhất từ mô hình.

**Các phương pháp hay nhất (Best Practices) trong Prompt Engineering:**

1.  **Sử dụng mô hình mới nhất:** Các mô hình mới hơn thường dễ dàng hơn trong việc thực hiện prompt engineering và cho kết quả tốt hơn.

2.  **Đặt hướng dẫn ở đầu prompt và sử dụng `###` hoặc `"""` để phân tách hướng dẫn và ngữ cảnh:** Điều này giúp mô hình dễ dàng nhận diện đâu là hướng dẫn và đâu là dữ liệu đầu vào.
    *   **Ví dụ:**
        ```
        Tóm tắt văn bản dưới đây thành một danh sách gạch đầu dòng các điểm quan trọng nhất.

        Văn bản: """
        {văn bản đầu vào}
        """
        ```

3.  **Cụ thể, mô tả và chi tiết nhất có thể về ngữ cảnh, kết quả mong muốn, độ dài, định dạng, phong cách, v.v.:** Càng chi tiết, mô hình càng dễ hiểu và tạo ra kết quả chính xác.
    *   **Ví dụ:** Thay vì 


        `Viết một bài thơ về OpenAI.`
        Hãy viết:
        `Viết một bài thơ truyền cảm hứng ngắn gọn về OpenAI, tập trung vào việc ra mắt sản phẩm DALL-E gần đây (DALL-E là mô hình ML chuyển văn bản thành hình ảnh) theo phong cách của {một nhà thơ nổi tiếng}.`

4.  **Trình bày định dạng đầu ra mong muốn thông qua các ví dụ:** Mô hình phản hồi tốt hơn khi được chỉ ra các yêu cầu định dạng cụ thể. Điều này cũng giúp dễ dàng phân tích cú pháp nhiều đầu ra một cách đáng tin cậy.
    *   **Ví dụ:**
        ```
        Trích xuất các thực thể được đề cập trong văn bản dưới đây. Đầu tiên trích xuất tất cả tên công ty, sau đó trích xuất tất cả tên người, sau đó trích xuất các chủ đề cụ thể phù hợp với nội dung và cuối cùng trích xuất các chủ đề tổng thể.

        Định dạng mong muốn:
        Tên công ty: <danh_sách_tên_công_ty_phân_tách_bằng_dấu_phẩy>
        Tên người: -||-
        Chủ đề cụ thể: -||-
        Chủ đề chung: -||-

        Văn bản: {văn bản}
        ```

5.  **Bắt đầu với zero-shot, sau đó few-shot, nếu cả hai đều không hiệu quả thì fine-tune:**
    *   **Zero-shot:** Cung cấp prompt mà không có ví dụ nào.
        *   **Ví dụ:**
            ```
            Trích xuất từ khóa từ văn bản dưới đây.

            Văn bản: {văn bản}

            Từ khóa:
            ```
    *   **Few-shot:** Cung cấp một vài ví dụ trong prompt.
        *   **Ví dụ:**
            ```
            Trích xuất từ khóa từ các văn bản tương ứng dưới đây.

            Văn bản 1: Stripe cung cấp các API mà các nhà phát triển web có thể sử dụng để tích hợp xử lý thanh toán vào các trang web và ứng dụng di động của họ.
            Từ khóa 1: Stripe, xử lý thanh toán, API, nhà phát triển web, trang web, ứng dụng di động
            ##
            Văn bản 2: OpenAI đã huấn luyện các mô hình ngôn ngữ tiên tiến rất giỏi trong việc hiểu và tạo văn bản. API của chúng tôi cung cấp quyền truy cập vào các mô hình này và có thể được sử dụng để giải quyết hầu hết mọi tác vụ liên quan đến xử lý ngôn ngữ.
            Từ khóa 2: OpenAI, mô hình ngôn ngữ, xử lý văn bản, API.
            ##
            Văn bản 3: {văn bản}
            Từ khóa 3:
            ```
    *   **Fine-tune:** Nếu zero-shot và few-shot không mang lại kết quả mong muốn, hãy xem xét fine-tune mô hình với tập dữ liệu cụ thể của bạn.

6.  **Giảm các mô tả “rườm rà” và không chính xác:** Thay vì nói `Mô tả sản phẩm này nên khá ngắn gọn, chỉ vài câu, và không quá nhiều.`, hãy nói `Sử dụng một đoạn văn từ 3 đến 5 câu để mô tả sản phẩm này.`

7.  **Thay vì chỉ nói những gì không nên làm, hãy nói những gì nên làm:**
    *   **Ví dụ:** Thay vì:
        ```
        Sau đây là cuộc trò chuyện giữa một Agent và một Khách hàng. KHÔNG HỎI TÊN NGƯỜI DÙNG HOẶC MẬT KHẨU. KHÔNG LẶP LẠI.

        Khách hàng: Tôi không thể đăng nhập vào tài khoản của mình.
        Agent:
        ```
        Hãy viết:
        ```
        Sau đây là cuộc trò chuyện giữa một Agent và một Khách hàng. Agent sẽ cố gắng chẩn đoán vấn đề và đề xuất giải pháp, đồng thời tránh hỏi bất kỳ câu hỏi nào liên quan đến PII (Thông tin nhận dạng cá nhân). Thay vì hỏi PII, chẳng hạn như tên người dùng hoặc mật khẩu, hãy giới thiệu người dùng đến bài viết trợ giúp www.samplewebsite.com/help/faq

        Khách hàng: Tôi không thể đăng nhập vào tài khoản của mình.
        Agent:
        ```

8.  **Đối với tạo mã (Code Generation) - Sử dụng “từ dẫn đầu” để định hướng mô hình theo một mẫu cụ thể:**
    *   **Ví dụ:** Để tạo một hàm Python chuyển đổi dặm sang kilômét, thay vì chỉ mô tả, hãy bắt đầu bằng `import` để gợi ý mô hình viết mã Python.
        ```python
        # Viết một hàm python đơn giản mà
        # 1. Hỏi tôi một số bằng dặm
        # 2. Nó chuyển đổi dặm sang kilômét
        import
        ```

**Các tham số quan trọng trong Prompt Engineering:**

*   **`model`:** Các mô hình hiệu suất cao hơn thường đắt hơn và có thể có độ trễ cao hơn.
*   **`temperature`:** Một thước đo mức độ thường xuyên mô hình xuất ra một token ít có khả năng xảy ra hơn. `temperature` càng cao, đầu ra càng ngẫu nhiên (và thường sáng tạo) hơn. Tuy nhiên, điều này không giống với “tính xác thực”. Đối với hầu hết các trường hợp sử dụng thực tế như trích xuất dữ liệu và Q&A chính xác, `temperature` bằng 0 là tốt nhất.
*   **`max_completion_tokens` (độ dài tối đa):** Không kiểm soát độ dài của đầu ra, nhưng là giới hạn cắt cứng cho việc tạo token. Lý tưởng nhất là bạn sẽ không thường xuyên đạt đến giới hạn này, vì mô hình của bạn sẽ dừng lại khi nó nghĩ rằng đã hoàn thành hoặc khi nó đạt đến một chuỗi dừng mà bạn đã xác định.
*   **`stop` (chuỗi dừng):** Một tập hợp các ký tự (token) mà khi được tạo ra, sẽ khiến quá trình tạo văn bản dừng lại.



### 2.2. AI Agent

AI Agent (hoặc tác nhân AI) là một hệ thống hoặc chương trình trí tuệ nhân tạo có khả năng tự chủ thực hiện các tác vụ thay mặt người dùng hoặc một hệ thống khác bằng cách thiết kế quy trình làm việc và sử dụng các công cụ có sẵn. AI Agent có thể bao gồm nhiều chức năng ngoài xử lý ngôn ngữ tự nhiên, bao gồm ra quyết định, giải quyết vấn đề, tương tác với môi trường bên ngoài và thực hiện các hành động.

Các tác nhân này có thể được triển khai trong nhiều ứng dụng để giải quyết các tác vụ phức tạp trong các ngữ cảnh doanh nghiệp khác nhau, từ thiết kế phần mềm và tự động hóa CNTT đến các công cụ tạo mã và trợ lý đàm thoại. Chúng sử dụng các kỹ thuật xử lý ngôn ngữ tự nhiên tiên tiến của các mô hình ngôn ngữ lớn (LLM) để hiểu và phản hồi các đầu vào của người dùng từng bước và xác định khi nào cần gọi các công cụ bên ngoài.

**Cách AI Agent hoạt động:**

Cốt lõi của AI Agent là các mô hình ngôn ngữ lớn (LLM). Vì lý do này, AI Agent thường được gọi là LLM Agent. Các LLM truyền thống tạo ra phản hồi dựa trên dữ liệu được sử dụng để huấn luyện chúng và bị giới hạn bởi kiến thức và khả năng suy luận. Ngược lại, công nghệ Agentic sử dụng tính năng gọi công cụ (tool calling) ở phần backend để thu thập thông tin cập nhật, tối ưu hóa quy trình làm việc và tự động tạo các tác vụ phụ để đạt được các mục tiêu phức tạp.

Trong quá trình này, tác nhân tự chủ học cách thích nghi với kỳ vọng của người dùng theo thời gian. Khả năng lưu trữ các tương tác trong quá khứ vào bộ nhớ và lập kế hoạch cho các hành động trong tương lai khuyến khích trải nghiệm cá nhân hóa và phản hồi toàn diện. Tính năng gọi công cụ này có thể đạt được mà không cần sự can thiệp của con người và mở rộng khả năng ứng dụng thực tế của các hệ thống AI này. Các Agent có thể được định nghĩa bởi ba giai đoạn hoặc các thành phần Agentic sau:

1.  **Khởi tạo mục tiêu và lập kế hoạch:** Mặc dù AI Agent tự chủ trong quá trình ra quyết định, chúng yêu cầu các mục tiêu và quy tắc được xác định trước bởi con người. Có ba yếu tố chính ảnh hưởng đến hành vi của tác nhân tự chủ:
    *   Nhóm nhà phát triển thiết kế và huấn luyện hệ thống AI Agent.
    *   Nhóm triển khai Agent và cung cấp quyền truy cập cho người dùng.
    *   Người dùng cung cấp cho AI Agent các mục tiêu cụ thể cần hoàn thành và thiết lập các công cụ có sẵn để sử dụng.

    Với các mục tiêu của người dùng và các công cụ có sẵn của Agent, AI Agent sau đó thực hiện phân tách tác vụ để cải thiện hiệu suất. Về cơ bản, Agent tạo ra một kế hoạch gồm các tác vụ và tác vụ phụ cụ thể để hoàn thành mục tiêu phức tạp. Đối với các tác vụ đơn giản, lập kế hoạch không phải là một bước cần thiết. Thay vào đó, một Agent có thể lặp đi lặp lại phản hồi của mình và cải thiện chúng mà không cần lập kế hoạch các bước tiếp theo.

2.  **Suy luận bằng cách sử dụng các công cụ có sẵn:** AI Agent dựa trên các hành động của chúng vào thông tin mà chúng nhận thức được. Thông thường, AI Agent không có đầy đủ cơ sở kiến thức cần thiết để giải quyết tất cả các tác vụ phụ trong một mục tiêu phức tạp. Để khắc phục điều này, AI Agent sử dụng các công cụ có sẵn của chúng. Các công cụ này có thể bao gồm các tập dữ liệu bên ngoài, tìm kiếm web, API và thậm chí cả các Agent khác. Sau khi thông tin bị thiếu được truy xuất từ các công cụ này, Agent có thể cập nhật cơ sở kiến thức của mình và thực hiện suy luận Agentic. Điều này có nghĩa là ở mỗi bước, Agent đánh giá lại kế hoạch hành động của mình và tự điều chỉnh, cho phép ra quyết định có thông tin.

3.  **Học hỏi và phản ánh:** AI Agent sử dụng các cơ chế phản hồi, chẳng hạn như các AI Agent khác và con người trong vòng lặp (HITL), để cải thiện độ chính xác của phản hồi. Các cơ chế phản hồi cải thiện khả năng suy luận và độ chính xác của AI Agent, thường được gọi là tinh chỉnh lặp lại. Để tránh lặp lại những sai lầm tương tự, AI Agent cũng có thể lưu trữ dữ liệu về các giải pháp cho các trở ngại trước đó trong một cơ sở kiến thức.

**AI Chatbot Agentic so với Non-Agentic:**

*   **AI Chatbot Non-Agentic:** Là những chatbot không có công cụ, bộ nhớ và khả năng suy luận. Chúng chỉ có thể đạt được các mục tiêu ngắn hạn và không thể lập kế hoạch trước. Chúng yêu cầu đầu vào liên tục từ người dùng để phản hồi. Chúng có thể tạo ra phản hồi cho các prompt phổ biến phù hợp với kỳ vọng của người dùng nhưng hoạt động kém hiệu quả đối với các câu hỏi đặc trưng của người dùng và dữ liệu của họ. Vì những chatbot này không giữ bộ nhớ, chúng không thể học hỏi từ những sai lầm của mình nếu phản hồi không đạt yêu cầu.

*   **AI Chatbot Agentic:** Học cách thích nghi với kỳ vọng của người dùng theo thời gian, mang lại trải nghiệm cá nhân hóa hơn và phản hồi toàn diện. Chúng có thể hoàn thành các tác vụ phức tạp bằng cách tạo các tác vụ phụ mà không cần sự can thiệp của con người và xem xét các kế hoạch khác nhau. Các kế hoạch này cũng có thể được tự điều chỉnh và cập nhật khi cần. AI Chatbot Agentic, không giống như các chatbot non-Agentic, đánh giá các công cụ của chúng và sử dụng các tài nguyên có sẵn để lấp đầy khoảng trống thông tin.



### 2.3. Tài liệu tham khảo/học tập chuyên sâu về Prompt Engineering

Dưới đây là một số tài liệu tham khảo và học tập chuyên sâu về Prompt Engineering từ các nguồn uy tín:

*   **OpenAI:**
    *   [Best practices for prompt engineering with the OpenAI API](https://help.openai.com/en/articles/6654000-best-practices-for-prompt-engineering-with-the-openai-api)
    *   [OpenAI - Guide to Prompt Engineering](https://platform.openai.com/docs/guides/prompt-engineering)

*   **Microsoft (Azure OpenAI):**
    *   [Prompt engineering techniques - Azure OpenAI](https://learn.microsoft.com/en-us/azure/ai-services/openai/concepts/prompt-engineering)
    *   [Azure OpenAI Essentials | Data | eBook](https://www.packtpub.com/en-us/product/azure-openai-essentials-9781805122654?srsltid=AfmBOopYgkCLv4w__ZzHLQSU9CXZrmAQbd8oUXwgEAr7JPidkgU3f0MW) (Đây là một cuốn sách, có thể cần mua)

*   **Google:**
    *   [Best practices for prompt engineering | Google Cloud Blog](https://cloud.google.com/blog/products/application-development/five-best-practices-for-prompt-engineering)

*   **Anthropic:**
    *   [Prompt engineering overview - Anthropic](https://docs.anthropic.com/en/docs/build-with-claude/prompt-engineering/overview)
    *   [Anthropic's Interactive Prompt Engineering Tutorial (GitHub)](https://github.com/anthropics/prompt-eng-interactive-tutorial)

*   **Tài liệu tổng hợp khác:**
    *   [Prompt Engineering Guide: The Ultimate Guide to Prompt Engineering](https://learnprompting.org/docs/introduction?srsltid=AfmBOoqlJxNy1Pj8_qJuFwP8424xyHHHR2R_8mniczxaVRq18HVAn0mI) (Hướng dẫn toàn diện, miễn phí)


