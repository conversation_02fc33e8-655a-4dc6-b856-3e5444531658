
### 1.1. Large Language Model (LLM)

LLM là một chương trình trí tuệ nhân tạo (AI) có khả năng nhận diện và tạo văn bản, cùng nhiều tác vụ khác. LLM được huấn luyện trên các tập dữ liệu khổng lồ (hàng nghìn đến hàng triệu gigabyte văn bản thu thập từ Internet hoặc các tập dữ liệu được chọn lọc kỹ càng). LLM được xây dựng dựa trên học máy (machine learning), cụ thể là một loại mạng nơ-ron gọi là mô hình transformer.

LLM sử dụng học sâu (deep learning) để hiểu cách các ký tự, từ và câu hoạt động cùng nhau. <PERSON><PERSON><PERSON> sâu liên quan đến phân tích xác suất dữ liệu phi cấu trúc, gi<PERSON><PERSON> mô hình học sâu nhận diện sự khác biệt giữa các phần nội dung mà không cần sự can thiệp của con người.

Sau đó, LLM được tinh chỉnh (fine-tuned) hoặc điều chỉnh prompt (prompt-tuned) cho các tác vụ cụ thể mà lập trình viên mong muốn, chẳng hạn như diễn giải câu hỏi và tạo phản hồi, hoặc dịch văn bản từ ngôn ngữ này sang ngôn ngữ khác.

**Ứng dụng của LLM:**

*   **AI tạo sinh (Generative AI):** Tạo văn bản phản hồi khi được cung cấp prompt hoặc câu hỏi (ví dụ: ChatGPT tạo bài luận, thơ).
*   **Hỗ trợ lập trình:** Viết mã, hoàn thành chương trình, viết hàm (ví dụ: GitHub Copilot).
*   **Phân tích cảm xúc (Sentiment analysis).**
*   **Nghiên cứu DNA.**
*   **Dịch vụ khách hàng.**
*   **Chatbot.**
*   **Tìm kiếm trực tuyến.**

**Ví dụ về các LLM phổ biến:** ChatGPT (OpenAI), Bard/Gemini (Google), Llama (Meta), Bing Chat (Microsoft).

**Cách hoạt động của LLM:**

*   **Học máy và học sâu:** LLM được xây dựng trên học máy, sử dụng học sâu để tự học cách nhận diện sự khác biệt trong dữ liệu. Học sâu sử dụng xác suất để 'học'.
*   **Mạng nơ-ron:** LLM được xây dựng trên mạng nơ-ron, mô phỏng cấu trúc não bộ con người với các nút mạng kết nối với nhau, bao gồm lớp đầu vào, lớp đầu ra và các lớp ẩn.
*   **Mô hình Transformer:** Là loại mạng nơ-ron cụ thể được sử dụng cho LLM, có khả năng học ngữ cảnh rất tốt nhờ kỹ thuật tự chú ý (self-attention). Điều này giúp chúng hiểu được mối quan hệ giữa các yếu tố trong một chuỗi, từ đó diễn giải ngôn ngữ con người một cách hiệu quả, ngay cả khi ngôn ngữ đó mơ hồ hoặc được sắp xếp theo cách mới.

**Ưu điểm và hạn chế của LLM:**

*   **Ưu điểm:** Khả năng phản hồi các truy vấn không có cấu trúc bằng ngôn ngữ tự nhiên, sử dụng phân tích dữ liệu để đưa ra câu trả lời hợp lý.
*   **Hạn chế:**
    *   **Độ tin cậy của dữ liệu:** LLM chỉ đáng tin cậy như dữ liệu mà chúng được nạp vào. Nếu dữ liệu đầu vào sai, chúng sẽ đưa ra thông tin sai lệch.
    *   **Ảo giác (Hallucination):** LLM đôi khi tạo ra thông tin giả mạo khi không thể đưa ra câu trả lời chính xác.
    *   **Bảo mật:** Các ứng dụng dựa trên LLM dễ bị lỗi như bất kỳ ứng dụng nào khác. Chúng có thể bị thao túng thông qua các đầu vào độc hại để đưa ra các phản hồi nguy hiểm hoặc phi đạo đức. Ngoài ra, người dùng có thể tải dữ liệu bảo mật, bí mật vào LLM để tăng năng suất, nhưng LLM sử dụng các đầu vào này để huấn luyện mô hình của chúng và không được thiết kế để lưu trữ an toàn; chúng có thể tiết lộ dữ liệu bí mật khi phản hồi các truy vấn từ người dùng khác.



### 1.2. Trí tuệ nhân tạo (AI)

Trí tuệ nhân tạo (AI) là công nghệ cho phép máy tính và máy móc mô phỏng khả năng học hỏi, hiểu biết, giải quyết vấn đề, ra quyết định, sáng tạo và tự chủ của con người.

Các ứng dụng và thiết bị được trang bị AI có thể nhìn và nhận diện đối tượng, hiểu và phản hồi ngôn ngữ con người, học hỏi từ thông tin và kinh nghiệm mới, đưa ra các khuyến nghị chi tiết cho người dùng và chuyên gia, và hoạt động độc lập thay thế sự can thiệp của con người (ví dụ: xe tự lái).

**Các khái niệm liên quan đến AI:**

*   **Học máy (Machine Learning - ML):** Là một nhánh của AI, liên quan đến việc tạo ra các mô hình bằng cách huấn luyện một thuật toán để đưa ra dự đoán hoặc quyết định dựa trên dữ liệu. ML bao gồm nhiều kỹ thuật cho phép máy tính học hỏi và suy luận từ dữ liệu mà không cần được lập trình rõ ràng cho các tác vụ cụ thể.
    *   **Các kỹ thuật học máy phổ biến:** Hồi quy tuyến tính, hồi quy logistic, cây quyết định, rừng ngẫu nhiên, máy vector hỗ trợ (SVMs), k-láng giềng gần nhất (KNN), phân cụm.
    *   **Mạng nơ-ron (Neural Network):** Là một loại thuật toán học máy phổ biến, mô phỏng cấu trúc và chức năng của não bộ con người. Mạng nơ-ron bao gồm các lớp nút (tương tự nơ-ron) được kết nối với nhau để xử lý và phân tích dữ liệu phức tạp. Chúng phù hợp với các tác vụ liên quan đến việc xác định các mẫu và mối quan hệ phức tạp trong lượng lớn dữ liệu.
    *   **Học có giám sát (Supervised Learning):** Sử dụng các tập dữ liệu đã được gán nhãn để huấn luyện thuật toán phân loại dữ liệu hoặc dự đoán kết quả. Mục tiêu là mô hình học được ánh xạ giữa đầu vào và đầu ra trong dữ liệu huấn luyện để có thể dự đoán nhãn của dữ liệu mới, chưa từng thấy.

*   **Học sâu (Deep Learning - DL):** Là một tập hợp con của học máy, sử dụng mạng nơ-ron đa lớp (gọi là mạng nơ-ron sâu) mô phỏng chặt chẽ hơn khả năng ra quyết định phức tạp của não bộ con người.
    *   Mạng nơ-ron sâu bao gồm một lớp đầu vào, ít nhất ba nhưng thường là hàng trăm lớp ẩn, và một lớp đầu ra, không giống như mạng nơ-ron trong các mô hình học máy cổ điển thường chỉ có một hoặc hai lớp ẩn.
    *   Các lớp này cho phép **học không giám sát (unsupervised learning)**: chúng có thể tự động trích xuất các đặc trưng từ các tập dữ liệu lớn, không được gán nhãn và phi cấu trúc, và đưa ra dự đoán của riêng chúng về những gì dữ liệu đại diện.
    *   DL phù hợp với xử lý ngôn ngữ tự nhiên (NLP), thị giác máy tính (computer vision) và các tác vụ khác liên quan đến việc nhận diện nhanh chóng, chính xác các mẫu và mối quan hệ phức tạp trong lượng lớn dữ liệu.
    *   **Các loại học sâu khác:** Học bán giám sát (Semi-supervised learning), học tự giám sát (Self-supervised learning), học tăng cường (Reinforcement learning), học chuyển giao (Transfer learning).

*   **AI tạo sinh (Generative AI - Gen AI):** Đôi khi được gọi là "gen AI", đề cập đến các mô hình học sâu có thể tạo ra nội dung gốc phức tạp như văn bản dài, hình ảnh chất lượng cao, video hoặc âm thanh chân thực và nhiều hơn nữa để phản hồi prompt hoặc yêu cầu của người dùng.
    *   Các mô hình tạo sinh mã hóa một biểu diễn đơn giản hóa của dữ liệu huấn luyện của chúng, và sau đó dựa vào biểu diễn đó để tạo ra tác phẩm mới tương tự, nhưng không giống hệt, dữ liệu gốc.
    *   Các mô hình tạo sinh đã được sử dụng trong nhiều năm trong thống kê để phân tích dữ liệu số. Nhưng trong thập kỷ qua, chúng đã phát triển để phân tích và tạo ra các loại dữ liệu phức tạp hơn.
    *   **Các loại mô hình học sâu tinh vi:** Bộ mã hóa tự động biến phân (Variational autoencoders - VAEs), mô hình khuếch tán (Diffusion models).



### 1.3. Các khái niệm và thuật ngữ liên quan

*   **Prompt:** Là một đoạn văn bản đầu vào được cung cấp cho mô hình AI để hướng dẫn nó tạo ra một phản hồi mong muốn. Prompt có thể là một câu hỏi, một câu lệnh, một đoạn văn bản để hoàn thành, hoặc bất kỳ hình thức hướng dẫn nào khác.

*   **Agent (AI Agent):** Là một hệ thống AI có khả năng cảm nhận môi trường, ra quyết định và thực hiện hành động để đạt được mục tiêu cụ thể. AI Agent có thể tự động hóa các tác vụ phức tạp, tương tác với các công cụ khác và học hỏi từ kinh nghiệm.

*   **Transformer:** Là một kiến trúc mạng nơ-ron được giới thiệu vào năm 2017, đặc biệt hiệu quả trong việc xử lý dữ liệu tuần tự như ngôn ngữ tự nhiên. Transformer sử dụng cơ chế tự chú ý (self-attention) để đánh giá mức độ quan trọng của các phần khác nhau trong dữ liệu đầu vào khi tạo ra đầu ra, giúp nó hiểu ngữ cảnh tốt hơn so với các mô hình trước đây.

*   **Token:** Là đơn vị cơ bản của văn bản mà mô hình AI xử lý. Token có thể là một từ, một phần của từ, một ký tự, hoặc một dấu câu. Các mô hình LLM phân tích và tạo ra văn bản dựa trên các chuỗi token này.

*   **Temperature:** Là một siêu tham số (hyperparameter) trong các mô hình AI (đặc biệt là LLM) kiểm soát mức độ ngẫu nhiên của các dự đoán trong quá trình tạo văn bản. Giá trị temperature cao hơn sẽ tạo ra kết quả đa dạng và sáng tạo hơn, trong khi giá trị thấp hơn sẽ tạo ra kết quả tập trung và ít ngẫu nhiên hơn.
    *   **Cách hoạt động:** Temperature được áp dụng như một số chia cho các logits (đầu ra thô của lớp cuối cùng của mô hình) trước khi chúng được đưa qua hàm softmax. Khi temperature tăng, phân phối xác suất trở nên đồng đều hơn (mềm hơn), nghĩa là tất cả các lớp hoặc từ/token có xác suất tương tự nhau. Ngược lại, khi temperature giảm, phân phối trở nên tập trung hơn, với một hoặc vài lớp có xác suất cao trong khi các lớp khác có xác suất rất thấp.

*   **Fine-tune model (Tinh chỉnh mô hình):** Là quá trình tiếp tục huấn luyện một mô hình AI đã được huấn luyện trước (pre-trained model) trên một tập dữ liệu nhỏ hơn, cụ thể hơn cho một tác vụ hoặc miền nhất định. Mục tiêu của fine-tuning là điều chỉnh mô hình để nó hoạt động tốt hơn trên tác vụ cụ thể đó mà không cần phải huấn luyện lại từ đầu.


