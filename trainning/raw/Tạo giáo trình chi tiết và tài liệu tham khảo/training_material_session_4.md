## Buổi 4: Trainning về cách build custom Agent / RAG

### 4.1. Build Agent chỉ bằng Prompt (CustomGPT, CustomGem)

Trong thế giới AI, ba nền tảng lớn – Custom GPT của OpenAI, Claude Projects của Anthropic và Gemini Gems của Google – là những cách nhanh chóng và dễ dàng để tương tác với các mô hình ngôn ngữ lớn (LLM). <PERSON>á<PERSON> công cụ này cung cấp một cách nhanh chóng để tạo ra các tác nhân AI nhỏ được tùy chỉnh, dành riêng cho tác vụ và khá hữu ích.

**Các bước xây dựng Custom GPT, Project hoặc Gem:**

1.  **Bắt đầu với một trường hợp sử dụng:** Xác định rõ ràng tác vụ mà tác nhân AI của bạn sẽ giải quyết. Càng hẹp và được xác định rõ ràng, càng tốt. Ví dụ: một công cụ sàng lọc giao dịch được huấn luyện theo tiêu chí đầu tư của bạn, một trợ lý email soạn thảo phản hồi theo giọng điệu của bạn, hoặc một bot cho thuê được huấn luyện theo chính sách của công ty bạn.

2.  **Đặt hướng dẫn tùy chỉnh:** Sử dụng một khung prompt có cấu trúc như RODES:
    *   **Role (R) – Vai trò:** Xác định tính cách của AI. Cụ thể về việc trợ lý là ai và giọng điệu mà nó nên sử dụng.
    *   **Objective (O) – Mục tiêu:** Nêu rõ mục tiêu. Người dùng đang cố gắng đạt được điều gì với tương tác này?
    *   **Details (D) – Chi tiết:** Cung cấp bản thiết kế để thành công. Bao gồm các yêu cầu, ràng buộc và ngữ cảnh.
    *   **Examples (E) – Ví dụ:** Bao gồm các ví dụ đầu vào và đầu ra mong muốn. Điều này giúp điều chỉnh phản hồi của AI với kỳ vọng.
    *   **Sense Check (S) – Kiểm tra lại:** Yêu cầu AI xác nhận sự hiểu biết trước khi tiếp tục. Điều này giảm thiểu sự hiểu sai và đảm bảo độ chính xác.

3.  **Tải lên kiến thức hỗ trợ:** Đây là nơi AI của bạn trở nên thông minh. Bạn có thể tải lên:
    *   Các ví dụ về công việc trước đây (ví dụ: bản ghi nhớ đầu tư, đánh giá giao dịch).
    *   Tài liệu quy trình.
    *   Câu hỏi thường gặp hoặc tài nguyên khách hàng.
    Đảm bảo hướng dẫn của bạn nêu rõ khi nào và cách sử dụng các tệp này. Lựa chọn chúng cẩn thận – chúng đóng vai trò là một tập hợp các prompt thứ cấp.

4.  **Thêm các prompt khởi đầu:** Cung cấp cho người dùng một khởi đầu thuận lợi. Ví dụ: `Giúp tôi sàng lọc giao dịch bất động sản này.`, `Viết một LOI cho một tài sản bán lẻ.`, `Tỷ lệ vốn hóa cho khoản đầu tư này là bao nhiêu?`

5.  **Chọn khả năng:** Quyết định các khả năng bổ sung mà tác nhân của bạn nên có, chẳng hạn như:
    *   **Code Interpreter:** Cần thiết để làm việc với bảng tính, PDF và dữ liệu.
    *   **Web Browsing:** Nếu GPT của bạn cần dữ liệu thời gian thực.
    *   **Image Generator:** Tuyệt vời cho thiết kế, xây dựng thương hiệu hoặc mô hình.
    *   **Canvas:** Lý tưởng cho các công cụ không gian và bản vẽ.

6.  **Kiểm thử và lặp lại trong bản xem trước:** Đừng bỏ qua bước này. Hãy thử nghiệm:
    *   Thử các đầu vào phổ biến của người dùng.
    *   Xác minh xem nó có sử dụng đúng giọng điệu và kiến thức hay không.
    *   Kiểm tra xem nó có nằm trong phạm vi hay không.
    Tinh chỉnh hướng dẫn và tải lên kiến thức được quản lý tốt hơn nếu cần.

**Mẹo chuyên nghiệp: Thêm tín hiệu để làm cho nó có cảm giác giống con người hơn:**

Bạn muốn làm cho GPT của mình có cảm giác trực quan hơn? Thêm một tệp có tên `user_signals.txt`. Trong đó, xác định các “tín hiệu” của người dùng như sự thất vọng, bối rối hoặc khen ngợi và bao gồm các kiểu phản hồi được đề xuất. Điều này giúp GPT tự động thích nghi, trấn an hoặc làm rõ.

**Ví dụ:**

*   **Tín hiệu:** Người dùng bối rối.
    **Phản hồi:** “Hoàn toàn hiểu điều đó. Bạn có muốn cùng xem qua một ví dụ không?”
*   **Tín hiệu:** Người dùng bày tỏ sự thất vọng.
    **Phản hồi:** “Có vẻ như đây là một tình huống khó khăn. Hãy cùng giải quyết từng bước một.”

**Đặt tên:** Tên tác nhân của bạn phải ngắn gọn và mô tả, phù hợp với thương hiệu của bạn.

**Các công cụ cần thiết để đảm bảo chất lượng:** Mục đích rõ ràng, hướng dẫn dựa trên RODES, tệp kiến thức được quản lý và liên quan, các câu hỏi khởi đầu cuộc trò chuyện, các khả năng phù hợp được bật, kiểm thử trong thế giới thực.



### 4.2. RAG (Retrieval Augmented Generation)

Retrieval-Augmented Generation (RAG) là một kỹ thuật kết hợp truy xuất thông tin thần kinh với tạo văn bản thần kinh để cải thiện chất lượng phản hồi được tạo bởi các mô hình ngôn ngữ lớn (LLM). RAG cho phép LLM dựa vào các nguồn kiến thức bên ngoài để bổ sung cho biểu diễn thông tin nội bộ của chúng, cho phép chúng tạo ra các phản hồi chính xác và đáng tin cậy hơn.

**Tại sao RAG quan trọng?**

RAG giải quyết một số thách thức chính với các mô hình ngôn ngữ lớn, bao gồm:

*   **Giới hạn kiến thức:** LLM có kiến thức hạn chế dựa trên những gì chúng được huấn luyện. RAG cung cấp quyền truy cập vào kiến thức bên ngoài, cho phép LLM tạo ra các phản hồi chính xác và đáng tin cậy hơn.
*   **Rủi ro ảo giác:** LLM có thể tạo ra các phản hồi không chính xác về mặt thực tế hoặc không liên quan đến truy vấn. RAG cho phép LLM dựa vào các nguồn kiến thức bên ngoài để bổ sung cho biểu diễn thông tin nội bộ của chúng, giảm nguy cơ ảo giác.
*   **Hạn chế ngữ cảnh:** LLM thiếu ngữ cảnh từ dữ liệu riêng tư, dẫn đến ảo giác khi được hỏi các câu hỏi cụ thể về miền hoặc công ty. RAG cung cấp thông tin cập nhật về thế giới và dữ liệu cụ thể về miền cho các ứng dụng GenAI của bạn, cho phép chúng tạo ra các câu trả lời có nhiều thông tin hơn.
*   **Khả năng kiểm toán:** RAG cho phép GenAI trích dẫn các nguồn của nó và cải thiện khả năng kiểm toán, giúp dễ dàng theo dõi các nguồn thông tin được sử dụng để tạo phản hồi.

**RAG hoạt động như thế nào?**

RAG có hai giai đoạn: truy xuất và tạo nội dung. Trong giai đoạn truy xuất, các thuật toán tìm kiếm và truy xuất các đoạn thông tin liên quan đến prompt hoặc câu hỏi của người dùng. Ngữ cảnh được truy xuất có thể đến từ nhiều nguồn dữ liệu, chẳng hạn như kho tài liệu, cơ sở dữ liệu hoặc API. Ngữ cảnh được truy xuất sau đó được cung cấp làm đầu vào cho mô hình tạo, thường là một mô hình ngôn ngữ lớn (LLM). Mô hình tạo sử dụng ngữ cảnh được truy xuất để định hình đầu ra văn bản được tạo của nó, tạo ra một phản hồi dựa trên các sự kiện và kiến thức liên quan.

Để làm cho các định dạng tương thích, một bộ sưu tập tài liệu, hoặc thư viện kiến thức, và các truy vấn do người dùng gửi được chuyển đổi thành các biểu diễn số bằng cách sử dụng các mô hình ngôn ngữ nhúng (embedding language models). Nhúng là quá trình văn bản được biểu diễn bằng số trong một không gian vector. Các kiến trúc mô hình RAG so sánh các nhúng của truy vấn người dùng trong vector của thư viện kiến thức. Prompt gốc của người dùng sau đó được thêm vào ngữ cảnh liên quan từ các tài liệu tương tự trong thư viện kiến thức. Prompt được tăng cường này sau đó được gửi đến mô hình nền tảng.

**Các phương pháp hay nhất để triển khai RAG:**

*   **Chọn nguồn kiến thức phù hợp:** Chọn các nguồn kiến thức liên quan đến miền của bạn và cung cấp thông tin cập nhật.
*   **Tinh chỉnh LLM của bạn:** Tinh chỉnh LLM của bạn trên miền cụ thể của bạn để cải thiện hiệu suất của nó.
*   **Sử dụng mô hình truy xuất:** Sử dụng mô hình truy xuất để tìm kiếm thông qua các nguồn kiến thức lớn và truy xuất các đoạn ngữ cảnh liên quan cho truy vấn hoặc tác vụ đã cho.
*   **Chuyển đổi dữ liệu sang biểu diễn số:** Chuyển đổi tài liệu của bạn và bất kỳ truy vấn người dùng nào sang định dạng tương thích để thực hiện tìm kiếm liên quan.
*   **Cập nhật thư viện kiến thức:** Cập nhật thư viện kiến thức và các nhúng liên quan của chúng thường xuyên để đảm bảo rằng chúng chứa thông tin mới nhất.

**Sự khác biệt giữa RAG và các kỹ thuật khác như tinh chỉnh (fine-tuning):**

Tinh chỉnh là một kỹ thuật liên quan đến việc huấn luyện một LLM đã được huấn luyện trước trên một tác vụ hoặc miền cụ thể. RAG, mặt khác, liên quan đến việc truy xuất kiến thức bên ngoài để bổ sung cho biểu diễn thông tin nội bộ của LLM. RAG đặc biệt hữu ích để giải quyết giới hạn kiến thức và rủi ro ảo giác.

**Các ứng dụng của RAG:**

RAG có nhiều ứng dụng, bao gồm trả lời câu hỏi, chatbot và dịch vụ khách hàng. RAG có thể được sử dụng để tạo ra các phản hồi chính xác và đáng tin cậy hơn cho các truy vấn của người dùng, cải thiện trải nghiệm người dùng tổng thể.



### 4.3. Hướng dẫn triển khai RAG với Python và LangChain

Một trong những ứng dụng mạnh mẽ nhất được kích hoạt bởi LLM là các chatbot hỏi đáp (Q&A) tinh vi. Đây là các ứng dụng có thể trả lời các câu hỏi về thông tin nguồn cụ thể. Các ứng dụng này sử dụng một kỹ thuật được gọi là Retrieval Augmented Generation, hay RAG.

Một ứng dụng RAG điển hình có hai thành phần chính:

**1. Lập chỉ mục (Indexing):** Một pipeline để nhập dữ liệu từ một nguồn và lập chỉ mục cho nó. _Điều này thường xảy ra ngoại tuyến._

   *   **Tải (Load):** Đầu tiên chúng ta cần tải dữ liệu của mình. Điều này được thực hiện bằng cách sử dụng [Document Loaders](https://python.langchain.com/docs/modules/data_connection/document_loaders/).
   *   **Chia nhỏ (Split):** [Text splitters](https://python.langchain.com/docs/modules/data_connection/document_loaders/) chia nhỏ các `Documents` lớn thành các đoạn nhỏ hơn. Điều này hữu ích cho cả việc lập chỉ mục dữ liệu và truyền nó vào một mô hình, vì các đoạn lớn khó tìm kiếm hơn và sẽ không vừa với cửa sổ ngữ cảnh hữu hạn của mô hình.
   *   **Lưu trữ (Store):** Chúng ta cần một nơi để lưu trữ và lập chỉ mục các đoạn của mình, để chúng có thể được tìm kiếm sau này. Điều này thường được thực hiện bằng cách sử dụng [VectorStore](https://python.langchain.com/docs/modules/data_connection/vectorstores/) và mô hình [Embeddings](https://python.langchain.com/docs/modules/data_connection/text_embedding/).

**2. Truy xuất và tạo (Retrieval and generation):** Chuỗi RAG thực tế, lấy truy vấn của người dùng tại thời điểm chạy và truy xuất dữ liệu liên quan từ chỉ mục, sau đó chuyển dữ liệu đó cho mô hình.

   *   **Truy xuất (Retrieve):** Với đầu vào của người dùng, các đoạn liên quan được truy xuất từ bộ lưu trữ bằng cách sử dụng [Retriever](https://python.langchain.com/docs/modules/data_connection/retrievers/).
   *   **Tạo (Generate):** Một [ChatModel](https://python.langchain.com/docs/modules/model_io/chat/) / [LLM](https://python.langchain.com/docs/modules/model_io/llms/) tạo ra câu trả lời bằng cách sử dụng một prompt bao gồm cả câu hỏi với dữ liệu được truy xuất.

Sau khi chúng ta đã lập chỉ mục dữ liệu của mình, chúng ta sẽ sử dụng [LangGraph](https://langchain-ai.github.io/langgraph/) làm framework điều phối để triển khai các bước truy xuất và tạo.

**Cài đặt các thư viện cần thiết:**

```bash
pip install -qU "langchain[google-genai]"
pip install -qU langchain-openai
pip install -qU langchain-core
```

**Ví dụ mã nguồn cơ bản:**

```python
import bs4
from langchain import hub
from langchain_community.document_loaders import WebBaseLoader
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langgraph.graph import START, StateGraph
from typing_extensions import List, TypedDict

# 1. Tải tài liệu
loader = WebBaseLoader(
    web_paths=("https://lilianweng.github.io/posts/2023-06-23-agent/",),
    bs_kwargs=dict(
        parse_only=bs4.SoupStrainer(
            "div",
            attrs={
                "class": ["post-content", "post-title", "post-header"]
            },
        )
    ),
)
docs = loader.load()

# 2. Chia nhỏ tài liệu
text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
splits = text_splitter.split_documents(docs)

# 3. Lưu trữ tài liệu (sử dụng InMemoryVectorStore để đơn giản)
from langchain_community.vectorstores import Chroma
from langchain_openai import OpenAIEmbeddings

vectorstore = Chroma.from_documents(documents=splits, embedding=OpenAIEmbeddings())

# 4. Khởi tạo Retriever
retriever = vectorstore.as_retriever()

# 5. Khởi tạo LLM
from langchain_openai import ChatOpenAI

llm = ChatOpenAI(model="gpt-3.5-turbo-0125")

# 6. Xây dựng chuỗi RAG
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough

# Tải prompt từ LangChain Hub
prompt = hub.pull("rlm/rag-prompt")

def format_docs(docs):
    return "\n\n".join(doc.page_content for doc in docs)

rag_chain = (
    {"context": retriever | format_docs, "question": RunnablePassthrough()}
    | prompt
    | llm
    | StrOutputParser()
)

# 7. Chạy chuỗi RAG
# response = rag_chain.invoke("What is the agent's primary goal?")
# print(response)
```

**Lưu ý:** Để chạy được ví dụ trên, bạn cần cài đặt `openai` và `tiktoken` (cho `OpenAIEmbeddings` và `ChatOpenAI`), và thiết lập biến môi trường `OPENAI_API_KEY` với khóa API OpenAI của bạn.


