# Gi<PERSON>o trình đào tạo về AI và LLM

## Giớ<PERSON> thiệu

Giáo trình này được thiết kế để cung cấp kiến thức toàn diện về Trí tuệ nhân tạo (AI) và Mô hình ngôn ngữ lớn (LLM), cùng với các ứng dụng thực tế trong phát triển phần mềm. Gi<PERSON>o trình bao gồm các buổi học chi tiết, từ những khái niệm cơ bản đến các công cụ và kỹ thuật nâng cao, gi<PERSON><PERSON> học viên nắm vững và áp dụng AI vào công việc.

## Mục tiêu khóa học

*   Hiểu rõ các khái niệm cơ bản về AI, Machine Learning, Deep Learning và Generative AI.
*   Nắm vững các nguyên tắc và kỹ thuật của Prompt Engineering.
*   T<PERSON><PERSON> hiểu về AI Agent và cách xây dựng các tác nhân AI tùy chỉnh.
*   Khám phá các công cụ AI hỗ trợ các vai trò khác nhau trong phát triển phần mềm (BA, SA, Dev, DevOps, Tester, Team Leader).
*   Hiểu và triển khai Retrieval Augmented Generation (RAG) để nâng cao khả năng của LLM.

## Đối tượng

*   Những người muốn tìm hiểu về AI và LLM.
*   Các chuyên gia trong lĩnh vực phát triển phần mềm (BA, SA, Dev, DevOps, Tester, Team Leader) muốn ứng dụng AI vào công việc.
*   Những ai quan tâm đến việc xây dựng các giải pháp AI tùy chỉnh.








## Buổi 1: Tổng quan về AI và LLM




### 1.1. Large Language Model (LLM)

LLM là một chương trình trí tuệ nhân tạo (AI) có khả năng nhận diện và tạo văn bản, cùng nhiều tác vụ khác. LLM được huấn luyện trên các tập dữ liệu khổng lồ (hàng nghìn đến hàng triệu gigabyte văn bản thu thập từ Internet hoặc các tập dữ liệu được chọn lọc kỹ càng). LLM được xây dựng dựa trên học máy (machine learning), cụ thể là một loại mạng nơ-ron gọi là mô hình transformer.

LLM sử dụng học sâu (deep learning) để hiểu cách các ký tự, từ và câu hoạt động cùng nhau. Học sâu liên quan đến phân tích xác suất dữ liệu phi cấu trúc, giúp mô hình học sâu nhận diện sự khác biệt giữa các phần nội dung mà không cần sự can thiệp của con người.

Sau đó, LLM được tinh chỉnh (fine-tuned) hoặc điều chỉnh prompt (prompt-tuned) cho các tác vụ cụ thể mà lập trình viên mong muốn, chẳng hạn như diễn giải câu hỏi và tạo phản hồi, hoặc dịch văn bản từ ngôn ngữ này sang ngôn ngữ khác.

**Ứng dụng của LLM:**

*   **AI tạo sinh (Generative AI):** Tạo văn bản phản hồi khi được cung cấp prompt hoặc câu hỏi (ví dụ: ChatGPT tạo bài luận, thơ).
*   **Hỗ trợ lập trình:** Viết mã, hoàn thành chương trình, viết hàm (ví dụ: GitHub Copilot).
*   **Phân tích cảm xúc (Sentiment analysis).**
*   **Nghiên cứu DNA.**
*   **Dịch vụ khách hàng.**
*   **Chatbot.**
*   **Tìm kiếm trực tuyến.**

**Ví dụ về các LLM phổ biến:** ChatGPT (OpenAI), Bard/Gemini (Google), Llama (Meta), Bing Chat (Microsoft).

**Cách hoạt động của LLM:**

*   **Học máy và học sâu:** LLM được xây dựng trên học máy, sử dụng học sâu để tự học cách nhận diện sự khác biệt trong dữ liệu. Học sâu sử dụng xác suất để 'học'.
*   **Mạng nơ-ron:** LLM được xây dựng trên mạng nơ-ron, mô phỏng cấu trúc não bộ con người với các nút mạng kết nối với nhau, bao gồm lớp đầu vào, lớp đầu ra và các lớp ẩn.
*   **Mô hình Transformer:** Là loại mạng nơ-ron cụ thể được sử dụng cho LLM, có khả năng học ngữ cảnh rất tốt nhờ kỹ thuật tự chú ý (self-attention). Điều này giúp chúng hiểu được mối quan hệ giữa các yếu tố trong một chuỗi, từ đó diễn giải ngôn ngữ con người một cách hiệu quả, ngay cả khi ngôn ngữ đó mơ hồ hoặc được sắp xếp theo cách mới.

**Ưu điểm và hạn chế của LLM:**

*   **Ưu điểm:** Khả năng phản hồi các truy vấn không có cấu trúc bằng ngôn ngữ tự nhiên, sử dụng phân tích dữ liệu để đưa ra câu trả lời hợp lý.
*   **Hạn chế:**
    *   **Độ tin cậy của dữ liệu:** LLM chỉ đáng tin cậy như dữ liệu mà chúng được nạp vào. Nếu dữ liệu đầu vào sai, chúng sẽ đưa ra thông tin sai lệch.
    *   **Ảo giác (Hallucination):** LLM đôi khi tạo ra thông tin giả mạo khi không thể đưa ra câu trả lời chính xác.
    *   **Bảo mật:** Các ứng dụng dựa trên LLM dễ bị lỗi như bất kỳ ứng dụng nào khác. Chúng có thể bị thao túng thông qua các đầu vào độc hại để đưa ra các phản hồi nguy hiểm hoặc phi đạo đức. Ngoài ra, người dùng có thể tải dữ liệu bảo mật, bí mật vào LLM để tăng năng suất, nhưng LLM sử dụng các đầu vào này để huấn luyện mô hình của chúng và không được thiết kế để lưu trữ an toàn; chúng có thể tiết lộ dữ liệu bí mật khi phản hồi các truy vấn từ người dùng khác.



### 1.2. Trí tuệ nhân tạo (AI)

Trí tuệ nhân tạo (AI) là công nghệ cho phép máy tính và máy móc mô phỏng khả năng học hỏi, hiểu biết, giải quyết vấn đề, ra quyết định, sáng tạo và tự chủ của con người.

Các ứng dụng và thiết bị được trang bị AI có thể nhìn và nhận diện đối tượng, hiểu và phản hồi ngôn ngữ con người, học hỏi từ thông tin và kinh nghiệm mới, đưa ra các khuyến nghị chi tiết cho người dùng và chuyên gia, và hoạt động độc lập thay thế sự can thiệp của con người (ví dụ: xe tự lái).

**Các khái niệm liên quan đến AI:**

*   **Học máy (Machine Learning - ML):** Là một nhánh của AI, liên quan đến việc tạo ra các mô hình bằng cách huấn luyện một thuật toán để đưa ra dự đoán hoặc quyết định dựa trên dữ liệu. ML bao gồm nhiều kỹ thuật cho phép máy tính học hỏi và suy luận từ dữ liệu mà không cần được lập trình rõ ràng cho các tác vụ cụ thể.
    *   **Các kỹ thuật học máy phổ biến:** Hồi quy tuyến tính, hồi quy logistic, cây quyết định, rừng ngẫu nhiên, máy vector hỗ trợ (SVMs), k-láng giềng gần nhất (KNN), phân cụm.
    *   **Mạng nơ-ron (Neural Network):** Là một loại thuật toán học máy phổ biến, mô phỏng cấu trúc và chức năng của não bộ con người. Mạng nơ-ron bao gồm các lớp nút (tương tự nơ-ron) được kết nối với nhau để xử lý và phân tích dữ liệu phức tạp. Chúng phù hợp với các tác vụ liên quan đến việc xác định các mẫu và mối quan hệ phức tạp trong lượng lớn dữ liệu.
    *   **Học có giám sát (Supervised Learning):** Sử dụng các tập dữ liệu đã được gán nhãn để huấn luyện thuật toán phân loại dữ liệu hoặc dự đoán kết quả. Mục tiêu là mô hình học được ánh xạ giữa đầu vào và đầu ra trong dữ liệu huấn luyện để có thể dự đoán nhãn của dữ liệu mới, chưa từng thấy.

*   **Học sâu (Deep Learning - DL):** Là một tập con của học máy, sử dụng mạng nơ-ron đa lớp (gọi là mạng nơ-ron sâu) mô phỏng chặt chẽ hơn khả năng ra quyết định phức tạp của não bộ con người.
    *   Mạng nơ-ron sâu bao gồm một lớp đầu vào, ít nhất ba nhưng thường là hàng trăm lớp ẩn, và một lớp đầu ra, không giống như mạng nơ-ron trong các mô hình học máy cổ điển thường chỉ có một hoặc hai lớp ẩn.
    *   Các lớp này cho phép **học không giám sát (unsupervised learning)**: chúng có thể tự động trích xuất các đặc trưng từ các tập dữ liệu lớn, không được gán nhãn và phi cấu trúc, và đưa ra dự đoán của riêng chúng về những gì dữ liệu đại diện.
    *   DL phù hợp với xử lý ngôn ngữ tự nhiên (NLP), thị giác máy tính (computer vision) và các tác vụ khác liên quan đến việc nhận diện nhanh chóng, chính xác các mẫu và mối quan hệ phức tạp trong lượng lớn dữ liệu.
    *   **Các loại học sâu khác:** Học bán giám sát (Semi-supervised learning), học tự giám sát (Self-supervised learning), học tăng cường (Reinforcement learning), học chuyển giao (Transfer learning).

*   **AI tạo sinh (Generative AI - Gen AI):** Đôi khi được gọi là "gen AI", đề cập đến các mô hình học sâu có thể tạo ra nội dung gốc phức tạp như văn bản dài, hình ảnh chất lượng cao, video hoặc âm thanh chân thực và nhiều hơn nữa để phản hồi prompt hoặc yêu cầu của người dùng.
    *   Các mô hình tạo sinh mã hóa một biểu diễn đơn giản hóa của dữ liệu huấn luyện của chúng, và sau đó dựa vào biểu diễn đó để tạo ra tác phẩm mới tương tự, nhưng không giống hệt, dữ liệu gốc.
    *   Các mô hình tạo sinh đã được sử dụng trong nhiều năm trong thống kê để phân tích dữ liệu số. Nhưng trong thập kỷ qua, chúng đã phát triển để phân tích và tạo ra các loại dữ liệu phức tạp hơn.
    *   **Các loại mô hình học sâu tinh vi:** Bộ mã hóa tự động biến phân (Variational autoencoders - VAEs), mô hình khuếch tán (Diffusion models).



### 1.3. Các khái niệm và thuật ngữ liên quan

*   **Prompt:** Là một đoạn văn bản đầu vào được cung cấp cho mô hình AI để hướng dẫn nó tạo ra một phản hồi mong muốn. Prompt có thể là một câu hỏi, một câu lệnh, một đoạn văn bản để hoàn thành, hoặc bất kỳ hình thức hướng dẫn nào khác.

*   **Agent (AI Agent):** Là một hệ thống AI có khả năng cảm nhận môi trường, ra quyết định và thực hiện hành động để đạt được mục tiêu cụ thể. AI Agent có thể tự động hóa các tác vụ phức tạp, tương tác với các công cụ khác và học hỏi từ kinh nghiệm.

*   **Transformer:** Là một kiến trúc mạng nơ-ron được giới thiệu vào năm 2017, đặc biệt hiệu quả trong việc xử lý dữ liệu tuần tự như ngôn ngữ tự nhiên. Transformer sử dụng cơ chế tự chú ý (self-attention) để đánh giá mức độ quan trọng của các phần khác nhau trong dữ liệu đầu vào khi tạo ra đầu ra, giúp nó hiểu ngữ cảnh tốt hơn so với các mô hình trước đây.

*   **Token:** Là đơn vị cơ bản của văn bản mà mô hình AI xử lý. Token có thể là một từ, một phần của từ, một ký tự, hoặc một dấu câu. Các mô hình LLM phân tích và tạo ra văn bản dựa trên các chuỗi token này.

*   **Temperature:** Là một siêu tham số (hyperparameter) trong các mô hình AI (đặc biệt là LLM) kiểm soát mức độ ngẫu nhiên của các dự đoán trong quá trình tạo văn bản. Giá trị temperature cao hơn sẽ tạo ra kết quả đa dạng và sáng tạo hơn, trong khi giá trị thấp hơn sẽ tạo ra kết quả tập trung và ít ngẫu nhiên hơn.
    *   **Cách hoạt động:** Temperature được áp dụng như một số chia cho các logits (đầu ra thô của lớp cuối cùng của mô hình) trước khi chúng được đưa qua hàm softmax. Khi temperature tăng, phân phối xác suất trở nên đồng đều hơn (mềm hơn), nghĩa là tất cả các lớp hoặc từ/token có xác suất tương tự nhau. Ngược lại, khi temperature giảm, phân phối trở nên tập trung hơn, với một hoặc vài lớp có xác suất cao trong khi các lớp khác có xác suất rất thấp.

*   **Fine-tune model (Tinh chỉnh mô hình):** Là quá trình tiếp tục huấn luyện một mô hình AI đã được huấn luyện trước (pre-trained model) trên một tập dữ liệu nhỏ hơn, cụ thể hơn cho một tác vụ hoặc miền nhất định. Mục tiêu của fine-tuning là điều chỉnh mô hình để nó hoạt động tốt hơn trên tác vụ cụ thể đó mà không cần phải huấn luyện lại từ đầu.





## Buổi 2: Prompt Engineering và AI Agent




### 2.1. Prompt Engineering / Prompt Design

Prompt Engineering là quá trình thiết kế và tinh chỉnh các câu lệnh (prompts) để tương tác hiệu quả với các mô hình ngôn ngữ lớn (LLM) và các công cụ AI khác. Mục tiêu là để nhận được kết quả chính xác, phù hợp và hữu ích nhất từ mô hình.

**Các phương pháp hay nhất (Best Practices) trong Prompt Engineering:**

1.  **Sử dụng mô hình mới nhất:** Các mô hình mới hơn thường dễ dàng hơn trong việc thực hiện prompt engineering và cho kết quả tốt hơn.

2.  **Đặt hướng dẫn ở đầu prompt và sử dụng `###` hoặc `"""` để phân tách hướng dẫn và ngữ cảnh:** Điều này giúp mô hình dễ dàng nhận diện đâu là hướng dẫn và đâu là dữ liệu đầu vào.
    *   **Ví dụ:**
        ```
        Tóm tắt văn bản dưới đây thành một danh sách gạch đầu dòng các điểm quan trọng nhất.

        Văn bản: """
        {văn bản đầu vào}
        """
        ```

3.  **Cụ thể, mô tả và chi tiết nhất có thể về ngữ cảnh, kết quả mong muốn, độ dài, định dạng, phong cách, v.v.:** Càng chi tiết, mô hình càng dễ hiểu và tạo ra kết quả chính xác.
    *   **Ví dụ:** Thay vì 


        `Viết một bài thơ về OpenAI.`
        Hãy viết:
        `Viết một bài thơ truyền cảm hứng ngắn gọn về OpenAI, tập trung vào việc ra mắt sản phẩm DALL-E gần đây (DALL-E là mô hình ML chuyển văn bản thành hình ảnh) theo phong cách của {một nhà thơ nổi tiếng}.`

4.  **Trình bày định dạng đầu ra mong muốn thông qua các ví dụ:** Mô hình phản hồi tốt hơn khi được chỉ ra các yêu cầu định dạng cụ thể. Điều này cũng giúp dễ dàng phân tích cú pháp nhiều đầu ra một cách đáng tin cậy.
    *   **Ví dụ:**
        ```
        Trích xuất các thực thể được đề cập trong văn bản dưới đây. Đầu tiên trích xuất tất cả tên công ty, sau đó trích xuất tất cả tên người, sau đó trích xuất các chủ đề cụ thể phù hợp với nội dung và cuối cùng trích xuất các chủ đề tổng thể.

        Định dạng mong muốn:
        Tên công ty: <danh_sách_tên_công_ty_phân_tách_bằng_dấu_phẩy>
        Tên người: -||-
        Chủ đề cụ thể: -||-
        Chủ đề chung: -||-

        Văn bản: {văn bản}
        ```

5.  **Bắt đầu với zero-shot, sau đó few-shot, nếu cả hai đều không hiệu quả thì fine-tune:**
    *   **Zero-shot:** Cung cấp prompt mà không có ví dụ nào.
        *   **Ví dụ:**
            ```
            Trích xuất từ khóa từ văn bản dưới đây.

            Văn bản: {văn bản}

            Từ khóa:
            ```
    *   **Few-shot:** Cung cấp một vài ví dụ trong prompt.
        *   **Ví dụ:**
            ```
            Trích xuất từ khóa từ các văn bản tương ứng dưới đây.

            Văn bản 1: Stripe cung cấp các API mà các nhà phát triển web có thể sử dụng để tích hợp xử lý thanh toán vào các trang web và ứng dụng di động của họ.
            Từ khóa 1: Stripe, xử lý thanh toán, API, nhà phát triển web, trang web, ứng dụng di động
            ##
            Văn bản 2: OpenAI đã huấn luyện các mô hình ngôn ngữ tiên tiến rất giỏi trong việc hiểu và tạo văn bản. API của chúng tôi cung cấp quyền truy cập vào các mô hình này và có thể được sử dụng để giải quyết hầu hết mọi tác vụ liên quan đến xử lý ngôn ngữ.
            Từ khóa 2: OpenAI, mô hình ngôn ngữ, xử lý văn bản, API.
            ##
            Văn bản 3: {văn bản}
            Từ khóa 3:
            ```
    *   **Fine-tune:** Nếu zero-shot và few-shot không mang lại kết quả mong muốn, hãy xem xét fine-tune mô hình với tập dữ liệu cụ thể của bạn.

6.  **Giảm các mô tả “rườm rà” và không chính xác:** Thay vì nói `Mô tả sản phẩm này nên khá ngắn gọn, chỉ vài câu, và không quá nhiều.`, hãy nói `Sử dụng một đoạn văn từ 3 đến 5 câu để mô tả sản phẩm này.`

7.  **Thay vì chỉ nói những gì không nên làm, hãy nói những gì nên làm:**
    *   **Ví dụ:** Thay vì:
        ```
        Sau đây là cuộc trò chuyện giữa một Agent và một Khách hàng. KHÔNG HỎI TÊN NGƯỜI DÙNG HOẶC MẬT KHẨU. KHÔNG LẶP LẠI.

        Khách hàng: Tôi không thể đăng nhập vào tài khoản của mình.
        Agent:
        ```
        Hãy viết:
        ```
        Sau đây là cuộc trò chuyện giữa một Agent và một Khách hàng. Agent sẽ cố gắng chẩn đoán vấn đề và đề xuất giải pháp, đồng thời tránh hỏi bất kỳ câu hỏi nào liên quan đến PII (Thông tin nhận dạng cá nhân). Thay vì hỏi PII, chẳng hạn như tên người dùng hoặc mật khẩu, hãy giới thiệu người dùng đến bài viết trợ giúp www.samplewebsite.com/help/faq

        Khách hàng: Tôi không thể đăng nhập vào tài khoản của mình.
        Agent:
        ```

8.  **Đối với tạo mã (Code Generation) - Sử dụng “từ dẫn đầu” để định hướng mô hình theo một mẫu cụ thể:**
    *   **Ví dụ:** Để tạo một hàm Python chuyển đổi dặm sang kilômét, thay vì chỉ mô tả, hãy bắt đầu bằng `import` để gợi ý mô hình viết mã Python.
        ```python
        # Viết một hàm python đơn giản mà
        # 1. Hỏi tôi một số bằng dặm
        # 2. Nó chuyển đổi dặm sang kilômét
        import
        ```

**Các tham số quan trọng trong Prompt Engineering:**

*   **`model`:** Các mô hình hiệu suất cao hơn thường đắt hơn và có thể có độ trễ cao hơn.
*   **`temperature`:** Một thước đo mức độ thường xuyên mô hình xuất ra một token ít có khả năng xảy ra hơn. `temperature` càng cao, đầu ra càng ngẫu nhiên (và thường sáng tạo) hơn. Tuy nhiên, điều này không giống với “tính xác thực”. Đối với hầu hết các trường hợp sử dụng thực tế như trích xuất dữ liệu và Q&A chính xác, `temperature` bằng 0 là tốt nhất.
*   **`max_completion_tokens` (độ dài tối đa):** Không kiểm soát độ dài của đầu ra, nhưng là giới hạn cắt cứng cho việc tạo token. Lý tưởng nhất là bạn sẽ không thường xuyên đạt đến giới hạn này, vì mô hình của bạn sẽ dừng lại khi nó nghĩ rằng đã hoàn thành hoặc khi nó đạt đến một chuỗi dừng mà bạn đã xác định.
*   **`stop` (chuỗi dừng):** Một tập hợp các ký tự (token) mà khi được tạo ra, sẽ khiến quá trình tạo văn bản dừng lại.



### 2.2. AI Agent

AI Agent (hoặc tác nhân AI) là một hệ thống hoặc chương trình trí tuệ nhân tạo có khả năng tự chủ thự
(Content truncated due to size limit. Use line ranges to read in chunks)