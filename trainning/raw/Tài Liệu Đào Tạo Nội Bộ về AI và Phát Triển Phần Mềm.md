# **T<PERSON><PERSON>i Bộ: AI và Phát Triển Phần Mềm**

## **<PERSON><PERSON><PERSON> Tiêu <PERSON>o**
- Gi<PERSON><PERSON> đội ngũ hiểu tổng quan về AI hiện tại, đặc biệt là Large Language Models (LLMs), và các khái niệm liên quan như Prompt, Agent, Transformer, token, Temperature, Fine-tune model.
- Cung cấp kiến thức về các công cụ AI hiện có và cách sử dụng chúng trong phát triển phần mềm, cũng nh<PERSON> tiềm năng áp dụng cho các lĩnh vực khác.
- Đ<PERSON>a ra cái nhìn sâu sắc về lịch sử phát triển AI từ khi ChatGPT ra mắt, bao gồm các mô hình nổ<PERSON> bậ<PERSON>, công ty tiê<PERSON>hong, c<PERSON><PERSON> b<PERSON><PERSON><PERSON> tiế<PERSON>, và xu hướng tương lai.
- <PERSON><PERSON>ớng dẫn về Prompt Engineering và AI Agent, gi<PERSON><PERSON> đội ngũ tạo prompt hiệu quả và hiểu cách xây dựng agent.
- Cung cấp kiến thức chuyên sâu về các công cụ AI hỗ trợ các vai trò như BA, SA, Dev, DevOps, team leader, và tester.
- Hướng dẫn cách xây dựng custom Agent và RAG, sử dụng các công cụ như Langflow, LangChain, Agno framework, và MindsDB.

## **Đối Tượng Đào Tạo**
- Đội ngũ kỹ thuật của công ty, bao gồm các vị trí như Business Analyst (BA), Solution Architect (SA), Developer (Dev), DevOps, team leader, tester (tất cả trình độ senior trở lên).

## **Thời Gian Đào Tạo**
- Chia thành 4 buổi, mỗi buổi 2 giờ:
  - **Buổi 1**: Tổng quan về AI và LLM, lịch sử phát triển AI, các khái niệm cơ bản.
  - **Buổi 2**: Prompt Engineering và AI Agent.
  - **Buổi 3**: Các công cụ AI hỗ trợ phát triển phần mềm.
  - **Buổi 4**: Xây dựng custom Agent và RAG.

---

# **Buổi 1: Tổng Quan về AI và LLM Models**

## **1.1. Tổng Quan về Large Language Models (LLMs)**

### **Định Nghĩa**
Large Language Models (LLMs) là các mô hình AI được huấn luyện trên lượng dữ liệu văn bản khổng lồ, có khả năng hiểu, tạo ra, và tương tác với ngôn ngữ tự nhiên. Chúng được sử dụng trong các nhiệm vụ như trả lời câu hỏi, dịch thuật, và tạo nội dung.

### **Các Khái Niệm Chính**
| **Thuật Ngữ**      | **Mô Tả**                                                                 |
|--------------------|---------------------------------------------------------------------------|
| **Prompt**         | Đầu vào văn bản hướng dẫn LLM tạo ra đầu ra mong muốn.                     |
| **Agent**          | Hệ thống AI thực hiện nhiệm vụ tự động dựa trên prompt và công cụ.         |
| **Transformer**     | Kiến trúc mạng nơ-ron cơ bản của LLM, xử lý dữ liệu tuần tự hiệu quả.      |
| **Token**          | Đơn vị cơ bản của văn bản (từ hoặc phần từ) mà LLM xử lý.                  |
| **Temperature**     | Tham số điều chỉnh độ ngẫu nhiên của đầu ra LLM.                           |
| **Fine-tune model**| Huấn luyện thêm LLM trên dữ liệu cụ thể để tối ưu hóa cho nhiệm vụ đặc thù.|

### **Tài Nguyên**
- [Wikipedia: Large Language Model](https://en.wikipedia.org/wiki/Large_language_model)
- [Analytics Vidhya: Introduction to LLMs](https://www.analyticsvidhya.com/blog/2023/03/an-introduction-to-large-language-models-llms/)

## **1.2. Lịch Sử Phát Triển AI từ ChatGPT đến Nay**

### **Các Mốc Quan Trọng**
- **2017**: Google giới thiệu kiến trúc Transformer, nền tảng cho nhiều LLM hiện đại.
- **2020**: OpenAI ra mắt GPT-3, một bước tiến lớn trong quy mô và khả năng của LLM.
- **2022**: ChatGPT được phát hành, đưa AI đến gần hơn với người dùng phổ thông.
- **2023-2025**: Sự phát triển của các mô hình như GPT-4, Gemini, và các công cụ AI từ OpenAI, Google, Microsoft.

### **Các Mô Hình AI Nổi Bật**
| **Mô Hình**        | **Công Ty**       | **Đặc Điểm**                                      |
|--------------------|-------------------|--------------------------------------------------|
| **GPT-3/4**        | OpenAI            | Khả năng hiểu và tạo văn bản mạnh mẽ.             |
| **Gemini**         | Google            | Đa phương tiện, xử lý văn bản, hình ảnh, âm thanh.|
| **Claude**         | Anthropic         | Tập trung vào an toàn và đạo đức AI.             |
| **Grok**           | xAI               | Trả lời câu hỏi hữu ích và trung thực.           |

### **Các Công Ty Tiên Phong**
- **OpenAI**: Dẫn đầu với GPT và ChatGPT.
- **Google (DeepMind)**: Phát triển Gemini, LaMDA.
- **Microsoft**: Tích hợp AI vào Bing, Azure.
- **IBM**: Phát triển Watson.
- **NVIDIA**: Cung cấp phần cứng và phần mềm AI.

### **Xu Hướng Hiện Tại và Tương Lai**
- **Hiện Tại**: Tăng cường tự động hóa, tích hợp AI vào các ứng dụng doanh nghiệp.
- **Tương Lai Gần**: Mô hình AI chuyên sâu, tự học và điều chỉnh.
- **Tương Lai Xa**: AI trở thành công cụ không thể thiếu trong y tế, giáo dục, và hơn thế nữa.

### **Tài Nguyên**
- [Forbes: AI 50 List 2025](https://www.forbes.com/lists/ai50/)
- [Wikipedia: Timeline of AI](https://en.wikipedia.org/wiki/Timeline_of_artificial_intelligence)

---

# **Buổi 2: Prompt Engineering và AI Agent**

## **2.1. Prompt Engineering**

### **Định Nghĩa**
Prompt Engineering là quá trình thiết kế và tinh chỉnh các prompt để hướng dẫn LLM tạo ra đầu ra chính xác và phù hợp. Đây là kỹ năng quan trọng để tối ưu hóa hiệu suất AI.

### **Kỹ Thuật Prompt Engineering**
- **Hướng Dẫn Rõ Ràng**: Đảm bảo prompt cụ thể và dễ hiểu.
- **Ngữ Cảnh Liên Quan**: Cung cấp thông tin nền để LLM trả lời chính xác.
- **Ví Dụ Minh Họa**: Đưa ra các ví dụ để định hình đầu ra.
- **Tinh Chỉnh Lặp Lại**: Kiểm tra và điều chỉnh prompt dựa trên kết quả.

### **Tài Nguyên**
- [LearnPrompting.org: Prompt Guide](https://learnprompting.org/docs/introduction)
- [AWS: Prompt Engineering](https://aws.amazon.com/what-is/prompt-engineering/)

## **2.2. Tổng Quan về AI Agent**

### **Định Nghĩa**
AI Agent là hệ thống AI tự động thực hiện nhiệm vụ dựa trên prompt và công cụ. Chúng có thể tương tác với môi trường, đưa ra quyết định, và hoàn thành mục tiêu.

### **Cách Hoạt Động**
1. Nhận prompt từ người dùng.
2. Lập kế hoạch hành động bằng LLM.
3. Sử dụng công cụ (API, database) để thực hiện nhiệm vụ.
4. Trả kết quả cho người dùng.

### **Ví Dụ**
- **Chatbot**: Trả lời câu hỏi dựa trên kiến thức.
- **Agent Tự Động Hóa**: Đặt lịch, gửi email, tìm kiếm thông tin.

### **Tài Nguyên**
- [LangChain: Build Agent](https://python.langchain.com/docs/tutorials/agents/)
- [Medium: AI Agents Guide](https://medium.com/@lorevanoudenhove/how-to-build-ai-agents-with-langgraph-a-step-by-step-guide-5d84d9c7e832)

---

# **Buổi 3: Các AI Tools Hỗ Trợ Phát Triển Phần Mềm**

## **3.1. AI Tools cho Business Analyst (BA)**

| **Công Cụ**        | **Mô Tả**                                      | **Ưu Điểm**                              | **Nhược Điểm**                           | **Giá Bản Quyền** |
|--------------------|-----------------------------------------------|------------------------------------------|------------------------------------------|-------------------|
| **Tableau**        | Trực quan hóa và phân tích dữ liệu.           | Giao diện thân thiện, mạnh về visualization. | Chi phí cao, cần đào tạo.                | Từ $70/tháng ([Tableau Pricing](https://www.tableau.com/pricing)) |
| **Power BI**       | Phân tích kinh doanh của Microsoft.           | Tích hợp tốt với Microsoft, giá hợp lý.  | Hạn chế với dữ liệu không phải Microsoft.| Từ $10/tháng ([Power BI Pricing](https://powerbi.microsoft.com/en-us/pricing/)) |
| **Qlik Sense**     | Tích hợp và trực quan hóa dữ liệu.           | Linh hoạt, hỗ trợ phân tích sâu.         | Khó sử dụng cho người mới.               | Liên hệ nhà cung cấp ([Qlik Pricing](https://www.qlik.com/us/pricing)) |
| **BlazeSQL**       | Tạo truy vấn SQL từ ngôn ngữ tự nhiên.       | Dễ sử dụng, tự động hóa phân tích.       | Hạn chế với dữ liệu phức tạp.            | Từ $29/tháng ([BlazeSQL Pricing](https://www.blazesql.com/pricing)) |

## **3.2. AI Tools cho Solution Architect (SA)**

| **Công Cụ**        | **Mô Tả**                                      | **Ưu Điểm**                              | **Nhược Điểm**                           | **Giá Bản Quyền** |
|--------------------|-----------------------------------------------|------------------------------------------|------------------------------------------|-------------------|
| **Enterprise Architect** | Tạo mô hình dữ liệu và UML.               | Toàn diện, hỗ trợ nhiều framework.       | Giao diện phức tạp.                      | Từ $229 ([EA Pricing](https://sparxsystems.com/products/ea/purchase.html)) |
| **Lucidchart**     | Vẽ đồ thị trên cloud.                        | Dễ sử dụng, hỗ trợ cộng tác.             | Hạn chế với mô hình phức tạp.            | Từ $7.95/tháng ([Lucidchart Pricing](https://www.lucidchart.com/pages/pricing)) |
| **AWS CloudFormation** | Định nghĩa tài nguyên cloud.              | Tích hợp tốt với AWS, tự động hóa.       | Cần kiến thức AWS.                       | Miễn phí, trả theo tài nguyên ([AWS Pricing](https://aws.amazon.com/cloudformation/pricing/)) |

## **3.3. AI Tools cho Developer (Dev)**

| **Công Cụ**        | **Mô Tả**                                      | **Ưu Điểm**                              | **Nhược Điểm**                           | **Giá Bản Quyền** |
|--------------------|-----------------------------------------------|------------------------------------------|------------------------------------------|-------------------|
| **GitHub Copilot** | Hoàn thành mã tự động.                       | Tăng tốc viết code, tích hợp IDE.        | Đôi khi tạo mã không chính xác.          | $10/tháng ([Copilot Pricing](https://github.com/pricing)) |
| **Tabnine**        | Hoàn thành mã AI.                            | Hỗ trợ nhiều ngôn ngữ, bảo mật cao.      | Hiệu suất phụ thuộc vào ngữ cảnh.        | Từ $12/tháng ([Tabnine Pricing](https://www.tabnine.com/pricing)) |
| **Amazon CodeWhisperer** | Tạo mã tự động của AWS.                 | Tích hợp AWS, miễn phí cho cá nhân.      | Hạn chế ngoài môi trường AWS.            | Miễn phí/cá nhân ([CodeWhisperer Pricing](https://aws.amazon.com/codewhisperer/pricing/)) |
| **Cursor**         | IDE với AI hỗ trợ lập trình.                 | Giao diện giống VS Code, mạnh về AI.     | Còn mới, ít tài liệu.                    | Từ $20/tháng ([Cursor Pricing](https://cursor.sh/pricing)) |

## **3.4. AI Tools cho DevOps**

| **Công Cụ**        | **Mô Tả**                                      | **Ưu Điểm**                              | **Nhược Điểm**                           | **Giá Bản Quyền** |
|--------------------|-----------------------------------------------|------------------------------------------|------------------------------------------|-------------------|
| **Kubiya**         | Trợ lý DevOps ảo.                            | Tự động hóa quy trình, tích hợp tốt.     | Cần cấu hình ban đầu.                    | Liên hệ nhà cung cấp ([Kubiya Pricing](https://www.kubiya.ai/pricing)) |
| **CodeGuru**       | Xem xét mã và tối ưu hóa hiệu suất.          | Tích hợp AWS, phân tích sâu.             | Chỉ mạnh trong môi trường AWS.           | Từ $0.0005/phút ([CodeGuru Pricing](https://aws.amazon.com/codeguru/pricing/)) |
| **Datadog**        | Giám sát và phân tích.                       | Toàn diện, hỗ trợ nhiều nền tảng.        | Chi phí cao cho quy mô lớn.              | Từ $15/tháng ([Datadog Pricing](https://www.datadoghq.com/pricing/)) |
| **Sysdig**         | Giám sát container và bảo mật.               | Mạnh về container, bảo mật tốt.          | Cần kiến thức chuyên sâu.                | Liên hệ nhà cung cấp ([Sysdig Pricing](https://sysdig.com/pricing/)) |

## **3.5. AI Tools cho Team Leader**

| **Công Cụ**        | **Mô Tả**                                      | **Ưu Điểm**                              | **Nhược Điểm**                           | **Giá Bản Quyền** |
|--------------------|-----------------------------------------------|------------------------------------------|------------------------------------------|-------------------|
| **Otter.ai**       | Chuyển giọng nói thành văn bản.              | Ghi chú tự động, dễ chia sẻ.             | Độ chính xác phụ thuộc vào âm thanh.     | Từ $8.33/tháng ([Otter Pricing](https://otter.ai/pricing)) |
| **Grammarly**      | Hỗ trợ viết lách.                            | Kiểm tra ngữ pháp, cải thiện văn phong.  | Hạn chế với văn bản kỹ thuật.            | Từ $12/tháng ([Grammarly Pricing](https://www.grammarly.com/plans)) |
| **Zapier**         | Tự động hóa luồng công việc.                 | Kết nối nhiều ứng dụng, dễ dùng.         | Chi phí tăng với quy mô lớn.             | Từ $19.99/tháng ([Zapier Pricing](https://zapier.com/pricing)) |
| **Asana**          | Quản lý dự án với AI insights.               | Giao diện trực quan, hỗ trợ AI.          | Cần thời gian làm quen.                  | Từ $10.99/tháng ([Asana Pricing](https://asana.com/pricing)) |

## **3.6. AI Tools cho Tester**

| **Công Cụ**        | **Mô Tả**                                      | **Ưu Điểm**                              | **Nhược Điểm**                           | **Giá Bản Quyền** |
|--------------------|-----------------------------------------------|------------------------------------------|------------------------------------------|-------------------|
| **Testim**         | Kiểm thử tự động với AI.                     | Dễ sử dụng, tích hợp CI/CD.              | Hạn chế với ứng dụng phức tạp.           | Liên hệ nhà cung cấp ([Testim Pricing](https://www.testim.io/pricing)) |
| **Applitools**     | Kiểm thử hình ảnh và UI.                     | Mạnh về kiểm thử giao diện.              | Chi phí cao.                             | Từ $299/tháng ([Applitools Pricing](https://applitools.com/pricing/)) |
| **testRigor**      | Kiểm thử tự động không cần code.             | Dễ dùng, hỗ trợ nhiều nền tảng.          | Hạn chế với kiểm thử phức tạp.           | Từ $900/tháng ([testRigor Pricing](https://testrigor.com/pricing/)) |
| **LambdaTest**     | Kiểm thử đa trình duyệt với AI.              | Hỗ trợ nhiều trình duyệt, tích hợp tốt.  | Chi phí tăng với quy mô lớn.             | Từ $15/tháng ([LambdaTest Pricing](https://www.lambdatest.com/pricing)) |

### **Tài Nguyên**
- [DigitalOcean: AI Testing Tools](https://www.digitalocean.com/resources/articles/ai-testing-tools)
- [BrowserStack: Open Source Testing](https://www.browserstack.com/guide/open-source-ai-testing-tools)

---

# **Buổi 4: Xây Dựng Custom Agent và RAG**

## **4.1. Xây Dựng Custom Agent với Prompt**
- Sử dụng nền tảng như LangChain hoặc OpenAI để tạo agent.
- Định nghĩa công cụ và tích hợp với agent.
- Tạo prompt hiệu quả để hướng dẫn hành vi agent.

### **Ví Dụ**
Tạo một agent trả lời câu hỏi về thời tiết:
- Prompt: "Bạn là chuyên gia dự báo thời tiết. Hãy cung cấp thông tin thời tiết cho vị trí được yêu cầu."
- Công cụ: API thời tiết như OpenWeatherMap.

### **Tài Nguyên**
- [LangChain: Custom Agent](https://python.langchain.com/v0.1/docs/modules/agents/how_to/custom_agent/)
- [Microsoft Learn: Prompt Creation](https://learn.microsoft.com/en-us/ai-builder/create-a-custom-prompt)

## **4.2. Xây Dựng Agent với Giao Diện Kéo Thả (Langflow)**
- **Langflow**: Nền tảng low-code để xây dựng agent và workflow AI bằng giao diện kéo thả.
- Hỗ trợ tích hợp với nhiều LLM và cơ sở dữ liệu vector.
- Phù hợp cho việc tạo prototype nhanh.

### **Tài Nguyên**
- [Langflow: Official Website](https://www.langflow.org/)
- [DataStax: Langflow Guide](https://www.datastax.com/blog/guide-to-building-agents-in-langflow)

## **4.3. Xây Dựng Agent Hoàn Toàn Bằng Code (LangChain, Agno)**

| **Framework**      | **Mô Tả**                                      | **Ưu Điểm**                              | **Nhược Điểm**                           |
|--------------------|-----------------------------------------------|------------------------------------------|------------------------------------------|
| **LangChain**      | Framework phát triển ứng dụng LLM.            | Nhiều công cụ, cộng đồng lớn.            | Phức tạp với dự án tùy chỉnh.            |
| **Agno**           | Framework nhẹ, nhanh cho agent đa phương tiện.| Tốc độ cao, tiết kiệm tài nguyên.        | Còn mới, ít tài liệu.                    |

### **Tài Nguyên**
- [LangChain: Build Agent](https://python.langchain.com/docs/tutorials/agents/)
- [Analytics Vidhya: Agno Framework](https://www.analyticsvidhya.com/blog/2025/03/agno-framework/)

## **4.4. Hiểu về RAG (Retrieval-Augmented Generation)**
- **RAG**: Kỹ thuật kết hợp truy xuất dữ liệu với mô hình sinh thành để tạo câu trả lời chính xác hơn.
- Quy trình: Truy xuất dữ liệu từ cơ sở kiến thức, sử dụng làm ngữ cảnh cho LLM.
- **MindsDB**: Cung cấp Knowledge Bases hỗ trợ RAG, giúp truy vấn dữ liệu doanh nghiệp thông minh.

### **Tài Nguyên**
- [MindsDB: CTO’s Guide to RAG](https://mindsdb.com/the-ctos-guide-to-rag)
- [AWS: RAG Explained](https://aws.amazon.com/what-is/retrieval-augmented-generation/)

---

# **Tài Liệu Tham Khảo**
- [Wikipedia: Large Language Model](https://en.wikipedia.org/wiki/Large_language_model)
- [Forbes: AI 50 List 2025](https://www.forbes.com/lists/ai50/)
- [LearnPrompting.org: Prompt Guide](https://learnprompting.org/docs/introduction)
- [LangChain: Build Agent](https://python.langchain.com/docs/tutorials/agents/)
- [DigitalOcean: AI Testing Tools](https://www.digitalocean.com/resources/articles/ai-testing-tools)
- [MindsDB: CTO’s Guide to RAG](https://mindsdb.com/the-ctos-guide-to-rag)
- [Langflow: Official Website](https://www.langflow.org/)