# Chat
- ChatGPT
- Perplexity
- Grok
- Gemini
- Galaxy.ai

# Code
- GitHub Copilot
- Cursor
- Augment
- Windsurf
- ChatGPT Codex
- Google Jules
- Copilot Agent
- <PERSON> Code
- Trae
- Zend
- Void

# Slide Generation / Website Generation
- Gamma
- Genspark
- Manus
- Google AI Studio App
- Github copilot (prompting để gen slide)
- CustomGPT (ChatGPT) / Custom Gem (Gemini)
- sli.dev

# App builder
- replit.io
- bolt.new
- v0.dev
- loveable.dev
- devin

# UI Generation
- UX Pilot
- Google Stitch
- Figma AI
- Cursor + TalkToFigma MCP

# Research
- Google Notebook LM
- Grok deep search
- Gemini Deep research
- ChatGPT deep research
- Perplexity deep search
- Manus
- Flowith

# MCP
- Github MCP
- Context7 / DevDocs MCP
- TaskMaskter MCP
- Playwright MCP
- stagewise MCP
- TalkToFigma MCP

# Memory
- Mem0
- Anthropic Memory Server
- byterover.dev

# Agent frameworks
- Langgraph / Langchain
- Agno Framework
- Langflow
- Google ADK

# RAG framework / RAG tools
- MindsDb framework
- Docling

# Automation worker
- N8n
- Make.com
- Zapier

# LLM Model API (Online / Offline)
- OpenAI API
- Gemini API
- Grok API
- DeepSeek API
- OpenRouter API (Universal API)
- LMStudio API (Offline)
- Ollama API (Offline)