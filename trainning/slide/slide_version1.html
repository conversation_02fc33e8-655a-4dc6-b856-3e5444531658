<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> c<PERSON><PERSON> s<PERSON>u về <PERSON> cụ AI <PERSON><PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .slide {
            display: none !important;
            aspect-ratio: 16/9;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            z-index: 1;
        }

        .slide.active {
            display: flex !important;
            z-index: 10;
        }

        .slides-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateX(20px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .icon-3d {
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
        }

        /* Navigation button styles */
        .nav-btn {
            backdrop-filter: blur(8px);
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .nav-btn:active {
            transform: translateY(0);
        }

        .nav-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .nav-btn.disabled:hover {
            transform: none;
            background: rgba(255, 255, 255, 0.9);
        }

        /* Slide counter styles */
        .slide-counter {
            backdrop-filter: blur(8px);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>

<body class="bg-gray-100 font-sans overflow-hidden">
    <!-- Navigation Controls -->
    <div class="fixed top-6 right-6 z-50 flex gap-3">
        <button id="prevBtn" onclick="previousSlide()"
            class="nav-btn w-12 h-12 rounded-full flex items-center justify-center text-gray-600 hover:text-gray-800">
            <i class="fas fa-chevron-left text-lg"></i>
        </button>
        <button id="nextBtn" onclick="nextSlide()"
            class="nav-btn w-12 h-12 rounded-full flex items-center justify-center text-gray-600 hover:text-gray-800">
            <i class="fas fa-chevron-right text-lg"></i>
        </button>
    </div>

    <!-- Slide Counter -->
    <div class="fixed bottom-6 right-6 z-50 slide-counter px-4 py-2 rounded-full text-sm font-medium">
        <span id="slideCounter">1 / 15</span>
    </div>

    <!-- Slides Container -->
    <div class="slides-container relative w-full h-screen">

        <!-- Slide 1: Welcome -->
        <div class="slide active w-full h-screen bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-4xl mx-auto text-center text-white fade-in">
                <div class="mb-8">
                    <i class="fas fa-brain text-6xl mb-4 icon-3d"></i>
                </div>
                <h1 class="text-6xl font-bold mb-6">Báo cáo Chuyên sâu về Công cụ AI</h1>
                <p class="text-2xl mb-8">Tổng quan toàn diện về các công cụ Chat AI và Code Assistance hiện hành</p>
                <div class="text-lg space-y-2">
                    <p><i class="fas fa-calendar mr-2"></i>Tháng 6, 2025</p>
                    <p><i class="fas fa-user mr-2"></i>Phân tích Công nghệ & Xu hướng AI</p>
                </div>
            </div>
        </div>

        <!-- Slide 2: Table of Contents -->
        <div class="slide w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-6xl mx-auto w-full fade-in">
                <h1 class="text-4xl font-bold text-gray-900 mb-12 text-center">Table of Contents</h1>
                <div class="grid grid-cols-2 gap-8">
                    <div class="space-y-6">
                        <div
                            class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border-l-4 border-blue-500">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-comments text-blue-600 text-2xl mr-3"></i>
                                <h3 class="text-xl font-semibold text-gray-800">Phần I: Công cụ Chat</h3>
                            </div>
                            <ul class="text-gray-600 space-y-2">
                                <li>• Tổng quan & Xu hướng phát triển</li>
                                <li>• So sánh 4 công cụ hàng đầu</li>
                                <li>• ChatGPT, Perplexity, Grok, Gemini</li>
                                <li>• Phân tích ưu nhược điểm</li>
                            </ul>
                        </div>
                        <div
                            class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border-l-4 border-green-500">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-code text-green-600 text-2xl mr-3"></i>
                                <h3 class="text-xl font-semibold text-gray-800">Phần II: Code Assistance</h3>
                            </div>
                            <ul class="text-gray-600 space-y-2">
                                <li>• Xu hướng Agent-powered coding</li>
                                <li>• GitHub Copilot & Cursor</li>
                                <li>• Augment, Windsurf, Claude Code</li>
                                <li>• So sánh tính năng & giá cả</li>
                            </ul>
                        </div>
                    </div>
                    <div class="space-y-6">
                        <div
                            class="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-xl border-l-4 border-purple-500">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-chart-line text-purple-600 text-2xl mr-3"></i>
                                <h3 class="text-xl font-semibold text-gray-800">Phân tích Thị trường</h3>
                            </div>
                            <ul class="text-gray-600 space-y-2">
                                <li>• Mô hình kinh doanh Freemium</li>
                                <li>• Tích hợp dữ liệu thời gian thực</li>
                                <li>• Xu hướng đa phương thức</li>
                                <li>• Tương lai AI tools</li>
                            </ul>
                        </div>
                        <div
                            class="bg-gradient-to-r from-orange-50 to-red-50 p-6 rounded-xl border-l-4 border-orange-500">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-lightbulb text-orange-600 text-2xl mr-3"></i>
                                <h3 class="text-xl font-semibold text-gray-800">Khuyến nghị</h3>
                            </div>
                            <ul class="text-gray-600 space-y-2">
                                <li>• Lựa chọn công cụ phù hợp</li>
                                <li>• Chiến lược triển khai</li>
                                <li>• Xu hướng tương lai</li>
                                <li>• Best practices</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3: Tổng quan Công cụ Chat AI -->
        <div class="slide w-full h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-4 flex flex-col justify-start pt-16">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">Tổng quan Chat AI</h1>
                    <h2 class="text-xl text-gray-600 mb-8">Cuộc cách mạng trong tương tác người-máy</h2>
                    <div class="space-y-4">
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-rocket text-blue-600 mr-3"></i>
                            <span>Từ chatbot đơn giản đến trợ lý ảo thông minh</span>
                        </div>
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-sync-alt text-green-600 mr-3"></i>
                            <span>Chuyển dịch sang hệ thống đa phương thức</span>
                        </div>
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-database text-purple-600 mr-3"></i>
                            <span>Tích hợp dữ liệu thời gian thực</span>
                        </div>
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-dollar-sign text-orange-600 mr-3"></i>
                            <span>Mô hình kinh doanh Freemium phổ biến</span>
                        </div>
                    </div>
                </div>
                <div class="col-span-8 flex items-center justify-center">
                    <div class="w-full max-w-2xl">
                        <canvas id="evolutionChart" class="w-full max-h-96"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Bảng so sánh các công cụ Chat AI -->
        <div class="slide w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-4 flex flex-col justify-start pt-16">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">So sánh Chat AI</h1>
                    <h2 class="text-lg text-gray-600 mb-6">4 công cụ hàng đầu thị trường</h2>
                    <div class="space-y-3 text-sm">
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <strong class="text-blue-700">ChatGPT:</strong> Tiên phong, đa phương thức mạnh
                        </div>
                        <div class="bg-green-50 p-3 rounded-lg">
                            <strong class="text-green-700">Perplexity:</strong> Tập trung trích dẫn, nghiên cứu
                        </div>
                        <div class="bg-purple-50 p-3 rounded-lg">
                            <strong class="text-purple-700">Grok:</strong> Tích hợp X, thời gian thực
                        </div>
                        <div class="bg-orange-50 p-3 rounded-lg">
                            <strong class="text-orange-700">Gemini:</strong> Hệ sinh thái Google, ngữ cảnh dài
                        </div>
                    </div>
                </div>
                <div class="col-span-8 flex items-center justify-center">
                    <div class="w-full overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg shadow-lg text-sm">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left font-semibold text-gray-700">Công cụ</th>
                                    <th class="px-4 py-3 text-left font-semibold text-gray-700">Nhà phát triển</th>
                                    <th class="px-4 py-3 text-left font-semibold text-gray-700">Tính năng nổi bật</th>
                                    <th class="px-4 py-3 text-left font-semibold text-gray-700">Định giá</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr class="hover:bg-blue-50">
                                    <td class="px-4 py-3 font-medium text-blue-700">ChatGPT</td>
                                    <td class="px-4 py-3">OpenAI</td>
                                    <td class="px-4 py-3">Canvas, Custom GPTs, Deep Research</td>
                                    <td class="px-4 py-3">Free → $200/tháng</td>
                                </tr>
                                <tr class="hover:bg-green-50">
                                    <td class="px-4 py-3 font-medium text-green-700">Perplexity</td>
                                    <td class="px-4 py-3">Perplexity AI</td>
                                    <td class="px-4 py-3">Câu trả lời có trích dẫn, Deep Research</td>
                                    <td class="px-4 py-3">Free → $40/tháng</td>
                                </tr>
                                <tr class="hover:bg-purple-50">
                                    <td class="px-4 py-3 font-medium text-purple-700">Grok</td>
                                    <td class="px-4 py-3">xAI</td>
                                    <td class="px-4 py-3">Tích hợp X, Think Mode, DeepSearch</td>
                                    <td class="px-4 py-3">Free → $40/tháng</td>
                                </tr>
                                <tr class="hover:bg-orange-50">
                                    <td class="px-4 py-3 font-medium text-orange-700">Gemini</td>
                                    <td class="px-4 py-3">Google</td>
                                    <td class="px-4 py-3">Tích hợp Workspace, ngữ cảnh 2M token</td>
                                    <td class="px-4 py-3">Free → $40/tháng</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: ChatGPT chi tiết -->
        <div class="slide w-full h-screen bg-gradient-to-br from-blue-50 to-cyan-100 flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-5 flex flex-col justify-start pt-16">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">ChatGPT</h1>
                    <h2 class="text-xl text-gray-600 mb-8">Người tiên phong trong Chat AI</h2>
                    <div class="space-y-6">
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h3 class="font-semibold text-gray-800 mb-2">Phát triển nhanh chóng</h3>
                            <p class="text-gray-600 text-sm">Ra mắt 11/2022, từ GPT-3.5 đến dòng mô hình "o" mới nhất
                            </p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h3 class="font-semibold text-gray-800 mb-2">Tính năng đa dạng</h3>
                            <p class="text-gray-600 text-sm">Đa phương thức, Canvas, Custom GPTs, Deep Research</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h3 class="font-semibold text-gray-800 mb-2">Phân khúc rộng</h3>
                            <p class="text-gray-600 text-sm">Từ miễn phí đến Enterprise ($200/tháng)</p>
                        </div>
                    </div>
                </div>
                <div class="col-span-7 flex items-center justify-center">
                    <div class="grid grid-cols-2 gap-6 w-full">
                        <div class="text-center">
                            <div class="bg-blue-600 text-white p-6 rounded-xl mb-3">
                                <i class="fas fa-users text-3xl mb-2"></i>
                                <div class="text-2xl font-bold">180M+</div>
                                <div class="text-sm">Người dùng hàng tháng</div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-green-600 text-white p-6 rounded-xl mb-3">
                                <i class="fas fa-brain text-3xl mb-2"></i>
                                <div class="text-2xl font-bold">8</div>
                                <div class="text-sm">Mô hình AI khác nhau</div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-purple-600 text-white p-6 rounded-xl mb-3">
                                <i class="fas fa-globe text-3xl mb-2"></i>
                                <div class="text-2xl font-bold">80+</div>
                                <div class="text-sm">Ngôn ngữ hỗ trợ</div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-orange-600 text-white p-6 rounded-xl mb-3">
                                <i class="fas fa-cogs text-3xl mb-2"></i>
                                <div class="text-2xl font-bold">5</div>
                                <div class="text-sm">Gói subscription</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6: Perplexity AI -->
        <div class="slide w-full h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-5 flex flex-col justify-start pt-16">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">Perplexity AI</h1>
                    <h2 class="text-xl text-gray-600 mb-8">Công cụ tìm kiếm & nghiên cứu</h2>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i class="fas fa-quote-left text-green-600 mt-1 mr-3"></i>
                            <div>
                                <h3 class="font-semibold text-gray-800">Trích dẫn minh bạch</h3>
                                <p class="text-gray-600 text-sm">Mọi câu trả lời đều có nguồn trích dẫn rõ ràng</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-graduation-cap text-green-600 mt-1 mr-3"></i>
                            <div>
                                <h3 class="font-semibold text-gray-800">Academic Focus</h3>
                                <p class="text-gray-600 text-sm">Tìm kiếm chuyên biệt trong nguồn học thuật</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-search text-green-600 mt-1 mr-3"></i>
                            <div>
                                <h3 class="font-semibold text-gray-800">Deep Research</h3>
                                <p class="text-gray-600 text-sm">Tạo báo cáo nghiên cứu chi tiết từ nhiều nguồn</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-span-7 flex items-center justify-center">
                    <div class="w-full max-w-2xl">
                        <canvas id="perplexityChart" class="w-full max-h-96"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 7: Grok (xAI) -->
        <div class="slide w-full h-screen bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-5 flex flex-col justify-start pt-16">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">Grok</h1>
                    <h2 class="text-xl text-gray-600 mb-8">AI với cá tính "nổi loạn"</h2>
                    <div class="space-y-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-purple-500">
                            <h3 class="font-semibold text-purple-700 mb-2">Tích hợp X (Twitter)</h3>
                            <p class="text-gray-600 text-sm">Truy cập dữ liệu thời gian thực từ X Platform</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue-500">
                            <h3 class="font-semibold text-blue-700 mb-2">Think Mode</h3>
                            <p class="text-gray-600 text-sm">Suy luận nâng cao cho vấn đề phức tạp</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-green-500">
                            <h3 class="font-semibold text-green-700 mb-2">DeepSearch</h3>
                            <p class="text-gray-600 text-sm">Báo cáo chi tiết từ hàng tá nguồn web</p>
                        </div>
                    </div>
                </div>
                <div class="col-span-7 flex items-center justify-center">
                    <div class="text-center">
                        <div class="grid grid-cols-1 gap-6">
                            <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-8 rounded-2xl">
                                <i class="fab fa-twitter text-4xl mb-4"></i>
                                <h3 class="text-2xl font-bold mb-2">25.8M</h3>
                                <p class="text-lg">Người dùng hàng tháng sau Grok-3</p>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-blue-500 text-white p-6 rounded-xl text-center">
                                    <i class="fas fa-zap text-2xl mb-2"></i>
                                    <div class="text-xl font-bold">436%</div>
                                    <div class="text-sm">Tăng trưởng traffic</div>
                                </div>
                                <div class="bg-green-500 text-white p-6 rounded-xl text-center">
                                    <i class="fas fa-rocket text-2xl mb-2"></i>
                                    <div class="text-xl font-bold">10x</div>
                                    <div class="text-sm">Sức mạnh tính toán</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 8: Gemini (Google) -->
        <div class="slide w-full h-screen bg-gradient-to-br from-orange-50 to-red-100 flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-5 flex flex-col justify-start pt-16">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">Gemini</h1>
                    <h2 class="text-xl text-gray-600 mb-8">Hệ sinh thái AI của Google</h2>
                    <div class="space-y-6">
                        <div class="flex items-center">
                            <div class="bg-blue-100 p-3 rounded-full mr-4">
                                <i class="fab fa-google text-blue-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">Tích hợp sâu</h3>
                                <p class="text-gray-600 text-sm">Google Workspace & Chrome</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-green-100 p-3 rounded-full mr-4">
                                <i class="fas fa-expand-arrows-alt text-green-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">Ngữ cảnh dài</h3>
                                <p class="text-gray-600 text-sm">Lên đến 2M token</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-purple-100 p-3 rounded-full mr-4">
                                <i class="fas fa-magic text-purple-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">Đa phương thức</h3>
                                <p class="text-gray-600 text-sm">Văn bản, hình ảnh, âm thanh, video</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-span-7 flex items-center justify-center">
                    <div class="grid grid-cols-2 gap-6 w-full">
                        <div class="bg-gradient-to-br from-red-500 to-orange-500 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-file-alt text-3xl mb-3"></i>
                            <div class="text-2xl font-bold">1,500</div>
                            <div class="text-sm">Trang tài liệu xử lý</div>
                        </div>
                        <div class="bg-gradient-to-br from-blue-500 to-cyan-500 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-code text-3xl mb-3"></i>
                            <div class="text-2xl font-bold">30K</div>
                            <div class="text-sm">Dòng code hỗ trợ</div>
                        </div>
                        <div
                            class="bg-gradient-to-br from-green-500 to-emerald-500 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-brain text-3xl mb-3"></i>
                            <div class="text-2xl font-bold">2M</div>
                            <div class="text-sm">Token ngữ cảnh tối đa</div>
                        </div>
                        <div
                            class="bg-gradient-to-br from-purple-500 to-pink-500 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-video text-3xl mb-3"></i>
                            <div class="text-2xl font-bold">Veo 3</div>
                            <div class="text-sm">Tạo video AI</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 9: Mô hình kinh doanh -->
        <div class="slide w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-5 flex flex-col justify-start pt-16">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Mô hình Freemium</h1>
                    <h2 class="text-lg text-gray-600 mb-6">Chiến lược phổ biến của Chat AI</h2>
                    <div class="space-y-4">
                        <div class="bg-green-50 border border-green-200 p-4 rounded-lg">
                            <h3 class="font-semibold text-green-700 mb-2">Tầng Miễn phí</h3>
                            <p class="text-gray-600 text-sm">Thu hút người dùng, xây dựng thói quen sử dụng</p>
                        </div>
                        <div class="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                            <h3 class="font-semibold text-blue-700 mb-2">Tầng Trả phí</h3>
                            <p class="text-gray-600 text-sm">Tính năng nâng cao, giới hạn cao hơn</p>
                        </div>
                        <div class="bg-purple-50 border border-purple-200 p-4 rounded-lg">
                            <h3 class="font-semibold text-purple-700 mb-2">Enterprise</h3>
                            <p class="text-gray-600 text-sm">Bảo mật, quản trị, tùy chỉnh</p>
                        </div>
                    </div>
                </div>
                <div class="col-span-7 flex items-center justify-center">
                    <div class="w-full max-w-2xl">
                        <canvas id="pricingChart" class="w-full max-h-96"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 10: Tổng quan Code Assistance -->
        <div class="slide w-full h-screen bg-gradient-to-br from-green-50 to-teal-100 flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-4 flex flex-col justify-start pt-16">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">Code Assistance</h1>
                    <h2 class="text-xl text-gray-600 mb-8">Xu hướng Agent-powered Coding</h2>
                    <div class="space-y-4">
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-robot text-green-600 mr-3"></i>
                            <span>Từ auto-complete đến AI Agents</span>
                        </div>
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-project-diagram text-blue-600 mr-3"></i>
                            <span>Hiểu toàn bộ codebase</span>
                        </div>
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-shield-alt text-purple-600 mr-3"></i>
                            <span>Bảo mật & mô hình cục bộ</span>
                        </div>
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-plug text-orange-600 mr-3"></i>
                            <span>Hỗ trợ MCP & tích hợp IDE</span>
                        </div>
                    </div>
                </div>
                <div class="col-span-8 flex items-center justify-center">
                    <div class="w-full max-w-2xl">
                        <canvas id="codeEvolutionChart" class="w-full max-h-96"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 11: GitHub Copilot -->
        <div class="slide w-full h-screen bg-gradient-to-br from-gray-50 to-slate-100 flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-5 flex flex-col justify-start pt-16">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">GitHub Copilot</h1>
                    <h2 class="text-xl text-gray-600 mb-8">Lập trình viên cặp AI hàng đầu</h2>
                    <div class="space-y-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue-500">
                            <h3 class="font-semibold text-blue-700 mb-2">Agent Mode</h3>
                            <p class="text-gray-600 text-sm">Tự động thực hiện tác vụ phức tạp</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-green-500">
                            <h3 class="font-semibold text-green-700 mb-2">Workspace</h3>
                            <p class="text-gray-600 text-sm">Môi trường cộng tác toàn diện</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-purple-500">
                            <h3 class="font-semibold text-purple-700 mb-2">Enterprise</h3>
                            <p class="text-gray-600 text-sm">Bảo mật & kiểm soát doanh nghiệp</p>
                        </div>
                    </div>
                </div>
                <div class="col-span-7 flex items-center justify-center">
                    <div class="grid grid-cols-2 gap-6 w-full">
                        <div class="text-center">
                            <div class="bg-gray-800 text-white p-6 rounded-xl mb-3">
                                <i class="fab fa-github text-3xl mb-2"></i>
                                <div class="text-2xl font-bold">50M+</div>
                                <div class="text-sm">Lập trình viên sử dụng</div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-blue-600 text-white p-6 rounded-xl mb-3">
                                <i class="fas fa-code text-3xl mb-2"></i>
                                <div class="text-2xl font-bold">40%</div>
                                <div class="text-sm">Code được AI tạo</div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-green-600 text-white p-6 rounded-xl mb-3">
                                <i class="fas fa-clock text-3xl mb-2"></i>
                                <div class="text-2xl font-bold">55%</div>
                                <div class="text-sm">Tiết kiệm thời gian</div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-purple-600 text-white p-6 rounded-xl mb-3">
                                <i class="fas fa-dollar-sign text-3xl mb-2"></i>
                                <div class="text-2xl font-bold">$10-39</div>
                                <div class="text-sm">Giá/tháng</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 12: So sánh Code Tools -->
        <div class="slide w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-4 flex flex-col justify-start pt-16">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">So sánh Code Tools</h1>
                    <h2 class="text-lg text-gray-600 mb-6">Đặc điểm nổi bật từng công cụ</h2>
                    <div class="space-y-3 text-sm">
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <strong class="text-gray-700">Copilot:</strong> Tích hợp GitHub, Agent Mode
                        </div>
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <strong class="text-blue-700">Cursor:</strong> Next Edit, codebase-aware
                        </div>
                        <div class="bg-green-50 p-3 rounded-lg">
                            <strong class="text-green-700">Windsurf:</strong> Cascade, agentic IDE
                        </div>
                        <div class="bg-purple-50 p-3 rounded-lg">
                            <strong class="text-purple-700">Claude Code:</strong> CLI agent, MCP
                        </div>
                    </div>
                </div>
                <div class="col-span-8 flex items-center justify-center">
                    <div class="w-full max-w-2xl">
                        <canvas id="codeToolsChart" class="w-full max-h-96"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 13: Xu hướng tương lai -->
        <div class="slide w-full h-screen bg-gradient-to-br from-purple-50 to-indigo-100 flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-5 flex flex-col justify-start pt-16">
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">Xu hướng Tương lai</h1>
                    <h2 class="text-xl text-gray-600 mb-8">Điều gì đang chờ đợi?</h2>
                    <div class="space-y-6">
                        <div class="flex items-start">
                            <i class="fas fa-rocket text-purple-600 mt-1 mr-3 text-xl"></i>
                            <div>
                                <h3 class="font-semibold text-gray-800">Multimodal AI</h3>
                                <p class="text-gray-600 text-sm">Tích hợp văn bản, hình ảnh, âm thanh, video</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-brain text-purple-600 mt-1 mr-3 text-xl"></i>
                            <div>
                                <h3 class="font-semibold text-gray-800">Autonomous Agents</h3>
                                <p class="text-gray-600 text-sm">AI tự chủ thực hiện tác vụ phức tạp</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-shield-alt text-purple-600 mt-1 mr-3 text-xl"></i>
                            <div>
                                <h3 class="font-semibold text-gray-800">Privacy-First</h3>
                                <p class="text-gray-600 text-sm">Mô hình cục bộ, bảo mật dữ liệu</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-network-wired text-purple-600 mt-1 mr-3 text-xl"></i>
                            <div>
                                <h3 class="font-semibold text-gray-800">Ecosystem Integration</h3>
                                <p class="text-gray-600 text-sm">MCP, API, tích hợp sâu</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-span-7 flex items-center justify-center">
                    <div class="grid grid-cols-2 gap-6 w-full">
                        <div
                            class="bg-gradient-to-br from-purple-500 to-indigo-600 text-white p-8 rounded-2xl text-center">
                            <i class="fas fa-eye text-4xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-2">Vision AI</h3>
                            <p class="text-sm">Hiểu và tạo nội dung hình ảnh</p>
                        </div>
                        <div class="bg-gradient-to-br from-blue-500 to-cyan-600 text-white p-8 rounded-2xl text-center">
                            <i class="fas fa-microphone text-4xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-2">Voice AI</h3>
                            <p class="text-sm">Tương tác bằng giọng nói tự nhiên</p>
                        </div>
                        <div
                            class="bg-gradient-to-br from-green-500 to-emerald-600 text-white p-8 rounded-2xl text-center">
                            <i class="fas fa-home text-4xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-2">Edge AI</h3>
                            <p class="text-sm">AI chạy trực tiếp trên thiết bị</p>
                        </div>
                        <div
                            class="bg-gradient-to-br from-orange-500 to-red-600 text-white p-8 rounded-2xl text-center">
                            <i class="fas fa-puzzle-piece text-4xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-2">Specialized AI</h3>
                            <p class="text-sm">AI chuyên biệt cho từng lĩnh vực</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 14: Khuyến nghị -->
        <div class="slide w-full h-screen bg-white flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-7xl mx-auto grid grid-cols-12 gap-8 h-full max-h-full w-full fade-in">
                <div class="col-span-5 flex flex-col justify-start pt-16">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Khuyến nghị</h1>
                    <h2 class="text-lg text-gray-600 mb-6">Chiến lược lựa chọn & triển khai</h2>
                    <div class="space-y-4">
                        <div class="bg-blue-50 border-l-4 border-blue-500 p-4">
                            <h3 class="font-semibold text-blue-700 mb-2">Cho Cá nhân</h3>
                            <p class="text-gray-600 text-sm">Bắt đầu với gói miễn phí, nâng cấp khi cần</p>
                        </div>
                        <div class="bg-green-50 border-l-4 border-green-500 p-4">
                            <h3 class="font-semibold text-green-700 mb-2">Cho Doanh nghiệp</h3>
                            <p class="text-gray-600 text-sm">Ưu tiên bảo mật, quản trị và tích hợp</p>
                        </div>
                        <div class="bg-purple-50 border-l-4 border-purple-500 p-4">
                            <h3 class="font-semibold text-purple-700 mb-2">Cho Developer</h3>
                            <p class="text-gray-600 text-sm">Thử nghiệm nhiều công cụ, chọn phù hợp workflow</p>
                        </div>
                    </div>
                </div>
                <div class="col-span-7 flex items-center justify-center">
                    <div class="grid grid-cols-1 gap-6 w-full max-w-lg">
                        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-xl">
                            <h3 class="text-xl font-bold mb-3">
                                <i class="fas fa-lightbulb mr-2"></i>Bắt đầu nhỏ
                            </h3>
                            <p class="text-sm">Thử nghiệm với gói miễn phí trước khi đầu tư</p>
                        </div>
                        <div class="bg-gradient-to-r from-green-500 to-blue-600 text-white p-6 rounded-xl">
                            <h3 class="text-xl font-bold mb-3">
                                <i class="fas fa-shield-alt mr-2"></i>Ưu tiên bảo mật
                            </h3>
                            <p class="text-sm">Đảm bảo dữ liệu không được sử dụng để training</p>
                        </div>
                        <div class="bg-gradient-to-r from-purple-500 to-pink-600 text-white p-6 rounded-xl">
                            <h3 class="text-xl font-bold mb-3">
                                <i class="fas fa-chart-line mr-2"></i>Đo lường ROI
                            </h3>
                            <p class="text-sm">Theo dõi năng suất và chất lượng công việc</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 15: Thank You -->
        <div class="slide w-full h-screen bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center p-8 overflow-hidden"
            style="aspect-ratio: 16/9;">
            <div class="max-w-4xl mx-auto text-center text-white fade-in">
                <div class="mb-8">
                    <i class="fas fa-heart text-6xl mb-4 icon-3d"></i>
                </div>
                <h1 class="text-5xl font-bold mb-8">Cảm ơn!</h1>
                <p class="text-2xl mb-8">Câu hỏi & Thảo luận</p>
                <div class="text-lg space-y-2">
                    <p><i class="fas fa-envelope mr-2"></i>AI Tools Research Team</p>
                    <p><i class="fas fa-globe mr-2"></i>Tháng 6, 2025</p>
                </div>
            </div>
        </div>

    </div> <!-- End slides-container -->

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            // Remove active class from current slide
            if (slides[currentSlide]) {
                slides[currentSlide].classList.remove('active');
                slides[currentSlide].classList.remove('fade-in');
            }

            // Calculate new slide index
            currentSlide = (n + totalSlides) % totalSlides;

            // Show new slide
            if (slides[currentSlide]) {
                slides[currentSlide].classList.add('active');
                slides[currentSlide].classList.add('fade-in');
            }

            updateSlideCounter();
            updateNavigationButtons();

            // Remove fade-in class after animation
            setTimeout(() => {
                if (slides[currentSlide]) {
                    slides[currentSlide].classList.remove('fade-in');
                }
            }, 500);
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            }
        }

        function previousSlide() {
            if (currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // Handle button clicks with disabled state
        document.addEventListener('click', function (e) {
            if (e.target.closest('#prevBtn') && !e.target.closest('#prevBtn').classList.contains('disabled')) {
                previousSlide();
            } else if (e.target.closest('#nextBtn') && !e.target.closest('#nextBtn').classList.contains('disabled')) {
                nextSlide();
            }
        });

        function updateSlideCounter() {
            const counter = document.getElementById('slideCounter');
            if (counter) {
                counter.textContent = `${currentSlide + 1} / ${totalSlides}`;
            }
        }

        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            if (prevBtn) {
                if (currentSlide === 0) {
                    prevBtn.classList.add('disabled');
                } else {
                    prevBtn.classList.remove('disabled');
                }
            }

            if (nextBtn) {
                if (currentSlide === totalSlides - 1) {
                    nextBtn.classList.add('disabled');
                } else {
                    nextBtn.classList.remove('disabled');
                }
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                e.preventDefault();
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                e.preventDefault();
                previousSlide();
            } else if (e.key === 'Home') {
                e.preventDefault();
                showSlide(0);
            } else if (e.key === 'End') {
                e.preventDefault();
                showSlide(totalSlides - 1);
            }
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function () {
            // Ensure only first slide is visible
            slides.forEach((slide, index) => {
                if (index === 0) {
                    slide.classList.add('active');
                } else {
                    slide.classList.remove('active');
                }
            });

            updateSlideCounter();
            updateNavigationButtons();

            // Initialize charts when slides are shown
            // Evolution Chart
            const evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
            new Chart(evolutionCtx, {
                type: 'line',
                data: {
                    labels: ['2022', '2023', '2024', '2025'],
                    datasets: [
                        {
                            label: 'Số lượng Chat AI Tools',
                            data: [5, 15, 35, 50],
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.3
                        },
                        {
                            label: 'Tính năng đa phương thức',
                            data: [1, 3, 8, 15],
                            borderColor: 'rgb(16, 185, 129)',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.3
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Sự phát triển của Chat AI Tools'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Perplexity Chart
            const perplexityCtx = document.getElementById('perplexityChart').getContext('2d');
            new Chart(perplexityCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Học thuật', 'Web Search', 'News', 'Khác'],
                    datasets: [{
                        data: [35, 40, 15, 10],
                        backgroundColor: [
                            'rgb(16, 185, 129)',
                            'rgb(59, 130, 246)',
                            'rgb(245, 158, 11)',
                            'rgb(156, 163, 175)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Phân bố nguồn tìm kiếm Perplexity'
                        }
                    }
                }
            });

            // Pricing Chart
            const pricingCtx = document.getElementById('pricingChart').getContext('2d');
            new Chart(pricingCtx, {
                type: 'bar',
                data: {
                    labels: ['ChatGPT', 'Perplexity', 'Grok', 'Gemini'],
                    datasets: [
                        {
                            label: 'Free Tier',
                            data: [1, 1, 1, 1],
                            backgroundColor: 'rgba(34, 197, 94, 0.6)'
                        },
                        {
                            label: 'Pro ($20)',
                            data: [20, 20, 8, 20],
                            backgroundColor: 'rgba(59, 130, 246, 0.6)'
                        },
                        {
                            label: 'Max ($40+)',
                            data: [200, 40, 40, 40],
                            backgroundColor: 'rgba(147, 51, 234, 0.6)'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'So sánh giá các gói subscription (USD/tháng)'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Giá (USD/tháng)'
                            }
                        }
                    }
                }
            });

            // Code Evolution Chart
            const codeEvolutionCtx = document.getElementById('codeEvolutionChart').getContext('2d');
            new Chart(codeEvolutionCtx, {
                type: 'radar',
                data: {
                    labels: ['Auto-complete', 'Code Generation', 'Debug', 'Refactoring', 'Agent Tasks', 'Security'],
                    datasets: [
                        {
                            label: '2022',
                            data: [8, 4, 3, 2, 1, 2],
                            backgroundColor: 'rgba(59, 130, 246, 0.2)',
                            borderColor: 'rgb(59, 130, 246)',
                            pointBackgroundColor: 'rgb(59, 130, 246)'
                        },
                        {
                            label: '2025',
                            data: [9, 9, 8, 7, 8, 7],
                            backgroundColor: 'rgba(16, 185, 129, 0.2)',
                            borderColor: 'rgb(16, 185, 129)',
                            pointBackgroundColor: 'rgb(16, 185, 129)'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Tiến hóa khả năng Code AI Tools'
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 10
                        }
                    }
                }
            });

            // Code Tools Chart
            const codeToolsCtx = document.getElementById('codeToolsChart').getContext('2d');
            new Chart(codeToolsCtx, {
                type: 'bar',
                data: {
                    labels: ['GitHub Copilot', 'Cursor', 'Windsurf', 'Augment', 'Claude Code'],
                    datasets: [
                        {
                            label: 'Tính năng Agent',
                            data: [9, 7, 9, 8, 8],
                            backgroundColor: 'rgba(147, 51, 234, 0.6)'
                        },
                        {
                            label: 'IDE Integration',
                            data: [10, 9, 8, 7, 6],
                            backgroundColor: 'rgba(59, 130, 246, 0.6)'
                        },
                        {
                            label: 'Codebase Awareness',
                            data: [8, 9, 8, 9, 7],
                            backgroundColor: 'rgba(16, 185, 129, 0.6)'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'So sánh khả năng Code Assistant Tools (1-10)'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 10
                        }
                    }
                }
            });
        });
    </script>
</body>

</html>