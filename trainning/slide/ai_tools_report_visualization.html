<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> v<PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }

        .navigation {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .nav-tabs {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .nav-tab {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-tab:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .nav-tab.active {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            transform: translateY(-2px);
        }

        .content-section {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: fadeIn 0.5s ease-in-out;
        }

        .content-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .tool-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #667eea;
            position: relative;
            overflow: hidden;
        }

        .tool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .tool-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .tool-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            font-weight: bold;
        }

        .chatgpt-icon {
            background: linear-gradient(45deg, #10a37f, #1a7f37);
        }

        .perplexity-icon {
            background: linear-gradient(45deg, #1fb6d3, #0891b2);
        }

        .grok-icon {
            background: linear-gradient(45deg, #1da1f2, #0d8bd9);
        }

        .gemini-icon {
            background: linear-gradient(45deg, #4285f4, #1a73e8);
        }

        .copilot-icon {
            background: linear-gradient(45deg, #2188ff, #0969da);
        }

        .cursor-icon {
            background: linear-gradient(45deg, #000000, #333333);
        }

        .tool-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .tool-developer {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .tool-description {
            color: #555;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .features-list {
            list-style: none;
            margin-bottom: 15px;
        }

        .features-list li {
            padding: 5px 0;
            position: relative;
            padding-left: 20px;
        }

        .features-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }

        .pricing-tag {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }

        .btn-learn-more {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .btn-learn-more:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .comparison-table th {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .comparison-table tr:hover {
            background: #f8f9fa;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #333;
        }

        .section-title {
            font-size: 2em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .trend-section {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            color: #2d3436;
        }

        .trend-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #d63031;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            h1 {
                font-size: 2em;
            }

            .tools-grid {
                grid-template-columns: 1fr;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>Báo Cáo Chuyên Sâu về Công Cụ AI</h1>
            <p class="subtitle">Phân tích tổng quan các công cụ Trí tuệ Nhân tạo hiện hành</p>

            <div class="overview-stats">
                <div class="stat-card">
                    <span class="stat-number">2</span>
                    <span>Phần Chính</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">8+</span>
                    <span>Công Cụ AI</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">4</span>
                    <span>Chat Tools</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">4+</span>
                    <span>Code Tools</span>
                </div>
            </div>
        </header>

        <nav class="navigation">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showSection('overview')">Tổng Quan</button>
                <button class="nav-tab" onclick="showSection('chat-tools')">Công Cụ Chat</button>
                <button class="nav-tab" onclick="showSection('code-tools')">Công Cụ Code</button>
                <button class="nav-tab" onclick="showSection('comparison')">So Sánh</button>
                <button class="nav-tab" onclick="showSection('trends')">Xu Hướng</button>
            </div>
        </nav>

        <!-- Overview Section -->
        <div id="overview" class="content-section active">
            <h2 class="section-title">Tổng Quan</h2>

            <div class="trend-section">
                <div class="trend-title">🚀 Xu Hướng Chính</div>
                <p><strong>Chuyển đổi từ Chat đơn giản sang Hệ thống Đa phương thức:</strong> Các công cụ AI không còn
                    chỉ xử lý văn bản mà đã tiến hóa thành các hệ thống có khả năng xử lý hình ảnh, giọng nói, video và
                    thực hiện suy luận phức tạp.</p>

                <p><strong>Xu hướng "Agentic":</strong> Từ việc đơn giản hoàn thành mã, các công cụ AI đang phát triển
                    thành các "tác tử" có khả năng thực hiện tác vụ tự chủ, quản lý dự án và hiểu toàn bộ codebase.</p>

                <p><strong>Mô hình Freemium phổ biến:</strong> Hầu hết các công cụ áp dụng mô hình cho phép trải nghiệm
                    miễn phí với các tính năng nâng cao trả phí.</p>
            </div>

            <p style="margin-bottom: 20px; color: #555; line-height: 1.8;">
                Báo cáo này phân tích chi tiết hai nhóm công cụ AI chính đang định hình tương lai của công việc và phát
                triển phần mềm.
                Từ các chatbot thông minh đến các trợ lý lập trình AI, chúng ta đang chứng kiến một cuộc cách mạng trong
                cách con người tương tác với máy tính.
            </p>
        </div>

        <!-- Chat Tools Section -->
        <div id="chat-tools" class="content-section">
            <h2 class="section-title">Công Cụ Chat AI</h2>

            <div class="tools-grid">
                <!-- ChatGPT -->
                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon chatgpt-icon">🤖</div>
                        <div>
                            <div class="tool-title">ChatGPT</div>
                            <div class="tool-developer">OpenAI</div>
                        </div>
                    </div>
                    <div class="pricing-tag">Freemium</div>
                    <div class="tool-description">
                        Chatbot AI tiên phong sử dụng các mô hình GPT-4o, o-series với khả năng đa phương thức mạnh mẽ.
                    </div>
                    <ul class="features-list">
                        <li>Đa phương thức (văn bản, giọng nói, hình ảnh)</li>
                        <li>Canvas cho cộng tác</li>
                        <li>Custom GPTs</li>
                        <li>Deep Research</li>
                        <li>Tích hợp Apple</li>
                    </ul>
                    <button class="btn-learn-more" onclick="openModal('chatgpt-modal')">Tìm hiểu thêm</button>
                </div>

                <!-- Perplexity AI -->
                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon perplexity-icon">🔍</div>
                        <div>
                            <div class="tool-title">Perplexity AI</div>
                            <div class="tool-developer">Perplexity AI, Inc.</div>
                        </div>
                    </div>
                    <div class="pricing-tag">Freemium</div>
                    <div class="tool-description">
                        Công cụ tìm kiếm AI tập trung vào cung cấp câu trả lời có trích dẫn đáng tin cậy.
                    </div>
                    <ul class="features-list">
                        <li>Câu trả lời có trích dẫn</li>
                        <li>Deep Research với nguồn học thuật</li>
                        <li>Library và Spaces</li>
                        <li>Tích hợp nhiều mô hình AI</li>
                    </ul>
                    <button class="btn-learn-more" onclick="openModal('perplexity-modal')">Tìm hiểu thêm</button>
                </div>

                <!-- Grok -->
                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon grok-icon">⚡</div>
                        <div>
                            <div class="tool-title">Grok</div>
                            <div class="tool-developer">xAI</div>
                        </div>
                    </div>
                    <div class="pricing-tag">X Premium</div>
                    <div class="tool-description">
                        AI chatbot với tích hợp sâu vào X (Twitter), có tính cách "nổi loạn" độc đáo.
                    </div>
                    <ul class="features-list">
                        <li>Tích hợp X (Twitter) thời gian thực</li>
                        <li>Think Mode cho suy luận sâu</li>
                        <li>DeepSearch</li>
                        <li>Chế độ Fun/Regular</li>
                    </ul>
                    <button class="btn-learn-more" onclick="openModal('grok-modal')">Tìm hiểu thêm</button>
                </div>

                <!-- Gemini -->
                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon gemini-icon">💎</div>
                        <div>
                            <div class="tool-title">Gemini</div>
                            <div class="tool-developer">Google</div>
                        </div>
                    </div>
                    <div class="pricing-tag">Freemium</div>
                    <div class="tool-description">
                        Hệ thống AI đa phương thức của Google với khả năng xử lý ngữ cảnh dài và tích hợp Google
                        Workspace.
                    </div>
                    <ul class="features-list">
                        <li>Ngữ cảnh lên đến 2M token</li>
                        <li>Tích hợp Google Workspace</li>
                        <li>Deep Research và Flow</li>
                        <li>Gems và NotebookLM</li>
                    </ul>
                    <button class="btn-learn-more" onclick="openModal('gemini-modal')">Tìm hiểu thêm</button>
                </div>
            </div>
        </div>

        <!-- Code Tools Section -->
        <div id="code-tools" class="content-section">
            <h2 class="section-title">Công Cụ Hỗ Trợ Code</h2>

            <div class="tools-grid">
                <!-- GitHub Copilot -->
                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon copilot-icon">👨‍💻</div>
                        <div>
                            <div class="tool-title">GitHub Copilot</div>
                            <div class="tool-developer">GitHub / OpenAI</div>
                        </div>
                    </div>
                    <div class="pricing-tag">Freemium</div>
                    <div class="tool-description">
                        Lập trình viên cặp AI với tích hợp sâu vào GitHub và hỗ trợ IDE rộng rãi.
                    </div>
                    <ul class="features-list">
                        <li>Hoàn thành mã nhận biết ngữ cảnh</li>
                        <li>Copilot Chat và Agent Mode</li>
                        <li>Copilot Workspace</li>
                        <li>Hỗ trợ nhiều mô hình AI</li>
                        <li>Tích hợp GitHub native</li>
                    </ul>
                    <button class="btn-learn-more" onclick="openModal('copilot-modal')">Tìm hiểu thêm</button>
                </div>

                <!-- Cursor -->
                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon cursor-icon">⚫</div>
                        <div>
                            <div class="tool-title">Cursor</div>
                            <div class="tool-developer">Cursor</div>
                        </div>
                    </div>
                    <div class="pricing-tag">Freemium</div>
                    <div class="tool-description">
                        IDE AI-first với khả năng dự đoán chỉnh sửa tiếp theo và hiểu toàn bộ codebase.
                    </div>
                    <ul class="features-list">
                        <li>Next Edit prediction</li>
                        <li>Nhận biết toàn bộ codebase</li>
                        <li>Chế độ riêng tư</li>
                        <li>Hỗ trợ MCP</li>
                        <li>Chỉnh sửa bằng ngôn ngữ tự nhiên</li>
                    </ul>
                    <button class="btn-learn-more" onclick="openModal('cursor-modal')">Tìm hiểu thêm</button>
                </div>

                <!-- More tools can be added here -->
                <div class="tool-card">
                    <div class="tool-header">
                        <div class="tool-icon" style="background: linear-gradient(45deg, #ff6b6b, #ee5a52);">🔧</div>
                        <div>
                            <div class="tool-title">Các Công Cụ Khác</div>
                            <div class="tool-developer">Đa dạng</div>
                        </div>
                    </div>
                    <div class="pricing-tag">Varies</div>
                    <div class="tool-description">
                        Augment, Windsurf, ChatGPT Codex, Google Jules, Claude Code, Zencoder và nhiều công cụ khác.
                    </div>
                    <ul class="features-list">
                        <li>Khả năng Agentic</li>
                        <li>Hỗ trợ MCP</li>
                        <li>Tích hợp IDE</li>
                        <li>Mô hình cục bộ</li>
                    </ul>
                    <button class="btn-learn-more" onclick="openModal('other-tools-modal')">Tìm hiểu thêm</button>
                </div>
            </div>
        </div>

        <!-- Comparison Section -->
        <div id="comparison" class="content-section">
            <h2 class="section-title">Bảng So Sánh</h2>

            <h3 style="margin-bottom: 15px; color: #2c3e50;">Công Cụ Chat AI</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Công Cụ</th>
                        <th>Nhà Phát Triển</th>
                        <th>Mô Hình Cốt Lõi</th>
                        <th>Tính Năng Độc Đáo</th>
                        <th>Mô Hình Định Giá</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>ChatGPT</strong></td>
                        <td>OpenAI</td>
                        <td>GPT-4o, o-series</td>
                        <td>Đa phương thức, Canvas, Custom GPTs</td>
                        <td>Freemium</td>
                    </tr>
                    <tr>
                        <td><strong>Perplexity AI</strong></td>
                        <td>Perplexity AI</td>
                        <td>GPT-4, Gemini, Claude</td>
                        <td>Câu trả lời có trích dẫn</td>
                        <td>Freemium</td>
                    </tr>
                    <tr>
                        <td><strong>Grok</strong></td>
                        <td>xAI</td>
                        <td>Grok-3, Grok-2</td>
                        <td>Tích hợp X, Think Mode</td>
                        <td>X Premium</td>
                    </tr>
                    <tr>
                        <td><strong>Gemini</strong></td>
                        <td>Google</td>
                        <td>Gemini 2.5 Pro</td>
                        <td>Ngữ cảnh dài, tích hợp Workspace</td>
                        <td>Freemium</td>
                    </tr>
                </tbody>
            </table>

            <h3 style="margin: 30px 0 15px; color: #2c3e50;">Công Cụ Hỗ Trợ Code</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Công Cụ</th>
                        <th>Môi Trường Chính</th>
                        <th>Tính Năng AI Chính</th>
                        <th>Hỗ Trợ MCP</th>
                        <th>Mô Hình Định Giá</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>GitHub Copilot</strong></td>
                        <td>Plugin IDE</td>
                        <td>Hoàn thành mã, Agent Mode</td>
                        <td>Có</td>
                        <td>Freemium</td>
                    </tr>
                    <tr>
                        <td><strong>Cursor</strong></td>
                        <td>IDE AI-first</td>
                        <td>Next Edit, Codebase awareness</td>
                        <td>Có</td>
                        <td>Freemium</td>
                    </tr>
                    <tr>
                        <td><strong>Augment</strong></td>
                        <td>Plugin IDE</td>
                        <td>Agent, Context Engine</td>
                        <td>Có</td>
                        <td>Freemium</td>
                    </tr>
                    <tr>
                        <td><strong>Windsurf</strong></td>
                        <td>IDE Agentic</td>
                        <td>Flows, Cascade</td>
                        <td>Có</td>
                        <td>Freemium</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Trends Section -->
        <div id="trends" class="content-section">
            <h2 class="section-title">Xu Hướng & Tương Lai</h2>

            <div class="trend-section">
                <div class="trend-title">🔮 Xu Hướng Chat AI</div>
                <p><strong>Chuyển đổi đa phương thức:</strong> Từ văn bản đơn thuần sang xử lý hình ảnh, giọng nói,
                    video. ChatGPT GPT-4o, Grok với Aurora, Gemini với Veo 3.</p>

                <p><strong>Tích hợp dữ liệu thời gian thực:</strong> Grok với X, Perplexity với web search, ChatGPT với
                    Deep Research.</p>

                <p><strong>Mô hình Freemium:</strong> Tất cả công cụ hàng đầu đều áp dụng chiến lược này để thu hút
                    người dùng và tạo doanh thu.</p>
            </div>

            <div class="trend-section">
                <div class="trend-title">💻 Xu Hướng Code AI</div>
                <p><strong>Từ Copilot sang Agent:</strong> GitHub Copilot Agent, Cursor với codebase awareness, Jules
                    với autonomous coding.</p>

                <p><strong>Tích hợp IDE sâu:</strong> Cursor, Windsurf là IDE AI-first, các plugin cho VS Code/JetBrains
                    ngày càng phức tạp.</p>

                <p><strong>Quyền riêng tư & mô hình cục bộ:</strong> Void Editor, Cursor Privacy Mode, hỗ trợ Ollama,
                    LMStudio.</p>

                <p><strong>Model Context Protocol (MCP):</strong> Tiêu chuẩn mới cho việc tích hợp công cụ và dịch vụ
                    bên ngoài.</p>
            </div>

            <div class="trend-section">
                <div class="trend-title">🚀 Dự Đoán Tương Lai</div>
                <p><strong>AI Agent trở thành chuẩn:</strong> Từ trợ lý đơn giản sang các tác tử tự chủ có thể thực hiện
                    tác vụ phức tạp.</p>

                <p><strong>Tích hợp sâu hơn vào workflow:</strong> AI sẽ không chỉ hỗ trợ mà trở thành phần không thể
                    thiếu trong quy trình làm việc.</p>

                <p><strong>Cạnh tranh về quyền riêng tư:</strong> Các giải pháp AI cục bộ và riêng tư sẽ ngày càng quan
                    trọng.</p>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- ChatGPT Modal -->
    <div id="chatgpt-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>ChatGPT - OpenAI</h2>
            <h3>Giới thiệu</h3>
            <p>ChatGPT là chatbot AI tạo sinh tiên phong được phát triển bởi OpenAI, ra mắt tháng 11/2022. Sử dụng các
                mô hình GPT-4o và o-series cho khả năng đa phương thức mạnh mẽ.</p>

            <h3>Tính năng nổi bật</h3>
            <ul>
                <li><strong>Đa phương thức:</strong> Xử lý văn bản, hình ảnh, giọng nói, video</li>
                <li><strong>Canvas:</strong> Không gian làm việc tương tác cho cộng tác</li>
                <li><strong>Custom GPTs:</strong> Tạo trợ lý AI tùy chỉnh</li>
                <li><strong>Deep Research:</strong> Nghiên cứu sâu từ nhiều nguồn</li>
                <li><strong>Memory:</strong> Ghi nhớ thông tin người dùng</li>
            </ul>

            <h3>Gói dịch vụ</h3>
            <ul>
                <li><strong>Miễn phí:</strong> GPT-4o mini, truy cập hạn chế</li>
                <li><strong>Plus ($20/tháng):</strong> GPT-4o, giới hạn mở rộng</li>
                <li><strong>Pro ($200/tháng):</strong> Không giới hạn, o1 pro mode</li>
                <li><strong>Team/Enterprise:</strong> Tính năng doanh nghiệp</li>
            </ul>
        </div>
    </div>

    <!-- Perplexity Modal -->
    <div id="perplexity-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Perplexity AI</h2>
            <h3>Giới thiệu</h3>
            <p>Perplexity AI là công cụ tìm kiếm AI tập trung vào việc cung cấp câu trả lời có trích dẫn đáng tin cậy,
                hoạt động như một công cụ hội thoại tập trung vào nghiên cứu.</p>

            <h3>Tính năng độc đáo</h3>
            <ul>
                <li><strong>Trích dẫn nguồn:</strong> Mỗi câu trả lời đều có nguồn trích dẫn</li>
                <li><strong>Academic Filter:</strong> Tìm kiếm trong nguồn học thuật</li>
                <li><strong>Deep Research:</strong> Nghiên cứu đa bước từ nhiều nguồn</li>
                <li><strong>Library & Spaces:</strong> Tổ chức và chia sẻ nghiên cứu</li>
            </ul>

            <h3>Ưu điểm</h3>
            <ul>
                <li>Độ tin cậy cao với nguồn trích dẫn</li>
                <li>Tích hợp nhiều mô hình AI</li>
                <li>Interface tập trung vào nghiên cứu</li>
                <li>API cho developers</li>
            </ul>
        </div>
    </div>

    <!-- Grok Modal -->
    <div id="grok-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Grok - xAI</h2>
            <h3>Giới thiệu</h3>
            <p>Grok là AI chatbot của xAI với tích hợp sâu vào X (Twitter), có tính cách "nổi loạn" độc đáo và khả năng
                truy cập dữ liệu thời gian thực.</p>

            <h3>Tính năng độc đáo</h3>
            <ul>
                <li><strong>Tích hợp X:</strong> Truy cập dữ liệu Twitter thời gian thực</li>
                <li><strong>Think Mode:</strong> Suy luận sâu cho các vấn đề phức tạp</li>
                <li><strong>DeepSearch:</strong> Tìm kiếm và phân tích web sâu</li>
                <li><strong>Fun/Regular Mode:</strong> Điều chỉnh tính cách phản hồi</li>
            </ul>

            <h3>Mô hình truy cập</h3>
            <ul>
                <li><strong>Miễn phí:</strong> Giới hạn trên X</li>
                <li><strong>X Premium:</strong> Truy cập đầy đủ</li>
                <li><strong>X Premium+:</strong> Tính năng nâng cao</li>
                <li><strong>API Enterprise:</strong> Cho doanh nghiệp</li>
            </ul>
        </div>
    </div>

    <!-- Gemini Modal -->
    <div id="gemini-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Gemini - Google</h2>
            <h3>Giới thiệu</h3>
            <p>Gemini là hệ thống AI đa phương thức của Google với khả năng xử lý ngữ cảnh dài và tích hợp sâu với
                Google Workspace.</p>

            <h3>Tính năng nổi bật</h3>
            <ul>
                <li><strong>Ngữ cảnh dài:</strong> Lên đến 2M token</li>
                <li><strong>Tích hợp Workspace:</strong> Gmail, Docs, Sheets, Drive</li>
                <li><strong>Deep Research:</strong> Nghiên cứu sâu từ Google Search</li>
                <li><strong>Gems:</strong> Trợ lý AI tùy chỉnh</li>
                <li><strong>NotebookLM:</strong> Phân tích tài liệu thông minh</li>
            </ul>

            <h3>Phiên bản</h3>
            <ul>
                <li><strong>Gemini 2.5 Pro:</strong> Phiên bản mạnh nhất</li>
                <li><strong>Gemini 2.5 Flash:</strong> Nhanh và hiệu quả</li>
                <li><strong>Imagen 4:</strong> Tạo hình ảnh</li>
                <li><strong>Veo 3:</strong> Tạo video</li>
            </ul>
        </div>
    </div>

    <!-- GitHub Copilot Modal -->
    <div id="copilot-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>GitHub Copilot</h2>
            <h3>Giới thiệu</h3>
            <p>GitHub Copilot là lập trình viên cặp AI được phát triển bởi GitHub và OpenAI, tích hợp sâu vào GitHub và
                các IDE phổ biến.</p>

            <h3>Tính năng chính</h3>
            <ul>
                <li><strong>Code Completion:</strong> Hoàn thành mã thông minh</li>
                <li><strong>Copilot Chat:</strong> Trò chuyện về code</li>
                <li><strong>Agent Mode:</strong> Thực hiện tác vụ tự chủ</li>
                <li><strong>Workspace:</strong> Quản lý dự án</li>
                <li><strong>Code Review:</strong> Đánh giá mã tự động</li>
            </ul>

            <h3>Hỗ trợ IDE</h3>
            <ul>
                <li>VS Code</li>
                <li>Visual Studio</li>
                <li>JetBrains IDEs</li>
                <li>Neovim</li>
                <li>GitHub.com</li>
            </ul>

            <h3>Mô hình giá</h3>
            <ul>
                <li><strong>Free:</strong> 50 requests/tháng</li>
                <li><strong>Pro ($10/tháng):</strong> Unlimited</li>
                <li><strong>Business/Enterprise:</strong> Tính năng doanh nghiệp</li>
            </ul>
        </div>
    </div>

    <!-- Cursor Modal -->
    <div id="cursor-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Cursor</h2>
            <h3>Giới thiệu</h3>
            <p>Cursor là IDE AI-first được thiết kế để tích hợp sâu AI vào quy trình lập trình, với khả năng dự đoán
                chỉnh sửa tiếp theo và hiểu toàn bộ codebase.</p>

            <h3>Tính năng độc đáo</h3>
            <ul>
                <li><strong>Next Edit Prediction:</strong> Dự đoán chỉnh sửa tiếp theo</li>
                <li><strong>Codebase Awareness:</strong> Hiểu toàn bộ dự án</li>
                <li><strong>Natural Language Editing:</strong> Chỉnh sửa bằng ngôn ngữ tự nhiên</li>
                <li><strong>Privacy Mode:</strong> Bảo mật mã nguồn</li>
                <li><strong>MCP Support:</strong> Tích hợp công cụ bên ngoài</li>
            </ul>

            <h3>Ưu điểm</h3>
            <ul>
                <li>IDE được thiết kế từ đầu cho AI</li>
                <li>Import settings từ VS Code dễ dàng</li>
                <li>Nhiều mô hình AI hỗ trợ</li>
                <li>SOC 2 certified</li>
            </ul>

            <h3>Giá cả</h3>
            <ul>
                <li><strong>Hobby:</strong> Miễn phí với giới hạn</li>
                <li><strong>Pro ($20/tháng):</strong> Unlimited fast requests</li>
                <li><strong>Business ($40/user/tháng):</strong> Team features</li>
            </ul>
        </div>
    </div>

    <!-- Other Tools Modal -->
    <div id="other-tools-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Các Công Cụ Code AI Khác</h2>

            <h3>Augment</h3>
            <p>Plugin cho JetBrains với Context Engine hiểu sâu codebase và khả năng Agent.</p>

            <h3>Windsurf (Codeium)</h3>
            <p>IDE Agentic với Flows và Cascade cho việc hiểu codebase sâu và chỉnh sửa đa tệp.</p>

            <h3>ChatGPT Codex</h3>
            <p>Tác tử kỹ thuật phần mềm trong ChatGPT, có thể viết tính năng, sửa lỗi, tạo PR.</p>

            <h3>Google Jules</h3>
            <p>Tác tử mã hóa tự chủ, không đồng bộ từ Google Labs, tích hợp GitHub.</p>

            <h3>Claude Code</h3>
            <p>CLI tool từ Anthropic cho việc chỉnh sửa tệp, sửa lỗi, tích hợp Git.</p>

            <h3>Zencoder</h3>
            <p>Plugin với Zen Agents, Repo Grokking và hỗ trợ MCP tùy chỉnh.</p>

            <h3>Xu hướng chung</h3>
            <ul>
                <li>Chuyển từ completion sang agent</li>
                <li>Tích hợp IDE sâu</li>
                <li>Hỗ trợ MCP</li>
                <li>Bảo mật và privacy</li>
                <li>Mô hình cục bộ</li>
            </ul>
        </div>
    </div>

    <script>
        // Navigation functionality
        function showSection(sectionId) {
            // Hide all sections
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionId).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Modal functionality
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking on close button or outside modal
        document.addEventListener('click', function (event) {
            if (event.target.classList.contains('close')) {
                event.target.closest('.modal').style.display = 'none';
                document.body.style.overflow = 'auto';
            }

            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function (event) {
            if (event.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (modal.style.display === 'block') {
                        modal.style.display = 'none';
                        document.body.style.overflow = 'auto';
                    }
                });
            }
        });

        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add animation on scroll
        function animateOnScroll() {
            const elements = document.querySelectorAll('.tool-card, .stat-card');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            elements.forEach(element => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(30px)';
                element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(element);
            });
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', animateOnScroll);
    </script>
</body>

</html>