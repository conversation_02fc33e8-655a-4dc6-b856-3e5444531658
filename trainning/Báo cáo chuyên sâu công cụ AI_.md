

# **<PERSON><PERSON><PERSON> c<PERSON>o <PERSON>ê<PERSON> sâu về các Công cụ Trí tuệ Nhân tạo Hiện hành**

**Phần I: Công cụ <PERSON> (Chat Tools)**

Sự phát triển của các mô hình ngôn ngữ lớn (LLM) đã thúc đẩy một cuộc cách mạng trong tương tác giữa người và máy, với các công cụ chat AI ngày càng trở nên tinh vi và đa năng. C<PERSON><PERSON> công cụ này không còn đơn thuần là những chatbot trả lời câu hỏi đơn giản mà đã tiến hóa thành những trợ lý ảo có khả năng hiểu ngữ cảnh phức tạp, xử lý đa dạng loại hình thông tin (v<PERSON><PERSON> b<PERSON><PERSON>, h<PERSON><PERSON>, gi<PERSON><PERSON> nói) và thực hiện các tác vụ suy luận sâu. Một xu hướng rõ nét là sự chuyển dịch từ các mô hình chỉ dựa trên văn bản sang các hệ thống đa phương thức, có khả năng xử lý và tạo ra nhiều loại nội dung khác nhau. Điều này được thể hiện qua việc ChatGPT tích hợp GPT-4o và các mô hình dòng "o" 1, Grok với chế độ "Think" 3, và Gemini với phiên bản 2.5 Pro.4 Perplexity AI, mặt khác, tạo sự khác biệt bằng cách tập trung vào việc cung cấp câu trả lời kèm theo nguồn trích dẫn đáng tin cậy, hoạt động như một công cụ hội thoại tập trung vào nghiên cứu.6

Sự phát triển này phản ánh nhu cầu ngày càng tăng về các công cụ AI có khả năng hỗ trợ đa dạng các tác vụ, từ trả lời câu hỏi đơn giản đến giải quyết vấn đề phức tạp. Người dùng có thể kỳ vọng vào các công cụ chat ngày càng linh hoạt và đáng tin cậy hơn. Tuy nhiên, sự phức tạp gia tăng cũng đồng nghĩa với việc hiệu suất có thể khác nhau giữa các phương thức và tác vụ suy luận khác nhau.

Để duy trì hoạt động và phát triển, hầu hết các công cụ chat hàng đầu như ChatGPT 8, Perplexity 10, Gemini 4, và Grok (thông qua X Premium 11) đều áp dụng mô hình kinh doanh freemium. Mô hình này cho phép người dùng trải nghiệm các tính năng cơ bản miễn phí, trong khi các tính năng nâng cao và giới hạn sử dụng cao hơn được cung cấp qua các gói đăng ký trả phí. Chi phí tính toán cao của việc vận hành các LLM đòi hỏi một chiến lược kiếm tiền hiệu quả. Freemium giúp thu hút lượng lớn người dùng và làm quen sản phẩm, sau đó các gói trả phí sẽ khai thác giá trị từ những người dùng có nhu cầu cao hơn về khả năng, giới hạn sử dụng hoặc các tính năng chuyên biệt như nghiên cứu sâu của ChatGPT 1, truy vấn Pro của Perplexity 10, quyền truy cập Gemini 2.5 Pro 4, hay các tính năng nâng cao của Grok cho người dùng X Premium+.11 Cách tiếp cận theo tầng này giúp các công ty cân bằng giữa tăng trưởng người dùng và tạo doanh thu, đồng thời phục vụ các phân khúc người dùng khác nhau từ cá nhân, chuyên gia đến doanh nghiệp. Điều này ngụ ý rằng mặc dù quyền truy cập cơ bản vào AI mạnh mẽ ngày càng phổ biến, người dùng cần hiệu suất cao, ổn định hoặc các tính năng chuyên biệt có thể sẽ phải trả phí. Sự khác biệt trong các gói trả phí thường xoay quanh quyền truy cập vào các mô hình mới nhất, giới hạn sử dụng cao hơn và các tính năng dành riêng cho doanh nghiệp như bảo mật và kiểm soát quản trị.

Một yếu tố khác biệt quan trọng giữa các công cụ chat là khả năng tích hợp với dữ liệu thời gian thực và các nguồn dữ liệu cụ thể. Grok nhấn mạnh vào việc truy cập dữ liệu thời gian thực, đặc biệt từ X (Twitter) 12, trong khi Perplexity tập trung vào các câu trả lời từ web có trích dẫn.6 ChatGPT và Gemini cũng cung cấp khả năng duyệt web.1 Một hạn chế lớn của các LLM đời đầu là giới hạn kiến thức tại một thời điểm nhất định. Để duy trì sự liên quan và cung cấp thông tin chính xác về các sự kiện hiện tại hoặc các chủ đề cụ thể, việc tích hợp với dữ liệu web thời gian thực là rất quan trọng. Điểm độc đáo của Grok là sự tích hợp sâu với X 12, cung cấp thông tin chi tiết từ các cuộc thảo luận trực tiếp trên mạng xã hội. Perplexity xây dựng danh tiếng dựa trên việc cung cấp các câu trả lời có trích dẫn từ các tìm kiếm trên web.6 ChatGPT 1 và Gemini 4 đã tích hợp tìm kiếm web để khắc phục những hạn chế về kiến thức tĩnh. Xu hướng này cho thấy tầm quan trọng của việc nền tảng hóa các phản hồi LLM bằng thông tin có thể kiểm chứng và cập nhật. Do đó, độ tin cậy và tiện ích của các công cụ chat sẽ ngày càng phụ thuộc vào chất lượng và phạm vi truy cập dữ liệu thời gian thực cũng như khả năng trích dẫn nguồn của chúng. Các công cụ có quyền truy cập dữ liệu độc quyền (như Grok tích hợp X) có thể mang lại lợi thế chuyên biệt cho một số trường hợp sử dụng nhất định.

**Bảng 1: Tổng quan so sánh các Công cụ Chat AI**

| Tên Công cụ | Nhà phát triển | Mô hình Cốt lõi (nếu có) | Tính năng Độc đáo Chính | Mô hình Định giá | Truy cập Dữ liệu Thời gian thực |
| :---- | :---- | :---- | :---- | :---- | :---- |
| ChatGPT | OpenAI | GPT-4o, GPT-4.1, o-series (o1, o3, o4-mini) | Đa phương thức (văn bản, giọng nói, hình ảnh), Canvas, Custom GPTs, Deep Research, tích hợp Apple | Freemium, Plus, Pro, Team, Enterprise, Edu | Có (Duyệt web, Deep Research) |
| Perplexity AI | Perplexity AI, Inc. | GPT-4.1, Gemini 2.5 Pro, Claude 3.7 Sonnet, Grok-2 | Tập trung vào câu trả lời có trích dẫn, Deep Research với tùy chọn tập trung vào nguồn học thuật, Library, Spaces | Freemium, Pro, Enterprise Pro | Có (Tìm kiếm web, Deep Research) |
| Grok | xAI | Grok-3, Grok-2, Aurora (cho hình ảnh) | Tích hợp sâu với X (Twitter) theo thời gian thực, cá tính "nổi loạn", chế độ Fun/Regular, Think Mode, DeepSearch | Miễn phí (giới hạn) trên X, X Premium, X Premium+, API doanh nghiệp | Có (X, duyệt web, DeepSearch) |
| Gemini | Google | Gemini 2.5 Pro, 2.5 Flash, Imagen 4, Veo 3 | Đa phương thức mạnh mẽ, ngữ cảnh dài (lên đến 2M token), tích hợp sâu với Google Workspace & Chrome, Deep Research, Flow, Gems, NotebookLM | Freemium, Google AI Pro, Gemini cho Workspace (Business/Enterprise), Code Assist, API | Có (Google Search, Deep Research) |

---

**1\. ChatGPT (OpenAI)**

* **1.1. Giới thiệu (Introduction):**  
  * ChatGPT là một chatbot trí tuệ nhân tạo tạo sinh được phát triển bởi OpenAI, ra mắt vào tháng 11 năm 2022\.2 Công cụ này sử dụng các mô hình ngôn ngữ lớn (LLM) như GPT-4o và các mô hình đa phương thức khác để tạo ra các phản hồi giống như con người dưới dạng văn bản, giọng nói và hình ảnh.1  
  * Nó được thiết kế để hỗ trợ một loạt các tác vụ bao gồm viết lách, học tập, động não, lập trình, tóm tắt thông tin và nhiều hơn nữa, nhằm mục đích nâng cao năng suất và sự sáng tạo của người dùng.1  
* **1.2. Lịch sử phát triển (Development History):**  
  * **Ra mắt lần đầu:** Ngày 30 tháng 11 năm 2022, dưới dạng một bản xem trước nghiên cứu miễn phí.2  
  * **Công nghệ nền tảng:** Được xây dựng dựa trên chuỗi mô hình Generative Pre-trained Transformer (GPT) độc quyền của OpenAI. Nó được tinh chỉnh cho các ứng dụng hội thoại bằng cách sử dụng kết hợp học có giám sát và học tăng cường từ phản hồi của con người (RLHF), xem xét ngữ cảnh hội thoại ở mỗi giai đoạn.2  
  * Các cột mốc mô hình chính 2:  
    * **GPT-3.5:** Được sử dụng trong phiên bản ChatGPT ban đầu (tháng 11 năm 2022).  
    * **GPT-4:** Được giới thiệu vào tháng 3 năm 2023, ban đầu có sẵn thông qua gói đăng ký ChatGPT Plus, cung cấp các khả năng nâng cao.  
    * **GPT-4o:** Ra mắt vào tháng 5 năm 2024, một mô hình đa phương thức (xử lý văn bản, hình ảnh, âm thanh, video) nhanh hơn và có khả năng cao hơn, được cung cấp cho người dùng miễn phí với các giới hạn nhất định và giới hạn cao hơn cho người đăng ký trả phí.  
    * **GPT-4o mini:** Một phiên bản nhỏ hơn, rẻ hơn của GPT-4o, thay thế GPT-3.5 vào tháng 7 năm 2024\.  
    * **Dòng "o" cho Suy luận (o1, o3, o4-mini):**  
      * **o1-preview & o1-mini:** Được giới thiệu vào tháng 9 năm 2024, được thiết kế để "suy nghĩ" trước khi phản hồi các yêu cầu phức tạp.  
      * **o1 & o1-pro:** Bản phát hành đầy đủ vào tháng 12 năm 2024, với phiên bản "pro" dành cho người dùng ChatGPT Pro cung cấp kết quả tốt hơn thông qua nhiều tài nguyên tính toán hơn.  
      * **o3-mini & o3-mini-high:** Kế nhiệm o1-mini, phát hành tháng 1 năm 2025\.  
      * **o3 & o3-pro:** Bản phát hành đầy đủ tháng 4 năm 2025 (o3), tháng 6 năm 2025 (o3-pro), nhấn mạnh vào suy luận có cấu trúc và hiệu suất nhanh hơn.  
      * **o4-mini & o4-mini-high:** Các phiên bản nhỏ gọn, hiệu quả cao của dòng o4 sắp tới, phát hành tháng 4 năm 2025\.  
    * **GPT-4.5:** Một mô hình rất lớn, được cho là "mô hình phi chuỗi suy nghĩ cuối cùng" của OpenAI, phát hành tháng 2 năm 2025\.  
    * **GPT-4.1 & GPT-4.1 mini:** Ra mắt trong API tháng 4 năm 2025, được thêm vào ChatGPT tháng 5 năm 2025\. GPT-4.1 mini thay thế GPT-4o mini.  
  * **Bối cảnh lịch sử:** Sự phát triển của AI, bao gồm các khái niệm như mạng nơ-ron và học máy, bắt nguồn từ giữa thế kỷ 20\. Việc phát hành ChatGPT năm 2022 đã đánh dấu một minh chứng công khai quan trọng về tiềm năng của AI trong giao tiếp.15  
* **1.3. Các tính năng hiện tại đang có (Current Features):**  
  * AI hội thoại cốt lõi 14:  
    Trả lời câu hỏi và giải thích khái niệm; soạn thảo, viết lại hoặc tóm tắt nội dung; cung cấp các đề xuất sáng tạo (ví dụ: viết truyện hoặc ý tưởng); giải quyết vấn đề thông qua suy luận logic; dịch giữa các ngôn ngữ. Nó hiểu ngôn ngữ tự nhiên và có thể tuân theo các hướng dẫn phức tạp, ghi nhớ các lượt trước đó trong một cuộc trò chuyện và điều chỉnh phản hồi của nó cho phù hợp với ngữ cảnh. Khách hàng sử dụng gói trả phí có thể lựa chọn giữa một số mô hình nền tảng.  
  * **Khả năng đa phương thức:**  
    * **Đầu vào văn bản, giọng nói, hình ảnh:** Tương tác thông qua gõ phím, hội thoại giọng nói thời gian thực (ứng dụng di động), hoặc bằng cách tải lên/chụp ảnh.1  
    * **Tạo ảnh:** Tạo ảnh gốc từ mô tả văn bản hoặc biến đổi ảnh hiện có.1  
    * **Chế độ giọng nói nâng cao:** Hội thoại thời gian thực với lựa chọn giọng nói; bao gồm chia sẻ video và màn hình cho người dùng Plus/Pro/Team.1  
  * **Tích hợp Web & Nghiên cứu:**  
    * **Tìm kiếm (Duyệt Web):** Truy cập thông tin thời gian thực từ web, cung cấp câu trả lời kịp thời kèm liên kết đến nguồn.1  
    * **Nghiên cứu sâu (Deep Research):** (Có sẵn cho các gói trả phí, giới hạn cho gói Miễn phí) Một tác nhân tiến hành nghiên cứu đa bước, tổng hợp nội dung từ nhiều nguồn trực tuyến và tạo ra các báo cáo có cấu trúc, được trích dẫn. Hữu ích cho việc xây dựng chiến lược, báo cáo và tổng quan tài liệu.1 Ra mắt lần đầu vào tháng 2 năm 2025, được cung cấp bởi một phiên bản o3 chuyên biệt.17  
  * **Năng suất & Tương tác dữ liệu:**  
    * **Tải lên tệp (Tài liệu & Dữ liệu):** Tải lên tệp PDF, bản trình bày, tài liệu văn bản, bảng tính (CSV) để tóm tắt, trích xuất thông tin, hỏi đáp, phân tích dữ liệu và tạo biểu đồ.1  
    * **Phân tích dữ liệu:** Chạy mã Python trong môi trường an toàn để phân tích và trực quan hóa dữ liệu.1  
    * **Canvas:** Không gian làm việc tương tác để đồng sáng tác, chỉnh sửa hoặc gỡ lỗi cùng với ChatGPT, hỗ trợ đánh dấu, đề xuất nội tuyến, tải lên tệp và chỉnh sửa mã trong không gian chung.1  
  * **Cá nhân hóa & Tổ chức:**  
    * **Bộ nhớ (Memory):** Khi được bật, ChatGPT ghi nhớ các thông tin người dùng chia sẻ (sở thích, mục tiêu) để cá nhân hóa các phản hồi trong tương lai. Người dùng có thể xem, chỉnh sửa hoặc xóa bộ nhớ.8  
    * **Dự án (Projects):** Tổ chức các cuộc trò chuyện, tệp và ngữ cảnh theo một mục tiêu chung, lý tưởng cho các quy trình làm việc nhiều phiên hoặc các nỗ lực hợp tác.14  
  * **Tùy chỉnh & Mở rộng:**  
    * **GPT tùy chỉnh (Custom GPTs):** Người dùng có thể xây dựng trợ lý AI của riêng mình với hướng dẫn tùy chỉnh, tệp tải lên (nguồn kiến thức) và các công cụ được chọn. Chúng có thể được chia sẻ hoặc xuất bản.1  
    * **Cửa hàng GPT (GPT Store):** Một thư mục công khai để duyệt, sử dụng hoặc xuất bản các trợ lý chuyên biệt cho từng tác vụ do người khác xây dựng, được phân loại theo chức năng (viết, mã hóa, v.v.).14  
  * **Lập trình & Phát triển:**  
    * **Tạo & Gỡ lỗi mã:** Tạo và gỡ lỗi mã, tự động hóa các tác vụ lặp đi lặp lại, học API mới.1  
    * **Chỉnh sửa mã (Ứng dụng máy tính để bàn):** Có sẵn với ứng dụng máy tính để bàn ChatGPT cho macOS.1  
  * **Mô hình suy luận nâng cao:** Truy cập vào các mô hình như OpenAI o1, o3, o4-mini (tùy thuộc vào gói) được đào tạo để dành nhiều thời gian hơn cho việc suy nghĩ đối với các vấn đề phức tạp.1  
  * **Quan hệ đối tác:** Tích hợp với các trải nghiệm của Apple (iOS, iPadOS, macOS) được công bố tại WWDC tháng 6 năm 2024\.1  
  * **Tác vụ theo lịch (Truy cập hạn chế):** Một số người dùng có thể đặt ChatGPT để chủ động thực hiện các tác vụ trong tương lai, như gửi lời nhắc hoặc chạy phân tích.14  
  * **Tạo video Sora (Truy cập hạn chế):** Quyền truy cập hạn chế vào tính năng tạo video Sora cho người dùng Plus/Pro.8  
* 1.4. License / Subscription 1:  
  * **ChatGPT Miễn phí:**  
    * Chi phí: $0/tháng.  
    * Tính năng: Truy cập GPT-4o mini, dữ liệu web thời gian thực với tìm kiếm, quyền truy cập hạn chế vào GPT-4o, OpenAI o4-mini và nghiên cứu sâu. Quyền truy cập hạn chế vào tải lên tệp, phân tích dữ liệu, tạo hình ảnh và chế độ giọng nói. Chỉnh sửa mã bằng ứng dụng máy tính để bàn macOS. Sử dụng GPT tùy chỉnh.  
    * Hạn chế: Giới hạn dung lượng hàng ngày đối với GPT-4o và tải lên tệp, quyền truy cập hạn chế vào các tính năng cao cấp, thời gian phản hồi chậm hơn, giới hạn tin nhắn thấp hơn.  
  * **ChatGPT Plus:**  
    * Chi phí: $20/tháng.  
    * Đối tượng: Cá nhân cần hiệu suất tốt hơn và các công cụ AI tiên tiến.  
    * Tính năng: Mọi thứ trong gói Miễn phí, cộng thêm: Giới hạn mở rộng về tin nhắn, tải lên tệp, phân tích dữ liệu, tạo hình ảnh. Chế độ giọng nói tiêu chuẩn và nâng cao với chia sẻ video và màn hình. Truy cập nghiên cứu sâu và nhiều mô hình suy luận (OpenAI o3‑mini, OpenAI o3‑mini‑high và OpenAI o1). Truy cập bản xem trước nghiên cứu của GPT‑4.5 và GPT‑4.1 (tối ưu hóa cho các tác vụ mã hóa). Tạo và sử dụng dự án, tác vụ và GPT tùy chỉnh. Quyền truy cập hạn chế vào tạo video Sora. Cụ thể, 80 tin nhắn GPT-4o mỗi 3 giờ (tin nhắn GPT-4o mini không giới hạn).  
  * **ChatGPT Pro:**  
    * Chi phí: $200/tháng.  
    * Đối tượng: Chuyên gia, nhà phát triển và người dùng thành thạo cần các khả năng AI tiên tiến nhất.  
    * Tính năng: Tất cả các tính năng của gói ChatGPT Team, cộng thêm: Quyền truy cập không giới hạn vào GPT-4o, các mô hình suy luận (bao gồm o1, chế độ o1 pro) và chế độ Giọng nói Nâng cao. 120 truy vấn nghiên cứu sâu mỗi tháng. Quyền truy cập vào tác nhân duyệt web của OpenAI (Operator) và tạo video Sora mở rộng. Giới hạn cao hơn về chia sẻ video và màn hình.  
  * **ChatGPT Team:**  
    * Chi phí: $25–$30/người dùng/tháng.8  
    * Đối tượng: Doanh nghiệp nhỏ và các nhóm cần AI để cộng tác và tự động hóa quy trình làm việc.  
    * Tính năng: Tất cả các ưu đãi của ChatGPT Plus với giới hạn cao hơn cho GPT-4, GPT-4o và các mô hình AI. Khả năng xây dựng và chia sẻ các ứng dụng/GPT tùy chỉnh được hỗ trợ bởi AI trong một không gian làm việc chuyên dụng. Công cụ quản trị để quản lý nhóm. Không đào tạo dữ liệu trên các cuộc trò chuyện của nhóm.  
  * **ChatGPT Enterprise:**  
    * Chi phí: Giá tùy chỉnh.8  
    * Đối tượng: Các tổ chức lớn cần AI tiên tiến, bảo mật và kiểm soát quản trị.  
    * Tính năng: Quyền riêng tư, bảo mật và kiểm soát quản trị cấp doanh nghiệp. Đăng nhập một lần (SSO), xác minh tên miền và phân tích người dùng. Quyền truy cập ưu tiên vào các mô hình và chuyên môn của OpenAI. Quyền truy cập không giới hạn vào GPT-4o, cửa sổ ngữ cảnh mở rộng.  
  * **ChatGPT Edu:**  
    * Chi phí: Dành cho các trường đại học (giá không được nêu chi tiết nhưng nhắm vào lĩnh vực này).  
    * Đối tượng: Trường đại học, giảng viên và sinh viên.  
    * Tính năng: Bảo mật nâng cao, công cụ quản trị và tạo chatbot tùy chỉnh. Các mô hình OpenAI mới nhất và giới hạn tin nhắn cao hơn.  
  * **OpenAI cho Tổ chức Phi lợi nhuận:**  
    * Các gói giảm giá: ChatGPT Team với giá $20/người dùng/tháng. Giảm 50% cho ChatGPT Enterprise (khoảng $30/người dùng/tháng).  
  * Sử dụng API 9:  
    OpenAI tính phí cho mỗi yêu cầu sử dụng API, tách biệt với các gói đăng ký ChatGPT và có thể tốn kém cho doanh nghiệp.  
* **1.5. Ưu điểm so với các AI tool khác cùng loại (Advantages):**  
  * **Bộ tính năng tiên phong và toàn diện:** Là một trong những AI hội thoại tiên tiến sớm nhất và được chấp nhận rộng rãi nhất 2, ChatGPT cung cấp một loạt các tính năng đặc biệt rộng, bao gồm khả năng đa phương thức mạnh mẽ (văn bản, giọng nói, hình ảnh 1), duyệt web tích hợp 1, phân tích dữ liệu 1, và một Canvas độc đáo để cộng tác.1  
  * **Truy cập vào các mô hình tiên tiến nhất:** Người đăng ký có quyền truy cập vào các mô hình mới nhất và mạnh mẽ nhất của OpenAI, bao gồm dòng GPT-4 và dòng "o" chuyên biệt (o1, o3, o4-mini) được thiết kế để suy luận sâu hơn và giải quyết vấn đề phức tạp.1  
  * **Khả năng tùy chỉnh và mở rộng mạnh mẽ:** Khả năng tạo GPT tùy chỉnh và truy cập Cửa hàng GPT 14 cho phép tùy chỉnh đáng kể theo nhu cầu cụ thể và thúc đẩy một hệ sinh thái phong phú. Quyền truy cập API 19 càng tăng cường khả năng mở rộng của nó.  
  * **Cơ sở người dùng vững chắc và nhận diện thương hiệu:** Việc gia nhập sớm và phổ biến rộng rãi đã mang lại cho nó sự công nhận thương hiệu đáng kể và một lượng lớn người dùng, thúc đẩy phản hồi và cải tiến liên tục.1  
  * **Truy cập theo tầng cho các nhu cầu khác nhau:** Các gói đăng ký đa dạng phục vụ nhiều đối tượng người dùng, từ sử dụng cá nhân miễn phí đến triển khai cấp doanh nghiệp, cung cấp các mức độ khả năng và hỗ trợ khác nhau.8  
* 1.6. Ứng dụng trong các tác vụ thực tế nào (Real-world applications/use cases) 1:  
  * **Tạo nội dung:** Soạn thảo các dạng văn bản khác nhau như email, bài đăng blog, bài báo, mô tả sản phẩm, cập nhật mạng xã hội, thơ và lời bài hát.  
  * **Học tập và Giáo dục:** Giải thích các chủ đề phức tạp, hỗ trợ luyện thi, giúp làm bài tập về nhà, tạo tài liệu giáo dục như bài thuyết trình, phiếu bài tập và câu đố.  
  * **Động não và Lên ý tưởng:** Tạo ra các ý tưởng mới cho các dự án kinh doanh, dự án sáng tạo (ví dụ: làm gì với tác phẩm nghệ thuật của trẻ em) và giải quyết vấn đề.  
  * **Lập trình và Phát triển phần mềm:** Viết, gỡ lỗi và giải thích mã; học API mới; tự động hóa các tác vụ lập trình lặp đi lặp lại.  
  * **Năng suất kinh doanh và chuyên nghiệp:** Tóm tắt các cuộc họp, trích xuất thông tin chi tiết từ dữ liệu, tự động hóa quy trình làm việc, hỗ trợ nhập dữ liệu, tạo báo cáo và tài liệu chuyên nghiệp.  
  * **Hỗ trợ nghiên cứu:** Giúp lựa chọn chủ đề, thu thập thông tin cơ bản, xác định các nguồn tài liệu liên quan, tổ chức kết quả nghiên cứu và hỗ trợ trích dẫn.  
  * **Tiếp thị và Bán hàng:** Tạo bản sao tiếp thị, phát triển kịch bản cho quảng cáo, phân tích dữ liệu khách hàng (truy vấn tìm kiếm, hành vi trên mạng xã hội), thiết kế khảo sát khách hàng và tạo ý tưởng nội dung tập trung vào SEO.  
  * **Trợ lý cá nhân:** Cung cấp lời khuyên phù hợp (ví dụ: lịch trình du lịch, cách ứng phó trong các tình huống cụ thể), cung cấp nguồn cảm hứng sáng tạo (ví dụ: ý tưởng quà tặng, thiệp chúc mừng được cá nhân hóa) và giúp thực hiện các tác vụ như hạ yên xe đạp.1  
* **1.7. Tài liệu hướng dẫn tìm hiểu sâu hơn (Documentation for further learning):**  
  * **Tổng quan chính thức:** [https://openai.com/chatgpt/overview/](https://openai.com/chatgpt/overview/) 1  
  * **Tổng quan về các khả năng:** [https://help.openai.com/en/articles/9260256-chatgpt-capabilities-overview](https://help.openai.com/en/articles/9260256-chatgpt-capabilities-overview) 14  
  * **Tài liệu tham khảo API:** [https://platform.openai.com/docs/api-reference](https://platform.openai.com/docs/api-reference) 19  
  * **Trang định giá:** 1  
    [https://openai.com/pricing](https://openai.com/pricing)  
  * **Điều khoản sử dụng & Chính sách bảo mật:** [https://openai.com/policies/terms-of-use](https://openai.com/policies/terms-of-use), [https://openai.com/policies/privacy-policy](https://openai.com/policies/privacy-policy) 16  
  * **Hướng dẫn về Tài liệu Kỹ thuật (Bên thứ ba):** [https://paceai.co/chatgpt-for-technical-documentation/](https://paceai.co/chatgpt-for-technical-documentation/) 21

---

**2\. Perplexity AI**

* **2.1. Giới thiệu (Introduction):**  
  * Perplexity AI là một công cụ tìm kiếm và nghiên cứu dựa trên AI, được thiết kế để cung cấp các câu trả lời trực tiếp, đáng tin cậy và cập nhật kèm theo trích dẫn, giúp loại bỏ nhiễu thông tin.6  
  * Nó tận dụng các mô hình AI như GPT-4.1 của OpenAI, Gemini 2.5 Pro của Google, Claude 3.7 Sonnet của Anthropic và Grok-2 của xAi để phân tích và tóm tắt thông tin từ internet.6  
  * Mục tiêu của công cụ là hợp lý hóa quy trình nghiên cứu, giúp người dùng tìm các bài báo học thuật, tạo ra thông tin chi tiết và điều hướng các chủ đề phức tạp.7  
* **2.2. Lịch sử phát triển (Development History):**  
  * Perplexity AI, Inc. được thành lập vào năm 2022 bởi Aravind Srinivas, Denis Yarats, Johnny Ho và Andy Konwinski.22  
  * Aravind Srinivas, CEO của công ty, trước đây đã giữ các vị trí nghiên cứu tại OpenAI, Google Brain và DeepMind.23  
  * Công cụ tìm kiếm hàng đầu của họ đã được ra mắt vào ngày 7 tháng 12 năm 2022\.22  
  * Sau đó, một tiện ích mở rộng cho Google Chrome và các ứng dụng cho iOS và Android đã được phát hành.22  
  * Công ty đã nhận được tài trợ từ các nhà đầu tư nổi tiếng bao gồm Jeff Bezos, Elad Gil, Nat Friedman và Nvidia.24  
* **2.3. Các tính năng hiện tại đang có (Current Features):**  
  * **Công cụ Tìm kiếm dựa trên AI:** Cung cấp các câu trả lời ngắn gọn, bằng ngôn ngữ tự nhiên cho các truy vấn, được hỗ trợ bởi các nguồn trích dẫn từ web.6  
  * **Perplexity Copilot:** Cung cấp tìm kiếm AI có hướng dẫn để khám phá sâu hơn các chủ đề.6  
  * **Thư viện (Library):** Lưu trữ các khám phá của người dùng, vượt xa một lịch sử tìm kiếm đơn giản.6  
  * **Đồng bộ hóa trên nhiều thiết bị:** Đồng bộ hóa lịch sử và khám phá của người dùng trên các thiết bị.6  
  * **Lựa chọn Mô hình (Pro/Enterprise):** Cho phép người dùng chọn mô hình AI ưa thích cho các tìm kiếm.10  
  * **Tải lên & Phân tích Tệp:** Người dùng có thể tải lên tệp (PDF, v.v., \<25MB cho Pro, dung lượng lớn hơn cho Enterprise) và nhận câu trả lời dựa trên nội dung của chúng.10  
  * **Chế độ Nghiên cứu Sâu (Deep Research Mode):**  
    * Được giới thiệu vào tháng 2 năm 2025\.7  
    * Dựa trên một tác nhân dựa trên chuỗi suy nghĩ, tương tự như các sản phẩm của Google, OpenAI và DeepSeek.7  
    * Thực hiện các báo cáo nghiên cứu chuyên sâu về bất kỳ chủ đề nào, mất khoảng năm phút và bao gồm nhiều tìm kiếm trung gian trước khi một LLM tích hợp kết quả vào một báo cáo khoảng 3 trang.7  
    * Báo cáo bao gồm các trang web và tài liệu được phát hiện, được chia thành các phần/tiểu mục có thể kích hoạt các tìm kiếm Nghiên cứu Sâu mới.7  
    * Cho phép đặt câu hỏi tiếp theo và tương tác giống như chatbot.7  
  * **Tính năng Tập trung (Focus Feature) (bao gồm Tập trung Học thuật \- Academic Focus):**  
    * Cho phép người dùng thu hẹp phạm vi tìm kiếm, ví dụ: vào các nguồn "Học thuật" (chủ yếu là Semantic Scholar, PubMed), "Viết", "Wolfram Alpha", "YouTube", "Reddit".3  
    * Tham số search\_mode: "academic" ưu tiên các bài báo được bình duyệt, các bài báo trên tạp chí và các ấn phẩm nghiên cứu.25  
  * **Cộng tác (Spaces):** Người dùng có thể cộng tác trong các "Không gian" (Spaces) riêng tư với đồng đội hoặc bạn bè.10  
  * **Kho lưu trữ Tệp Tổ chức & Kiểm soát (Enterprise):** Cung cấp các biện pháp kiểm soát chặt chẽ xung quanh việc chia sẻ tệp và kho lưu trữ tệp của tổ chức.10  
  * **Tích hợp với Ứng dụng Tệp (Enterprise):** Đính kèm tệp từ Google Drive, Dropbox; liên tục đồng bộ hóa từ Google Drive, Sharepoint, OneDrive, Box, Dropbox.10  
  * **Tìm kiếm qua Đăng ký Dữ liệu (Enterprise):** Tìm kiếm thông qua dữ liệu tài chính (Factset, Crunchbase) và dữ liệu giáo dục (Wiley).10  
* 2.4. License / Subscription 7:  
  * **Gói Miễn phí (Free Plan):**  
    * Chi phí: $0.  
    * Tính năng: Tìm kiếm ngắn gọn không giới hạn. Truy vấn Pro (suy luận đa bước, nguồn bổ sung) giới hạn 3 lượt/ngày. Truy vấn nghiên cứu sâu giới hạn 3 lượt/ngày. Cộng tác trong Không gian riêng tư (5 người cộng tác/Không gian). Nhận câu trả lời từ tệp (5 tệp/Không gian, mỗi tệp \<25MB).  
    * Bảo mật Dữ liệu: Đào tạo dữ liệu hạn chế với tùy chọn từ chối.  
  * **Gói Pro (Pro Plan):**  
    * Chi phí: $20/tháng hoặc $200/năm (ngầm định, nếu Enterprise Pro là $400/năm với chiết khấu 20% từ $40/tháng). 7 cũng đề cập miễn phí cho sinh viên trong một tháng.  
    * Tính năng: Tìm kiếm ngắn gọn không giới hạn. Truy vấn Pro không giới hạn. 500 truy vấn nghiên cứu sâu/ngày. Cộng tác trong Không gian riêng tư (5 người cộng tác/Không gian). Chọn mô hình AI ưa thích. Nhận câu trả lời từ tệp (50 tệp/Không gian, mỗi tệp \<25MB).  
    * Bảo mật Dữ liệu: Đào tạo dữ liệu hạn chế với tùy chọn từ chối. Chứng nhận bảo mật SOC 2 Type II.  
  * **Gói Doanh nghiệp Pro (Enterprise Pro Plan):**  
    * Chi phí: $40/tháng mỗi chỗ hoặc $400/năm mỗi chỗ (giảm giá 20%). Giá linh hoạt cho \>100 chỗ.  
    * Tính năng: Tìm kiếm ngắn gọn không giới hạn. Truy vấn Pro không giới hạn. 500 truy vấn nghiên cứu sâu/ngày. Cộng tác viên không giới hạn trong Không gian. Chọn mô hình AI ưa thích. Nhận câu trả lời từ tệp (500 tệp tải lên, 8.000 tệp qua Trình kết nối ứng dụng/Không gian, mỗi tệp \<25MB). Kho lưu trữ tệp tổ chức, kiểm soát chia sẻ tệp. Đính kèm tệp từ Google Drive/Dropbox; liên tục đồng bộ hóa từ Google Drive, Sharepoint, OneDrive, Box, Dropbox. Tìm kiếm qua đăng ký dữ liệu (Factset, Crunchbase, Wiley). Đăng nhập một lần (SSO). Quản lý người dùng. Hỗ trợ doanh nghiệp chuyên dụng.  
    * Bảo mật Dữ liệu: Perplexity và các đối tác LLM bên thứ ba không đào tạo trên dữ liệu của bạn. Chứng nhận bảo mật SOC 2 Type II.  
* **2.5. Ưu điểm so với các AI tool khác cùng loại (Advantages):**  
  * **Tập trung vào Độ chính xác và Trích dẫn:** Triết lý thiết kế cốt lõi là cung cấp câu trả lời với các trích dẫn nguồn rõ ràng, nâng cao độ tin cậy và cho phép người dùng xác minh thông tin.6 Điều này trái ngược với một số chatbot có thể tạo ra thông tin có vẻ hợp lý nhưng chưa được xác minh.  
  * **Tính năng Nghiên cứu Sâu Chuyên biệt:** Chế độ Nghiên cứu Sâu cung cấp một phương pháp tiếp cận có cấu trúc, đa bước để tạo ra các báo cáo toàn diện về các chủ đề phức tạp, thường sử dụng các nguồn học thuật khi được tập trung.7 Khả năng cung cấp một báo cáo chi tiết với nhiều nguồn được phát hiện là một điểm khác biệt chính cho các tác vụ nghiên cứu.3  
  * **Tùy chọn Tập trung Học thuật:** Khả năng nhắm mục tiêu cụ thể vào các nguồn học thuật (Semantic Scholar, PubMed) rất có lợi cho sinh viên, nhà nghiên cứu và các chuyên gia cần thông tin được bình duyệt.7  
  * **Ưu tiên Nguồn Minh bạch:** Người dùng thường có thể thấy các nguồn đang được tham khảo, mang lại sự minh bạch hơn so với các phản hồi LLM dạng hộp đen.  
  * **Tính năng Cấp Doanh nghiệp:** Gói Enterprise Pro cung cấp các tính năng mạnh mẽ cho các tổ chức, bao gồm bảo mật dữ liệu nâng cao (không đào tạo trên dữ liệu), SSO, quản lý người dùng và tích hợp với các nguồn dữ liệu và hệ thống tệp của doanh nghiệp.10  
  * **Hiệu suất So sánh trong Nghiên cứu:** Trong ít nhất một thử nghiệm so sánh liên quan đến nghiên cứu sâu về kỹ thuật gợi ý, báo cáo của Perplexity (với trọng tâm học thuật) được đánh giá cao hơn về chi tiết và nguồn so với DeepSearch của Grok và đầu ra của Gemini.3  
* 2.6. Ứng dụng trong các tác vụ thực tế nào (Real-world applications/use cases) 7:  
  * **Công cụ Trả lời:** Nhận câu trả lời nhanh chóng, có nguồn gốc cho các câu hỏi thực tế (ví dụ: "Khi nào iPhone tiếp theo ra mắt?", "Lợi ích sức khỏe của yoga?").26  
  * **Nghiên cứu Học thuật:** Tìm kiếm các bài báo khoa học, tổng quan tài liệu, hiểu các chủ đề học thuật phức tạp, viết bài báo/luận văn.7  
  * **Theo dõi Sự kiện Thời gian thực:** Nhận cập nhật về các sự kiện hiện tại như tình hình bão hoặc kết quả bầu cử.26  
  * **Nghiên cứu Thị trường:** Điều tra xu hướng thị trường bất động sản, so sánh giá sản phẩm, nghiên cứu thị trường chứng khoán và tài chính.26  
  * **Tối ưu hóa SEO:** Tạo từ khóa, tạo bản đồ từ khóa, tối ưu hóa mô tả meta.26  
  * **Học tập và Tự học:** Giải thích chi tiết các chủ đề, học lập trình, học ngôn ngữ mới.26  
  * **Tạo & Tóm tắt Nội dung:** Tóm tắt bài báo/trang web, tạo ý tưởng cho bài đăng blog, email, kịch bản.7  
  * **Nghiên cứu Chuyên nghiệp:** Nghiên cứu pháp lý (tìm các vụ án liên quan), tình báo kinh doanh.26  
  * **Tạo mã:** Tạo đoạn mã (ví dụ: Python để phân tích dữ liệu).26  
* **2.7. Tài liệu hướng dẫn tìm hiểu sâu hơn (Documentation for further learning):**  
  * **Trang web chính thức:** [https://www.perplexity.ai/](https://www.perplexity.ai/) 6  
  * **Điều khoản dịch vụ:** [https://www.perplexity.ai/tos](https://www.perplexity.ai/tos) 6  
  * **Chính sách bảo mật:** [https://www.perplexity.ai/privacy](https://www.perplexity.ai/privacy) 6  
  * **Giá doanh nghiệp:** [https://www.perplexity.ai/enterprise/pricing](https://www.perplexity.ai/enterprise/pricing) 10  
  * **Tài liệu API (qua Make.com):** [https://apps.make.com/perplexity-ai](https://apps.make.com/perplexity-ai) 27 (chi tiết cách lấy khóa API và sử dụng với Make.com)  
  * **Hướng dẫn Bộ lọc Học thuật:** [https://docs.perplexity.ai/guides/academic-filter-guide](https://docs.perplexity.ai/guides/academic-filter-guide) 25

---

**3\. Grok (xAI)**

* **3.1. Giới thiệu (Introduction):**  
  * Grok là một chatbot AI và mô hình ngôn ngữ lớn được phát triển bởi xAI, do Elon Musk thành lập.12  
  * Nó được thiết kế để trả lời các câu hỏi một cách hài hước, với một "chút nổi loạn" và ít biện pháp bảo vệ hơn so với các đối thủ cạnh tranh, bao gồm cả các truy vấn "nhạy cảm".13  
  * Một tính năng chính là khả năng tích hợp kiến thức thời gian thực, đặc biệt là dữ liệu từ X (trước đây là Twitter).12  
* **3.2. Lịch sử phát triển (Development History):**  
  * **Thành lập xAI:** Được thành lập bởi Elon Musk vào tháng 7 năm 2023 với mục tiêu "hiểu bản chất thực sự của vũ trụ".28 Các thành viên sáng lập chủ chốt bao gồm Igor Babuschkin, Tony Wu, Greg Yang, Jimmy Ba và Christian Szegedy (mặc dù Szegedy đã rời đi vào tháng 2 năm 2025).28  
  * **Ra mắt Grok-1:** Ra mắt lần đầu vào tháng 11 năm 2023, được xây dựng chỉ trong bốn tháng, dành riêng cho người dùng X trả phí. Ban đầu nó không ngang bằng với GPT-4 hoặc Claude 2 nhưng cho thấy tiềm năng.12  
  * **Grok-1 nguồn mở:** Grok-1 được mở nguồn vào tháng 3 năm 2024 theo giấy phép Apache-2.0.12  
  * **Giới thiệu Grok-2:** Tháng 8 năm 2024, Grok-2 mang lại những "cải tiến đáng kể" về khả năng suy luận, độ chính xác, tính linh hoạt và khả năng tạo hình ảnh. Grok-2 Mini (nhỏ hơn, nhanh hơn, tiết kiệm chi phí hơn) cũng được phát hành. Quyền truy cập API cho Grok-2 và Mini đã có sẵn.12  
  * **Grok-2 Vision & Tìm kiếm Web:** Tháng 10 năm 2024, Grok-2 được cải tiến để hiểu hình ảnh.12 Tháng 12 năm 2024, Grok-2 nâng cấp được triển khai với tốc độ phản hồi được cải thiện, độ chính xác cao hơn, khả năng hiểu đa ngôn ngữ, tìm kiếm web trực tiếp với trích dẫn và tạo hình ảnh thông qua mô hình Aurora. Quyền truy cập Grok được mở cho tất cả người dùng X miễn phí (có quảng cáo với giới hạn tốc độ), với giới hạn cao hơn cho người đăng ký. Nút "Grok" trên các bài đăng X được giới thiệu.28  
  * **Phát hành Grok-3:** Công bố vào ngày 19 tháng 2 năm 2025 3, hoặc tháng 2 năm 2025\.11 Phiên bản này tuyên bố vượt trội hơn GPT-4o và Claude 3.5 Sonnet ở một số tiêu chuẩn.3 Nó được đào tạo trên siêu máy tính Colossus của xAI với sức mạnh tính toán gấp 10 lần so với phiên bản tiền nhiệm.12 Điều này dẫn đến sự gia tăng 436% lượt truy cập và thu hút 25,82 triệu người dùng hàng tháng.12  
* **3.3. Các tính năng hiện tại đang có (Current Features):**  
  * **Truy cập thông tin thời gian thực:** Tích hợp sâu với X (Twitter) để cập nhật tin tức, xu hướng và các cuộc thảo luận mới nhất.3 Cũng thực hiện tìm kiếm web trực tiếp với trích dẫn.28  
  * **Tính cách độc đáo & Chế độ tương tác:**  
    * **Chế độ thông thường (Regular Mode):** Cho các phản hồi thẳng thắn, dựa trên thực tế.13  
    * **Chế độ vui vẻ (Fun Mode):** Thêm sự hài hước, dí dỏm, châm biếm và một chút "nổi loạn" vào các cuộc trò chuyện.13  
  * **Khả năng suy luận nâng cao ("Chế độ Suy nghĩ" \- Think Modes):**  
    * **Chế độ Suy nghĩ (Think Mode):** Kích hoạt một mô hình suy luận nâng cao để giải quyết vấn đề trong lập trình, toán học và khoa học.3 Grok-3 nổi bật với khả năng chia nhỏ các tác vụ phức tạp và kiểm tra lại công việc.12  
    * **Chế độ "Bộ não Lớn" (Big Brain Mode):** 12 Xử lý các truy vấn phức tạp với khả năng suy luận sâu hơn.  
  * **DeepSearch & DeeperSearch:**  
    * **DeepSearch:** Tạo báo cáo chi tiết dựa trên hàng tá nguồn web. Truy cập dữ liệu trực tiếp từ web và X để có cái nhìn tổng quan về các chủ đề thịnh hành, nội dung mới xuất bản và tin tức nóng hổi.3 Nó liên quan đến một tác nhân theo yêu cầu thực hiện các truy vấn phụ được nhắm mục tiêu, tìm nạp các trang có liên quan, theo dõi các liên kết, đánh giá độ tin cậy của nguồn và tổng hợp thông tin.30  
    * **DeeperSearch:** Cho kết quả nghiên cứu sâu hơn và mở rộng hơn nữa.3  
  * **Khả năng đa phương thức:**  
    * **Tạo hình ảnh:** Tạo hình ảnh từ lời nhắc văn bản (sử dụng mô hình Aurora với Grok-2, tiếp tục trong Grok-3).3  
    * **Chỉnh sửa hình ảnh:** Cho phép chỉnh sửa hình ảnh dựa trên lời nhắc văn bản.3  
    * **Hiểu hình ảnh:** Grok-2 (tháng 10 năm 2024\) và các phiên bản tiếp theo có thể hiểu/phân tích hình ảnh.12  
  * **Chế độ giọng nói:** Cho phép trò chuyện bằng giọng nói (yêu cầu đăng ký Premium+ hoặc SuperGrok).3  
  * **Tóm tắt tài liệu:** Có thể tải lên và tóm tắt các tài liệu như bài báo nghiên cứu hoặc báo cáo.13  
  * **Kiểm tra thông tin:** Sử dụng dữ liệu X thời gian thực để xác minh các tuyên bố và cung cấp câu trả lời chi tiết.13  
  * **Xử lý tác vụ đa dạng:** Soạn thảo email, tạo ý tưởng, gỡ lỗi mã, lập kế hoạch du lịch.13  
  * **Tích hợp với nền tảng X:** Tích hợp liền mạch vào X (trang web và ứng dụng di động), với nút "Grok" trên các bài đăng để phân tích/giải thích.12  
* 3.4. License / Subscription 11:  
  * **Truy cập miễn phí (cho tất cả người dùng X):** Kể từ tháng 2 năm 2025 (phát hành Grok-3), Grok được sử dụng miễn phí cho tất cả người dùng X với các giới hạn (ví dụ: 10 tin nhắn mỗi hai giờ 13, hoặc 10 truy vấn mỗi 2 giờ 28). Điều này được công bố là miễn phí "cho đến khi máy chủ của chúng tôi tan chảy".11  
  * **Đăng ký X Premium:**  
    * Chi phí: $7/tháng (thanh toán hàng năm) hoặc $8/tháng (thanh toán hàng tháng).11  
    * Lợi ích: Cung cấp chức năng Grok nâng cao hơn người dùng miễn phí, bao gồm giới hạn sử dụng tăng và quyền truy cập sớm vào các tính năng nâng cao như Chế độ giọng nói.  
  * **Đăng ký X Premium+:**  
    * Chi phí: $32.92/tháng (thanh toán hàng năm) hoặc $40/tháng (thanh toán hàng tháng).11 Trước đây là $16/tháng, giá tăng cùng với Grok-3.11  
    * Lợi ích: Toàn quyền truy cập Grok-3 trong X, giới hạn sử dụng cao nhất, quyền truy cập nhanh nhất vào các tính năng mới (như Chế độ giọng nói), trải nghiệm X hoàn toàn không có quảng cáo.11  
  * **Giao diện web độc lập (Grok.com):** Cung cấp một nền tảng chuyên dụng, nhưng quyền truy cập bị hạn chế ở một số khu vực (EU, Vương quốc Anh) với kế hoạch mở rộng.12  
  * **Ứng dụng di động (iOS, Android):** Có sẵn ở một số quốc gia được chọn.12  
  * **API doanh nghiệp:**  
    * xAI đã công bố kế hoạch cho một API doanh nghiệp cho Grok-3.11  
    * Giá: $2 cho mỗi 1 triệu token đầu vào và $10 cho mỗi 1 triệu token đầu ra.32  
    * Có thể truy cập qua Chatbase để đào tạo, tinh chỉnh với dữ liệu tùy chỉnh và cơ sở hạ tầng RAG.12  
    * API tương thích với các SDK của OpenAI và Anthropic.31  
* **3.5. Ưu điểm so với các AI tool khác cùng loại (Advantages):**  
  * **Tích hợp X (Twitter) thời gian thực:** Quyền truy cập trực tiếp độc đáo vào dữ liệu X cung cấp một cái nhìn thời gian thực chưa từng có về các cuộc thảo luận, xu hướng và tin tức nóng hổi toàn cầu, điều mà hầu hết các AI khác đều thiếu.12  
  * **Tính cách và Tương tác Riêng biệt:** "Chế độ Vui vẻ" của Grok mang đến phong cách trò chuyện dí dỏm, hài hước và ít bị kiểm duyệt hơn, giúp các tương tác có khả năng hấp dẫn hơn hoặc phù hợp với những người dùng tìm kiếm phản hồi ít bị "làm sạch" hơn.13 Sẵn sàng tham gia vào các chủ đề xã hội, chính trị và văn hóa hơn.12  
  * **Thông tin Cập nhật:** Kết hợp dữ liệu X với tìm kiếm web trực tiếp, nhằm cung cấp thông tin mới hơn so với các mô hình chỉ dựa vào dữ liệu đào tạo tĩnh.12  
  * **Suy luận Nâng cao và DeepSearch:** Các tính năng như "Chế độ Suy nghĩ" và "DeepSearch" được thiết kế để giải quyết vấn đề phức tạp và tạo báo cáo nghiên cứu chuyên sâu, tổng hợp thông tin từ nhiều nguồn.3  
  * **Chu kỳ Phát triển Nhanh chóng:** xAI đã cho thấy sự lặp lại rất nhanh từ Grok-1 đến Grok-3, nhanh chóng bổ sung các tính năng như tạo/hiểu hình ảnh và cải thiện các khả năng cốt lõi.12  
* 3.6. Ứng dụng trong các tác vụ thực tế nào (Real-world applications/use cases) 3:  
  * **Tóm tắt Tin tức & Theo dõi Xu hướng Thời gian thực:** Tóm tắt tin tức nóng hổi, các chủ đề thịnh hành và sự kiện toàn cầu bằng cách sử dụng dữ liệu X trực tiếp.13  
  * **Tạo Nội dung:** Tạo bài đăng blog, mô tả sản phẩm, chú thích mạng xã hội, báo cáo (đặc biệt là dạng ngắn).29  
  * **Nghiên cứu & Phân tích:**  
    * Nghiên cứu sâu về các chủ đề mới nổi, khái niệm học thuật, xu hướng thị trường, phân tích cạnh tranh, nghiên cứu tuân thủ, nghiên cứu kỹ thuật.3  
    * Phân tích phản hồi của khách hàng.35  
  * **Giải quyết Vấn đề:** Giải quyết vấn đề khoa học (ví dụ: phương trình vật lý), hỗ trợ viết mã, gỡ lỗi.12  
  * **Hỗ trợ Cá nhân:** Lập kế hoạch bữa ăn dựa trên khuyến mãi, hướng dẫn thiền tùy chỉnh, lập kế hoạch du lịch.13  
  * **Kinh doanh & Chiến lược:** Mô phỏng các kịch bản kinh doanh, lập kế hoạch chiến lược, bán các mặt hàng đã qua sử dụng (soạn thảo danh sách, ước tính giá).34  
  * **Kiểm tra Tính xác thực:** Xác minh các tuyên bố bằng cách sử dụng dữ liệu X thời gian thực.13  
  * **Kể chuyện Sáng tạo & Chuẩn bị Tranh luận:** Đồng sáng tác tiểu thuyết, tinh chỉnh lập luận, dự đoán các phản biện.34  
* **3.7. Tài liệu hướng dẫn tìm hiểu sâu hơn (Documentation for further learning):**  
  * **Tài liệu API xAI (Chung):** [https://docs.x.ai/](https://docs.x.ai/) 31  
  * **Hướng dẫn/Tutorial API Grok:** [https://docs.x.ai/docs/tutorial](https://docs.x.ai/docs/tutorial) (The Hitchhiker's Guide to Grok) 31  
  * **Giới thiệu API Grok:** [https://docs.x.ai/docs/introduction](https://docs.x.ai/docs/introduction) 36  
  * **Thông tin Grok DeepSearch (Bên thứ ba):** [https://www.tryprofound.com/blog/understanding-grok-a-comprehensive-guide-to-grok-websearch-grok-deepsearch](https://www.tryprofound.com/blog/understanding-grok-a-comprehensive-guide-to-grok-websearch-grok-deepsearch) 30,  
    [https://www.godofprompt.ai/blog/10-best-grok-3-prompts-for-deep-research](https://www.godofprompt.ai/blog/10-best-grok-3-prompts-for-deep-research) 35  
  * **Truy cập qua Nền tảng X:** Trực tiếp trong ứng dụng X hoặc trang web cho người đăng ký Premium/Premium+.12  
  * **Giao diện Web Độc lập:** Grok.com.12

---

**4\. Gemini (Google)**

* **4.1. Giới thiệu (Introduction):**  
  * Gemini là mô hình AI đa phương thức và giao diện của Google, có khả năng hiểu và tạo ra văn bản, mã, hình ảnh, âm thanh và video.5  
  * Nó hướng tới mục tiêu trở thành một công cụ AI linh hoạt cho các tác vụ khác nhau bao gồm viết lách, lập trình, động não, học tập và giải quyết vấn đề phức tạp.4  
  * Gemini bao gồm một họ các mô hình (ví dụ: Gemini 2.5 Pro, 2.5 Flash) và được tích hợp vào nhiều sản phẩm và dịch vụ của Google.4  
* **4.2. Lịch sử phát triển (Development History):**  
  * **Nguồn gốc từ Nghiên cứu LLM của Google:** Sự phát triển của Gemini được xây dựng dựa trên lịch sử nghiên cứu AI lâu dài của Google, bắt đầu với bài báo Word2Vec vào năm 2013, tiếp theo là mô hình hội thoại thần kinh vào năm 2015, kiến trúc Transformer vào năm 2017 và khả năng trò chuyện đa lượt vào năm 2020\.37  
  * **Ra mắt với tên Bard:** Ban đầu được ra mắt với tên "Bard", một thử nghiệm, vào tháng 3 năm 2023, tuân theo Nguyên tắc AI của Google.37  
  * **Tiến hóa thành Gemini:** Thương hiệu đã phát triển thành Gemini, phản ánh khả năng đa phương thức tiên tiến hơn của nó.  
  * Các phiên bản mô hình 5:  
    * **Gemini 1.0 Pro:** Một phiên bản ban đầu có sẵn qua API.40  
    * **Gemini 1.5 Pro & 1.5 Flash:** Được giới thiệu với cửa sổ ngữ cảnh đột phá 2 triệu token cho Pro và 1 triệu cho Flash, tăng cường khả năng hiểu ngữ cảnh dài.39  
    * **Gemini 2.0 Flash & Flash-Lite:** Mô hình đa phương thức cân bằng với cửa sổ ngữ cảnh 1 triệu token, được xây dựng cho kỷ nguyên của Tác nhân (Agents); Flash-Lite là phiên bản nhỏ hơn, tiết kiệm chi phí hơn.4  
    * **Gemini 2.5 Pro & 2.5 Flash (Preview):** Các mô hình tiên tiến nhất, vượt trội trong việc viết mã, suy luận phức tạp, với cửa sổ ngữ cảnh từ 1 triệu (Flash) đến 2 triệu (Pro) token và "ngân sách suy nghĩ" (thinking budgets).4  
    * **Các phiên bản chuyên biệt:** Mô hình Âm thanh Gốc (Native Audio), mô hình Chuyển văn bản thành giọng nói (TTS).5  
  * **Mô hình Gemma:** Các mô hình mở, nhẹ, tiên tiến (Gemma 3, Gemma 3n) được xây dựng từ cùng một nghiên cứu và công nghệ như Gemini.5  
* **4.3. Các tính năng hiện tại đang có (Current Features):**  
  * **Hiểu và Tạo Đa phương thức:** Xử lý đầu vào và đầu ra văn bản, mã, hình ảnh, âm thanh và video.5  
  * **Suy luận & Lập trình Nâng cao:**  
    * **Deep Think (Gemini 2.5 Pro):** Chế độ suy luận nâng cao cho các vấn đề phức tạp.5  
    * Vượt trội trong các tác vụ lập trình, bao gồm đề xuất sửa đổi, gỡ lỗi các cơ sở mã phức tạp, tối ưu hóa hiệu suất và giải thích mã.4 Hỗ trợ tải lên kho mã lên đến 30k dòng.4  
  * **Cửa sổ Ngữ cảnh Dài:** Lên đến 2 triệu token (Gemini 1.5 Pro, 2.5 Pro Preview), cho phép xử lý các tài liệu lớn, cơ sở mã hoặc bản ghi video.4 Có thể xử lý tải lên tệp lên đến 1.500 trang.4  
  * **Nghiên cứu Sâu (Deep Research):** (Có sẵn trong gói Pro) Phân tích hàng trăm nguồn trong thời gian thực để tạo báo cáo toàn diện có trích dẫn.4 Người dùng có thể sửa đổi kế hoạch nghiên cứu.42  
  * **Tạo Hình ảnh (Imagen 4, API Imagen 3):** Tạo hình ảnh gốc từ mô tả.4  
  * **Tạo Video (Veo 3 Fast, API Veo 2):** Tạo video chất lượng cao từ lời nhắc văn bản.4  
  * **Flow (Công cụ Làm phim AI):** Được xây dựng tùy chỉnh với Veo 3 Fast để tạo các cảnh và câu chuyện điện ảnh.4  
  * **Whisk:** Giới hạn cao hơn cho việc tạo ảnh thành video với Veo 2\.4  
  * **Tích hợp NotebookLM:** Trợ lý nghiên cứu và viết lách với các tính năng nâng cao cho người dùng Pro (gấp 5 lần Tổng quan Âm thanh, sổ ghi chép).4  
  * **Tích hợp với Google Workspace:** Truy cập Gemini trực tiếp trong Gmail, Docs, Sheets, Slides, Meet, Vids, v.v., để soạn thảo, trả lời, tóm tắt, tạo hình ảnh/thiết kế, ghi chú cuộc họp.4  
  * **Gemini trong Chrome:** "AI luôn sẵn sàng" được khởi chạy từ cửa sổ Chrome, sử dụng ngữ cảnh trang để hỗ trợ mà không cần tab mới. Bao gồm Gemini Live để có phản hồi bằng giọng nói theo thời gian thực.4  
  * **Cá nhân hóa & Gems:** Người dùng có thể tạo "Gems" – các phiên bản tùy chỉnh của Gemini được điều chỉnh cho các tác vụ hoặc phong cách cụ thể.4  
  * **Canvas:** Không gian làm việc tương tác để đồng sáng tạo với Gemini.4  
  * **Nền tảng hóa với Google Search:** Nâng cao phản hồi bằng thông tin thời gian thực từ Google Search.39  
  * **Âm thanh Gốc & TTS:** Đầu ra âm thanh gốc biểu cảm và khả năng chuyển văn bản thành giọng nói.5  
* 4.4. License / Subscription 4:  
  * **Ứng dụng Gemini (Gói Miễn phí):**  
    * Chi phí: $0 USD với Tài khoản Google.  
    * Tính năng: Truy cập ứng dụng Gemini, mô hình 2.5 Flash, giới hạn 2.5 Pro. Tạo hình ảnh với Imagen 4, Nghiên cứu Sâu, Gemini Live, Canvas, Gems. Whisk để tạo hình ảnh/video (Veo 2). NotebookLM. 15 GB dung lượng lưu trữ Google One.  
    * Sử dụng Dữ liệu: Dữ liệu có thể được sử dụng để cải thiện các sản phẩm của Google.39  
  * **Gói Google AI Pro (qua Ứng dụng Gemini/Google One AI Premium):**  
    * Chi phí: $19.99 USD/tháng.4 Một số nguồn đề cập $18.99/tháng cho Google One AI Premium.40  
    * Tính năng: Mọi thứ trong gói Miễn phí, cộng thêm: Truy cập nhiều hơn vào Gemini 2.5 Pro, Nghiên cứu Sâu trên 2.5 Pro. Tạo video với Veo 3 Fast. Công cụ làm phim AI Flow. Giới hạn cao hơn cho Whisk (Veo 2). NotebookLM nâng cao. Gemini trong Gmail, Docs, Vids, v.v. 2 TB dung lượng lưu trữ Google One. Tải lên tệp lớn hơn (1.500 trang), hỗ trợ viết mã nâng cao (30k dòng mã).  
    * Đủ điều kiện: Người dùng trên 18 tuổi, tài khoản Google cá nhân.4  
    * Sử dụng Dữ liệu: Dữ liệu không được sử dụng để cải thiện các sản phẩm của Google cho việc sử dụng API gói trả phí.39  
  * **Gemini cho Google Workspace (Doanh nghiệp/Tổ chức):**  
    * **Gemini Business:** $20/người dùng/tháng. Truy cập Gemini trong các ứng dụng Workspace, Gemini Advanced (2.5 Pro), quyền riêng tư/bảo mật cấp doanh nghiệp.40 Gói linh hoạt: $24/người dùng/tháng.40  
    * **Gemini Enterprise:** Giá tùy chỉnh. Bao gồm tất cả các tính năng của Business, có thể có các khả năng nâng cao hơn. Gói linh hoạt: $36/người dùng/tháng.40  
    * Tiện ích bổ sung Workspace 40:  
      * AI Meetings and Messaging: $10/người dùng/tháng (cố định), $12/người dùng/tháng (linh hoạt).  
      * AI Security: $10/người dùng/tháng (cố định), $12/người dùng/tháng (linh hoạt).  
    * Gói Giáo dục 40:  
      Gemini Education ($18/người dùng/tháng cố định, $24 linh hoạt), Gemini Education Premium ($27/người dùng/tháng cố định, $36 linh hoạt).  
  * Gemini Code Assist (cho nhà phát triển, qua Google Cloud 45):  
    * **Standard:** $19/người dùng/tháng (cam kết 12 tháng) hoặc $22.80/người dùng/tháng (hàng tháng). Bao gồm hoàn thành mã, tạo mã, trò chuyện trong IDE, nhận biết cơ sở mã, v.v.  
    * **Enterprise:** $45/người dùng/tháng (cam kết 12 tháng) hoặc $54/người dùng/tháng (hàng tháng). Tất cả các tính năng của Standard \+ tùy chỉnh mã, Gemini trong BigQuery, Apigee, Tích hợp ứng dụng.  
  * API Gemini (Vertex AI & Google AI Studio 39):  
    * Được định giá theo 1.000 ký tự hoặc 1 triệu token, thay đổi theo mô hình (1.0 Pro, 1.5 Pro, 1.5 Flash, 2.0 Flash, 2.5 Pro, 2.5 Flash) và loại đầu vào/đầu ra (văn bản, hình ảnh, âm thanh, video).  
    * Ví dụ (Gemini 2.5 Pro Preview, Gói trả phí39): Đầu vào $1.25-$2.50/1 triệu token; Đầu ra $10-$15/1 triệu token.  
    * Ví dụ (Gemini 1.5 Pro, Gói trả phí40): Đầu vào $0.0003125/1k ký tự; Đầu ra $0.00125/1k ký tự. Đầu vào hình ảnh $0.00032875/hình ảnh.  
    * Các gói miễn phí cho việc sử dụng API tồn tại với giới hạn tốc độ thấp hơn (ví dụ: Gemini 1.5 Flash: 15 RPM, 1 triệu TPM; Gemini 1.5 Pro: 2 RPM, 32k TPM).40  
    * Việc sử dụng Google AI Studio là miễn phí.39  
* **4.5. Ưu điểm so với các AI tool khác cùng loại (Advantages):**  
  * **Tích hợp sâu với Hệ sinh thái Google:** Hoạt động liền mạch trong Google Workspace (Gmail, Docs, v.v.), Chrome và Google Cloud (Vertex AI, BigQuery), mang lại trải nghiệm gắn kết cho người dùng đã ở trong hệ sinh thái này.4  
  * **Đa phương thức và Ngữ cảnh dài Tiên tiến:** Các mô hình Gemini (đặc biệt là 1.5 Pro và 2.5 Pro) tự hào về khả năng đa phương thức ấn tượng (văn bản, hình ảnh, âm thanh, video) và cửa sổ ngữ cảnh rất lớn (lên đến 2 triệu token), cho phép phân tích tinh vi dữ liệu mở rộng.4  
  * **Khả năng Nghiên cứu và Lập trình Mạnh mẽ:** Các tính năng như Nghiên cứu Sâu cung cấp các báo cáo có cấu trúc, có nguồn gốc 4, và khả năng hỗ trợ lập trình của nó được thiết kế cho các tác vụ phức tạp, bao gồm cả việc suy luận trên các cơ sở mã lớn.4  
  * **Truy cập vào Google Search và Knowledge Graph:** Việc nền tảng hóa các phản hồi bằng Google Search giúp tăng cường độ chính xác và mức độ liên quan với thông tin cập nhật.37  
  * **Định giá và Truy cập Linh hoạt:** Cung cấp nhiều tầng khác nhau từ quyền truy cập miễn phí cho người tiêu dùng và sử dụng API đến các giải pháp doanh nghiệp toàn diện, phục vụ các nhu cầu và ngân sách đa dạng.4  
  * **Tùy chọn Mô hình Mở (Gemma):** Cung cấp các mô hình mở, nhẹ, tiên tiến (Gemma) cho các nhà phát triển thích các giải pháp mở.5  
* 4.6. Ứng dụng trong các tác vụ thực tế nào (Real-world applications/use cases) 4:  
  * **Năng suất & Nơi làm việc:** Soạn thảo email, tóm tắt tài liệu, tạo bài thuyết trình (văn bản, hình ảnh), ghi chú cuộc họp, tổ chức công việc.4 Pennymac sử dụng nó cho mô tả công việc và tài liệu chính sách.46  
  * **Lập trình & Phát triển:** Tạo mã, gỡ lỗi, đề xuất sửa đổi, tối ưu hóa hiệu suất, hiểu cơ sở mã, tạo bài kiểm tra đơn vị.4  
  * **Nghiên cứu & Phân tích:** Báo cáo nghiên cứu sâu về các chủ đề khác nhau, phân tích các bộ dữ liệu lớn (phản hồi của khách hàng, kế hoạch kinh doanh), tìm kiếm thông tin chi tiết quan trọng, tạo biểu đồ.4 Pinnacol Assurance sử dụng nó để tìm hiểu sâu hơn về các yêu cầu bảo hiểm.46  
  * **Học tập & Giáo dục:** Chuẩn bị cho kỳ thi (tạo hướng dẫn học tập, bài kiểm tra thực hành từ ghi chú), hiểu các chủ đề phức tạp, trợ giúp bài tập về nhà.4  
  * **Tạo Nội dung Sáng tạo:** Viết bài đăng blog, chú thích mạng xã hội, trang web, kịch bản video, tạo hình ảnh và video.4 Oxa sử dụng nó cho các mẫu chiến dịch và bài đăng xã hội.46  
  * **Hoạt động Kinh doanh:** Bán hàng (tài liệu quảng cáo), tiếp thị (tóm tắt chiến dịch, kế hoạch dự án), dịch vụ khách hàng (email trả lời được cá nhân hóa), nhân sự (mô tả công việc, tài liệu đào tạo), quản lý dự án.38 Adore Me sử dụng nó cho mô tả sản phẩm.46  
  * Ví dụ Ngành cụ thể 46:  
    * **Bán lẻ/Khách sạn:** Fitz's Bottling Company (định dạng hàng tồn kho), Hog Island Oyster Co. (phân tích bán hàng), Spicewalla (thiết kế quảng cáo), Penny's Wine Shop (bản tin), No Limbits (dự đoán hàng tồn kho).  
    * **Tổ chức Phi lợi nhuận:** Erika's Lighthouse (chương trình sức khỏe tâm thần), Mississippi Farm to School Network (đề xuất tài trợ), Can Do Canines (soạn thảo email), The Chef Ann Foundation (tóm tắt các cuộc trò chuyện).  
    * **Nông nghiệp/Sản xuất:** Morgan Ranch (viết email), Saltverk (tổ chức dữ liệu môi trường), The American Cornhole League (nội dung mạng xã hội từ cảnh quay).  
  * **Ứng dụng Đa phương thức:** Xác định các mục trong hình ảnh qua giọng nói/văn bản, hiểu thông tin đa sắc thái từ dữ liệu văn bản và hình ảnh kết hợp.41  
* **4.7. Tài liệu hướng dẫn tìm hiểu sâu hơn (Documentation for further learning):**  
  * **Tổng quan về Gemini:** [https://gemini.google/overview/](https://gemini.google/overview/) 37  
  * **Các gói đăng ký Gemini:** [https://gemini.google/subscriptions/](https://gemini.google/subscriptions/) 4  
  * **Các mô hình Google DeepMind (Gemini):** [https://deepmind.google/models/gemini/](https://deepmind.google/models/gemini/) 5  
  * **Giá API Gemini (Google AI):** [https://ai.google.dev/pricing](https://ai.google.dev/pricing) 39  
  * **Tài liệu Gemini cho Google Cloud:** [https://cloud.google.com/gemini/docs](https://cloud.google.com/gemini/docs?authuser=1) 47  
  * **Giá Gemini Code Assist:** [https://cloud.google.com/products/gemini/pricing](https://cloud.google.com/products/gemini/pricing?authuser=1) 45  
  * **Firebase AI Logic (sử dụng các mô hình Gemini):** [https://firebase.google.com/docs/ai-logic](https://firebase.google.com/docs/ai-logic?authuser=1) 48  
  * **Tính năng Nghiên cứu Sâu (Blog Workspace):** [https://workspace.google.com/blog/ai-and-machine-learning/meet-deep-research-your-new-ai-research-assistant](https://workspace.google.com/blog/ai-and-machine-learning/meet-deep-research-your-new-ai-research-assistant?authuser=1) 42  
  * **Sử dụng Nghiên cứu Sâu (Trợ giúp của Google):** [https://support.google.com/gemini/answer/15719111?hl=vi](https://www.google.com/search?q=https://support.google.com/gemini/answer/15719111?hl%3Dvi&authuser=1) 43

---

**Phần II: Công cụ Hỗ trợ Code (Code Assistance Tools)**

Lĩnh vực công cụ AI hỗ trợ lập trình đang chứng kiến sự phát triển vượt bậc, chuyển mình từ những tiện ích tự động hoàn thành mã đơn giản sang các hệ thống "tác tử" (agentic) phức tạp hơn. Những công cụ này ngày càng có khả năng hiểu sâu hơn về toàn bộ cơ sở mã, thực hiện các chỉnh sửa đa tệp, gỡ lỗi, viết kiểm thử và thậm chí quản lý các khía cạnh của dự án. GitHub Copilot Agent 49, Cursor với khả năng nhận biết toàn bộ codebase 51, Agent của Augment 52, Chế độ Cascade Write của Windsurf 53, ChatGPT Codex 54, Google Jules 56, Claude Code 58, và Zen Agents của Zencoder 60 là những ví dụ điển hình cho xu hướng này.

Ban đầu, các công cụ AI hỗ trợ lập trình tập trung vào việc tăng tốc độ gõ mã thông qua các gợi ý hoàn thành dòng lệnh hoặc khối lệnh. Tuy nhiên, sự phức tạp của phát triển phần mềm hiện đại đòi hỏi nhiều hơn thế. Xu hướng hướng tới các khả năng "tác tử" phản ánh nhu cầu về AI có thể đảm nhận các tác vụ toàn diện hơn. Các công cụ như Cursor 51 và Augment 52 nhấn mạnh vào việc hiểu toàn bộ ngữ cảnh dự án, chứ không chỉ tệp hiện tại. GitHub Copilot đang mở rộng sang "Chế độ Agent" 61 và "Không gian làm việc Copilot" (Copilot Workspace) 50 để quản lý tác vụ rộng hơn. Các công cụ mới như Google Jules 56 và ChatGPT Codex 54 được quảng bá rõ ràng là các tác tử kỹ thuật phần mềm AI có khả năng thực hiện các tác vụ như triển khai tính năng và sửa lỗi. Điều này cho thấy một sự chuyển dịch sang các đối tác AI có thể hỗ trợ trong suốt vòng đời phát triển, không chỉ trong việc viết mã. Kết quả là, các nhà phát triển có thể kỳ vọng các công cụ AI sẽ được tích hợp sâu hơn vào quy trình làm việc của họ, đảm nhận các vai trò phức tạp và tự chủ hơn. Điều này có thể tăng năng suất đáng kể nhưng cũng đòi hỏi các nhà phát triển phải học cách gợi ý và quản lý hiệu quả các tác tử AI này. Định nghĩa về "lập trình viên cặp" (pair programmer) đang được mở rộng.

Môi trường phát triển tích hợp (IDE) đang trở thành trung tâm cho các công cụ hỗ trợ lập trình AI. Hầu hết các công cụ mã hóa tiên tiến được thiết kế dưới dạng plugin cho các IDE phổ biến (VS Code, JetBrains) hoặc bản thân chúng là các IDE ưu tiên AI (Cursor 51, Windsurf 62, Trae 63, Void 65). Các nhà phát triển dành phần lớn thời gian của họ trong IDE; do đó, để hỗ trợ AI hiệu quả, nó cần được tích hợp liền mạch vào môi trường này. Thành công của GitHub Copilot một phần là do khả năng tích hợp IDE của nó.61 Các công cụ như Cursor 51 và Windsurf 62 đang xây dựng các khả năng AI trực tiếp vào trải nghiệm trình soạn thảo, nhằm mục đích tương tác mượt mà hơn so với việc chuyển đổi giữa chatbot và IDE. Các plugin cho IDE hiện có (Augment cho JetBrains 52, Zencoder cho VS Code/JetBrains 66) tận dụng các môi trường quen thuộc. Tương lai của hỗ trợ mã hóa AI có khả năng sẽ gắn liền chặt chẽ với IDE. Điều này cho phép hiểu ngữ cảnh tốt hơn (truy cập vào tệp dự án, công cụ xây dựng, v.v.) và trải nghiệm nhà phát triển được sắp xếp hợp lý hơn. Chúng ta có thể thấy sự gia tăng của các IDE gốc AI hoặc các plugin AI ngày càng tinh vi giúp chuyển đổi các IDE hiện có.

Khi các công cụ AI xử lý toàn bộ cơ sở mã, mối quan tâm về sở hữu trí tuệ và quyền riêng tư dữ liệu tự nhiên nảy sinh, đặc biệt đối với người dùng doanh nghiệp. Một số công cụ mới hơn như Void Editor nhấn mạnh quyền riêng tư ("mã của bạn không bao giờ được lưu trữ từ xa" 65, tương tự với Cursor 51) và hỗ trợ các LLM cục bộ (Void với Ollama 65, LMStudio API 67, Ollama API 68). Khả năng sử dụng các mô hình cục bộ (thông qua Ollama, LMStudio) hoặc có các chế độ riêng tư rõ ràng (như của Cursor 51) giải quyết những lo ngại này, cho phép các nhà phát triển tận dụng hỗ trợ AI mà không cần gửi mã độc quyền đến máy chủ của bên thứ ba. Sự phân biệt của GitHub Copilot giữa các gói cá nhân và doanh nghiệp/tổ chức liên quan đến việc sử dụng dữ liệu để đào tạo 61 cũng phản ánh sự nhạy cảm này. Do đó, các tính năng bảo mật và tùy chọn sử dụng các mô hình tự lưu trữ hoặc cục bộ sẽ trở thành tiêu chí lựa chọn ngày càng quan trọng đối với các công cụ mã hóa AI, đặc biệt là trong môi trường doanh nghiệp. Điều này có thể thúc đẩy sự phát triển hơn nữa trong các LLM cục bộ hiệu quả và xử lý AI an toàn.

Giao thức Ngữ cảnh Mô hình (Model Context Protocol \- MCP) đang nổi lên như một yếu tố quan trọng để tăng cường tích hợp công cụ. Một số công cụ mã hóa (Trae 64, Windsurf 62, Claude Code 58, Augment 69) đã đề cập đến việc hỗ trợ hoặc tích hợp với MCP, cho phép kết nối với các công cụ và dịch vụ tùy chỉnh. MCP 70 cung cấp một cách tiêu chuẩn hóa để các tác tử AI tương tác với các công cụ và nguồn dữ liệu bên ngoài. Đối với các trợ lý mã hóa, điều này có nghĩa là chúng có thể kết nối với các dịch vụ như Figma (TalkToFigma MCP 72), nhà cung cấp tài liệu (Context7 DevDocs MCP 73), trình quản lý tác vụ (TaskMaster MCP 74), hoặc thậm chí các công cụ tự động hóa trình duyệt (Playwright MCP 75). Khả năng mở rộng này cho phép các công cụ mã hóa AI không chỉ dừng lại ở việc tạo mã mà còn tương tác với hệ sinh thái phát triển rộng lớn hơn một cách có cấu trúc. Hỗ trợ MCP có khả năng trở thành một tính năng tiêu chuẩn cho các tác tử mã hóa AI tiên tiến, cho phép chúng tận dụng một loạt thông tin ngữ cảnh rộng hơn và thực hiện các tác vụ phức tạp, tích hợp hơn, làm cho chúng trở thành những trợ lý mạnh mẽ và linh hoạt hơn.

**Bảng 2: Ma trận Tính năng của các Công cụ Hỗ trợ Code AI**

| Tên Công cụ | Nhà phát triển/Xuất xứ | Môi trường Chính | Tính năng AI Chính | Hỗ trợ Mô hình Cục bộ | Hỗ trợ MCP | Mô hình Định giá |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| GitHub Copilot | GitHub / OpenAI | Plugin IDE (VS Code, JetBrains, etc.), GitHub.com | Hoàn thành mã nhận biết ngữ cảnh, Chat, Chế độ Agent, Đánh giá mã, Không gian làm việc Copilot | Không (Dữ liệu doanh nghiệp không dùng để huấn luyện mô hình công khai) | Có (GitHub MCP Server, Copilot Extensions) | Freemium (cá nhân giới hạn), Pro, Pro+, Business, Enterprise |
| Cursor | Cursor | IDE AI-first (fork của VS Code) | Hoàn thành mã dự đoán ("Next Edit"), Nhận biết toàn bộ codebase, Chỉnh sửa bằng ngôn ngữ tự nhiên, "Max Mode" với nhiều mô hình | Có (Chế độ Riêng tư) | Có (Hỗ trợ MCP chung, ví dụ TalkToFigma) | Hobby (Miễn phí), Pro, Business |
| Augment | Augment Computing | Plugin IDE (JetBrains) | Agent, Hoàn thành mã thông minh, Chat với tích hợp sâu, hiểu toàn bộ codebase (Context Engine) | Không rõ từ snippet | Có | Community (Miễn phí), Developer, Pro, Max, Enterprise |
| Windsurf | Windsurf (trước là Codeium) | IDE Agentic | Flows (Agents \+ Copilots), Cascade (hiểu codebase sâu, đề xuất và chạy lệnh, chỉnh sửa đa tệp), Supercomplete, Linter Integration | Không rõ từ snippet | Có | Miễn phí khi tải (100 credits), các gói trả phí có thể tồn tại |
| ChatGPT Codex | OpenAI | Tích hợp trong ChatGPT | Tác tử kỹ thuật phần mềm, viết tính năng, sửa lỗi, truy vấn codebase, tạo PR, chạy trong sandbox trên đám mây | Không (Dịch vụ đám mây) | Không rõ trực tiếp, nhưng ChatGPT có thể tích hợp tool | Truy cập qua các gói ChatGPT Pro, Team, Enterprise, Plus |
| Google Jules | Google Labs | Nền tảng web, tích hợp GitHub | Tác tử mã hóa tự chủ, không đồng bộ, viết kiểm thử, xây dựng tính năng, sửa lỗi, cập nhật dependency, changelog âm thanh, chạy trên VM Google Cloud | Không (Dịch vụ đám mây) | Không rõ từ snippet | Beta miễn phí (giới hạn 5 yêu cầu/ngày), dự kiến có giá sau beta |
| Copilot Agent | GitHub | Một phần của GitHub Copilot | Thực hiện tác vụ (truy vấn tài liệu, lấy dữ liệu, hành động cụ thể), tích hợp tính năng tùy chỉnh vào Copilot Chat | Như GitHub Copilot | Có (Là một phần của hệ sinh thái MCP của GitHub) | Bao gồm trong các gói GitHub Copilot |
| Claude Code | Anthropic | CLI (Terminal) | Tác tử mã hóa, chỉnh sửa tệp, sửa lỗi, trả lời câu hỏi về codebase, chạy và sửa kiểm thử, tích hợp Git, tìm kiếm web | Không (Kết nối API trực tiếp) | Có | Research Preview, yêu cầu API key Anthropic |
| Trae | ByteDance | IDE AI-first (nền tảng VS Code) | Hoàn thành mã, Builder Mode (lập kế hoạch trước khi thay đổi), hệ thống agent tùy chỉnh, chat đa phương thức, gợi ý terminal | Có thể sử dụng Claude 3.5/3.7 miễn phí (trong giai đoạn early access) | Có | Miễn phí (trong giai đoạn early access), có kế hoạch Pro |
| Zend (Zencoder) | Zencoder | Plugin IDE (VS Code, JetBrains), CI | Zen Agents (sửa mã, tạo tài liệu/unit test), Repo Grokking™ (phân tích toàn bộ codebase), Chat thông minh, Hoàn thành mã, Sửa mã | Không rõ từ snippet | Có (Hỗ trợ custom MCP) | Free Plan, Business ($19/user/tháng), Enterprise ($39/user/tháng) |
| Void | VoidEditor (Open Source) | IDE AI-first (fork của VS Code), ưu tiên quyền riêng tư | Autocomplete, Chỉnh sửa nội tuyến, Chat \+ Agent Mode \+ Gather Mode, Hỗ trợ nhiều LLM (cục bộ & đám mây), Checkpoints | Có (Ollama, vLLM) | Không rõ từ snippet | Open Source, miễn phí (hỗ trợ Gemini, OpenRouter miễn phí), tùy chọn trả phí cho model API khác |

---

\*\*1. GitHub Copilot (GitHub / OpenAI)\*\*

\*   \*\*1.1. Giới thiệu (Introduction):\*\*  
    \*   GitHub Copilot là một lập trình viên cặp AI được thiết kế để nâng cao năng suất và sự hài lòng của nhà phát triển bằng cách cung cấp hỗ trợ theo ngữ cảnh trong suốt vòng đời phát triển phần mềm.\[61\]  
    \*   Nó cung cấp các đề xuất hoàn thành mã, hỗ trợ trò chuyện, giải thích mã và trả lời các câu hỏi về tài liệu trực tiếp trong GitHub và các IDE phổ biến.\[61\]  
\*   \*\*1.2. Lịch sử phát triển (Development History):\*\*  
    \*   Được phát triển bởi GitHub hợp tác với OpenAI.  
    \*   Đã phát triển từ việc chủ yếu hoàn thành mã sang bao gồm các chức năng trò chuyện (Copilot Chat) và các khả năng giống tác tử hơn (Chế độ Agent, Không gian làm việc Copilot).\[50, 61\]  
    \*   Liên tục được cập nhật với sự hỗ trợ mô hình mới và các tính năng, như được chỉ ra bởi tính chất nhật ký thay đổi của một số tài liệu.\[76\]  
\*   \*\*1.3. Các tính năng hiện tại đang có (Current Features) \[49, 50, 61\]:\*\*  
    \*   \*\*Tạo mã & Hỗ trợ được hỗ trợ bởi AI:\*\*  
        \*   \*\*Hoàn thành mã:\*\* Đề xuất các dòng đơn lẻ hoặc toàn bộ hàm khi bạn nhập.  
        \*   \*\*Copilot Chat:\*\* Giao diện hội thoại trong IDE (VS Code, Visual Studio, JetBrains, Neovim), GitHub.com và GitHub Mobile để đặt câu hỏi về mã hóa, giải thích mã, tạo kiểm thử đơn vị, đề xuất bản sửa lỗi, v.v. Hỗ trợ đính kèm tham chiếu bằng ký hiệu "@".\[76\]  
        \*   \*\*Hiểu biết theo ngữ cảnh:\*\* Tận dụng ngữ cảnh của tệp hiện tại của bạn và các tệp dự án liên quan.  
        \*   \*\*Lập chỉ mục ngữ nghĩa:\*\* (Đối với Copilot Enterprise) Lập chỉ mục cơ sở mã của một tổ chức để hiểu sâu hơn và đưa ra các đề xuất phù hợp.  
    \*   \*\*Chế độ Agent (GitHub Copilot Agent):\*\*  
        \*   Ủy thác các vấn đề mở cho Copilot, công cụ này có thể lập kế hoạch, viết, kiểm thử và lặp lại mã, cung cấp các yêu cầu kéo (pull requests).\[50, 61\]  
        \*   Có thể lấy dữ liệu từ kho lưu trữ và tài nguyên bên ngoài (thông qua MCP).  
        \*   Phù hợp nhất cho các tác vụ phức tạp liên quan đến nhiều bước, lặp lại và xử lý lỗi.\[50\]  
    \*   \*\*Linh hoạt về mô hình:\*\* Cho phép chuyển đổi giữa các mô hình AI như Claude 3.7 Sonnet, OpenAI o1, Google Gemini 2.0 Flash.\[61\] (Lưu ý: Đây dường như là một tính năng mới hơn, có thể dành cho các gói cụ thể hoặc đang trong giai đoạn xem trước).  
    \*   \*\*Đề xuất Chỉnh sửa Tiếp theo:\*\* Tiết lộ các hiệu ứng lan tỏa của các thay đổi trên một dự án.\[61\]  
    \*   \*\*Đánh giá mã:\*\* Phân tích mã, phát hiện các lỗi ẩn và sửa lỗi.\[50, 61\] Các đề xuất đánh giá mã do AI tạo ra.  
    \*   \*\*Copilot cho Yêu cầu Kéo:\*\* Các bản tóm tắt do AI tạo ra về các thay đổi đã được thực hiện trong một yêu cầu kéo, xác định các tác động và trọng tâm của người đánh giá.\[50\]  
    \*   \*\*Không gian làm việc Copilot (Bản xem trước công khai):\*\* Môi trường được kích hoạt bởi Copilot để tinh chỉnh các yêu cầu kéo, xác thực các thay đổi và tích hợp các đề xuất từ người đánh giá.\[50\]  
    \*   \*\*Hoàn thành văn bản Copilot (Bản xem trước công khai):\*\* Hoàn thành văn bản do AI tạo ra để giúp viết mô tả yêu cầu kéo một cách nhanh chóng và chính xác.\[50\]  
    \*   \*\*Hỗ trợ Terminal:\*\* Tích hợp GitHub CLI và trò chuyện trong Windows Terminal Canary.\[50, 61\]  
    \*   \*\*Không gian Copilot:\*\* Tổ chức và chia sẻ ngữ cảnh (mã, tài liệu, thông số kỹ thuật) để làm cơ sở cho các phản hồi của Copilot cho các tác vụ cụ thể.\[49, 50\]  
    \*   \*\*Cơ sở kiến thức Copilot (Chỉ dành cho Enterprise):\*\* Tạo và quản lý các bộ sưu tập tài liệu để sử dụng làm ngữ cảnh cho việc trò chuyện với Copilot.\[50\]  
    \*   \*\*Tùy chỉnh:\*\* Hướng dẫn tùy chỉnh cá nhân và kho lưu trữ. Tùy chỉnh môi trường agent và tường lửa.\[49\]  
    \*   \*\*Tiện ích mở rộng GitHub Copilot:\*\* Các ứng dụng GitHub tích hợp các công cụ bên ngoài vào Copilot Chat thông qua MCP.\[49, 76\]  
\*   \*\*1.4. License / Subscription \[61\]:\*\*  
    \*   \*\*Cho cá nhân:\*\*  
        \*   \*\*Miễn phí:\*\* Chức năng hạn chế: 50 yêu cầu chế độ agent/trò chuyện mỗi tháng, 2.000 lượt hoàn thành/tháng. Truy cập các mô hình như Claude 3.5 Sonnet, GPT-4.1. (Có sẵn nếu người dùng có quyền truy cập Pro thông qua các phương tiện khác nhưng chọn Miễn phí).  
        \*   \*\*Pro:\*\* $10 USD/tháng hoặc $100/năm. Chế độ agent/trò chuyện không giới hạn với GPT-4.1, hoàn thành mã không giới hạn. Truy cập đánh giá mã, Claude 3.7/4 Sonnet, Gemini 2.5 Pro. Số yêu cầu cao cấp gấp 6 lần so với Miễn phí. Miễn phí cho sinh viên, giáo viên đã được xác minh và người bảo trì các dự án nguồn mở phổ biến.  
        \*   \*\*Pro+:\*\* $39 USD/tháng hoặc $390/năm. Mọi thứ trong Pro, cộng thêm quyền truy cập vào tất cả các mô hình (Claude Opus 4, o3, GPT-4.5), số yêu cầu cao cấp gấp 30 lần so với Miễn phí, agent mã hóa (xem trước).  
    \*   \*\*Cho tổ chức:\*\*  
        \*   \*\*GitHub Copilot Business:\*\*.\[61\] Bao gồm Copilot trong IDE, CLI, Mobile. Bao gồm quản lý giấy phép, quản lý chính sách, bồi thường IP. Dữ liệu KHÔNG được sử dụng để đào tạo các mô hình công khai.  
        \*   \*\*GitHub Copilot Enterprise:\*\* (Ngầm định $39/người dùng/tháng). Tất cả các tính năng của Business \+ tùy chỉnh sâu hơn, trò chuyện trên GitHub.com, lập chỉ mục cơ sở mã để có các đề xuất phù hợp, quyền truy cập vào các mô hình riêng tư tùy chỉnh được tinh chỉnh. Dữ liệu KHÔNG được sử dụng để đào tạo các mô hình công khai.  
    \*   \*\*Quyền riêng tư:\*\* Xử lý dữ liệu tương tác của người dùng, lời nhắc, đề xuất, phản hồi. Đối với Business/Enterprise, dữ liệu KHÔNG được sử dụng để đào tạo mô hình. Chính sách lưu giữ dữ liệu khác nhau tùy theo phương thức truy cập (trò chuyện/hoàn thành IDE không được lưu giữ so với quyền truy cập khác lên đến 28 ngày đối với lời nhắc/đề xuất). Có sẵn GitHub DPA. Bộ lọc tham chiếu mã để phát hiện các kết quả trùng khớp với mã công khai.  
\*   \*\*1.5. Ưu điểm so với các AI tool khác cùng loại (Advantages):\*\*  
    \*   \*\*Tích hợp sâu với GitHub:\*\* Được xây dựng nguyên bản vào nền tảng GitHub, cung cấp quy trình làm việc liền mạch cho các vấn đề, PR, đánh giá mã và ngữ cảnh từ các kho lưu trữ.\[50, 61\]  
    \*   \*\*Hỗ trợ IDE rộng rãi:\*\* Tích hợp với các IDE lớn như VS Code, Visual Studio, JetBrains và Neovim.\[61\]  
    \*   \*\*Khả năng Agentic nâng cao:\*\* Copilot Agent và Copilot Workspace thể hiện sự chuyển dịch sang xử lý tác vụ tự chủ hơn và hỗ trợ cấp dự án.\[50, 61\]  
    \*   \*\*Tập trung vào doanh nghiệp:\*\* Các sản phẩm mạnh mẽ cho doanh nghiệp và tổ chức lớn với các tính năng như lập chỉ mục cơ sở mã, quản lý chính sách, bồi thường IP và kiểm soát quyền riêng tư nâng cao.\[61\]  
    \*   \*\*Hệ sinh thái đang phát triển:\*\* Hỗ trợ Tiện ích mở rộng Copilot và MCP cho phép tích hợp với một loạt các công cụ và nguồn dữ liệu rộng hơn.\[49, 76\]  
    \*   \*\*Lựa chọn mô hình (Mới nổi):\*\* Khả năng chuyển đổi giữa các LLM cơ bản khác nhau (Claude, Gemini, các mô hình OpenAI) mang lại sự linh hoạt.\[61\]  
\*   \*\*1.6. Ứng dụng trong các tác vụ thực tế nào (Real-world applications/use cases) \[49, 50\]:\*\*  
    \*   \*\*Tạo và Hoàn thành Mã:\*\* Viết mã soạn sẵn, triển khai các hàm, tự động hoàn thành các dòng lệnh.  
    \*   \*\*Hiểu và Khám phá Cơ sở Mã:\*\* Giải thích các khối mã, khám phá việc triển khai tính năng, lập tài liệu cho mã cũ.  
    \*   \*\*Tái cấu trúc Mã:\*\* Cải thiện khả năng đọc, sửa lỗi lint, tối ưu hóa hiệu suất, áp dụng các mẫu thiết kế, dịch mã giữa các ngôn ngữ.  
    \*   \*\*Gỡ lỗi:\*\* Xác định lỗi, đề xuất các bản sửa lỗi, xử lý giới hạn tốc độ API, gỡ lỗi JSON không hợp lệ.  
    \*   \*\*Kiểm thử:\*\* Tạo kiểm thử đơn vị, tạo đối tượng giả, tạo kiểm thử đầu cuối.  
    \*   \*\*Lập tài liệu:\*\* Lập tài liệu cho mã cũ, giải thích logic phức tạp, đồng bộ hóa tài liệu với các thay đổi mã, viết các cuộc thảo luận hoặc bài đăng trên blog.  
    \*   \*\*Phân tích Bảo mật:\*\* Tìm kiếm các lỗ hổng trong mã.  
    \*   \*\*Di chuyển & Hiện đại hóa Dự án:\*\* Hỗ trợ di chuyển dự án hoặc hiện đại hóa mã cũ (ví dụ: nâng cấp dự án Java).  
    \*   \*\*Quản lý Vấn đề:\*\* Tạo vấn đề, giao vấn đề cho Copilot Agent giải quyết.  
    \*   \*\*Quản lý Yêu cầu Kéo:\*\* Tóm tắt PR, đánh giá mã, tinh chỉnh PR trong Copilot Workspace.  
\*   \*\*1.7. Tài liệu hướng dẫn tìm hiểu sâu hơn (Documentation for further learning):\*\*  
    \*   \*\*Trang tính năng GitHub Copilot:\*\* \[https://github.com/features/copilot\](https://github.com/features/copilot) \[61\]  
    \*   \*\*Tài liệu GitHub Copilot (Chung):\*\* \[https://docs.github.com/en/copilot\](https://docs.github.com/en/copilot) \[49, 50\]  
    \*   \*\*Về Copilot Agents:\*\* \[https://docs.github.com/en/copilot/building-copilot-extensions/building-a-copilot-agent-for-your-copilot-extension/about-copilot-agents\](https://docs.github.com/en/copilot/building-copilot-extensions/building-a-copilot-agent-for-your-copilot-extension/about-copilot-agents) \[49\]  
    \*   \*\*Tính năng GitHub Copilot (Chi tiết):\*\* \[https://docs.github.com/en/copilot/about-github-copilot/github-copilot-features\](https://docs.github.com/en/copilot/about-github-copilot/github-copilot-features) \[50\]  
    \*   \*\*Nhật ký thay đổi Máy chủ GitHub MCP từ xa:\*\* \[https://github.blog/changelog/2025-06-12-remote-github-mcp-server-is-now-available-in-public-preview/\](https://github.blog/changelog/2025-06-12-remote-github-mcp-server-is-now-available-in-public-preview/) \[76\]

---

**2\. Cursor**

* **2.1. Giới thiệu (Introduction):**  
  * Cursor là một trình soạn thảo mã nguồn được hỗ trợ bởi AI, được thiết kế để nâng cao năng suất của nhà phát triển, tự giới thiệu là "cách tốt nhất để lập trình với AI".51  
  * Nó nhằm mục đích tích hợp sâu AI vào quy trình làm việc lập trình, cung cấp các tính năng vượt trội hơn so với việc tự động hoàn thành mã đơn giản.51  
* **2.2. Lịch sử phát triển (Development History):**  
  * (Không có ngày thành lập cụ thể hoặc lịch sử chi tiết trong các đoạn trích được cung cấp. Trọng tâm sẽ là các tính năng và vị thế thị trường của nó.)  
  * Nó đã trở nên phổ biến nhờ khả năng tích hợp AI tiên tiến, với người dùng thường so sánh nó một cách tích cực với VS Code tích hợp Copilot.51  
  * Cursor được ghi nhận về sự phát triển nhanh chóng, với các tính năng ngày càng phong phú thường xuyên.51  
* 2.3. Các tính năng hiện tại đang có (Current Features) 51:  
  * **Chỉnh sửa mã nguồn được hỗ trợ bởi AI:**  
    * **Hoàn thành bằng Tab/Chỉnh sửa dự đoán ("Next Edit"):** Dự đoán chỉnh sửa tiếp theo của người dùng, cho phép thay đổi nhanh chóng, đôi khi bằng cách nhấn liên tục phím "tab" để áp dụng các thay đổi tương tự. Được mô tả là "phép thuật".51  
    * **Chỉnh sửa bằng ngôn ngữ tự nhiên:** Viết mã hoặc cập nhật toàn bộ các lớp/hàm bằng các lời nhắc/hướng dẫn đơn giản.51  
    * **Nhận biết toàn bộ codebase:** AI hiểu ngữ cảnh của toàn bộ dự án, không chỉ tệp hiện tại, cho phép nó lấy câu trả lời từ codebase hoặc tham chiếu đến các tệp/tài liệu. Mã được đề xuất có thể được tích hợp chỉ bằng một cú nhấp chuột.51  
    * **Tự động hoàn thành thông minh:** Được mô tả là "điên rồ", nó dự đoán mã mong muốn dựa trên các hành động hiện tại mà không cần lời nhắc rõ ràng, đề xuất chỉnh sửa cho mã hiện có và nhận thấy sự không nhất quán.51  
  * **Truy cập mô hình:**  
    * Được cung cấp bởi sự kết hợp của các mô hình được xây dựng có mục đích và các "mô hình tiên phong" (ví dụ: Claude 3.5 Sonnet, các mô hình OpenAI).51 Claude của Anthropic là mô hình mặc định cho tất cả người dùng Cursor do hiệu suất của nó trên các codebase phức tạp.59  
    * "Chế độ Max" cho phép định giá dựa trên token bằng cách sử dụng các mô hình khác nhau, tính phí theo giá API của nhà cung cấp mô hình \+ biên độ lợi nhuận 20%.78  
  * **Trải nghiệm người dùng quen thuộc:** Nhập tất cả các tiện ích mở rộng, chủ đề và tổ hợp phím từ các trình soạn thảo khác (như VS Code) chỉ bằng một cú nhấp chuột để chuyển đổi suôn sẻ.51  
  * **Tùy chọn quyền riêng tư:**  
    * **Chế độ riêng tư:** Đảm bảo mã của người dùng không bao giờ được lưu trữ từ xa.51  
    * **Chứng nhận SOC 2:** Cho thấy cam kết về bảo mật và quyền riêng tư.51 Thực thi chế độ riêng tư trên toàn tổ chức cho gói Business.77  
  * **Tích hợp MCP (Model Context Protocol):** Cho phép Cursor tương tác với các công cụ và dịch vụ bên ngoài, ví dụ như tích hợp TalkToFigma MCP để đọc và sửa đổi thiết kế Figma theo chương trình.72  
* 2.4. License / Subscription 77:  
  * **Hobby Plan (Gói Sở thích):**  
    * Chi phí: Miễn phí.  
    * Bao gồm: Dùng thử Pro hai tuần. 200 lượt hoàn thành mỗi tháng. 50 yêu cầu mỗi tháng.  
  * **Pro Plan (Gói Chuyên nghiệp):**  
    * Chi phí: $20/tháng.  
    * Bao gồm: Mọi thứ trong gói Hobby, cộng thêm: Hoàn thành không giới hạn. 500 yêu cầu "nhanh" mỗi tháng. Yêu cầu "chậm" không giới hạn (sau khi hết yêu cầu nhanh). Truy cập không giới hạn vào các mô hình cao cấp.  
    * Một "yêu cầu" tiêu chuẩn có giá $0.04. Một cuộc trò chuyện sử dụng Claude 3.5 Sonnet có thể tốn 1 yêu cầu cho mỗi tin nhắn của người dùng, trong khi phản hồi của Cursor là miễn phí.78  
  * **Business Plan (Gói Doanh nghiệp):**  
    * Chi phí: $40/người dùng/tháng.  
    * Bao gồm: Mọi thứ trong gói Pro, cộng thêm: Thực thi chế độ riêng tư trên toàn tổ chức. Thanh toán tập trung cho nhóm. Bảng điều khiển quản trị với số liệu thống kê sử dụng. SAML/OIDC SSO. Mỗi người dùng vẫn nhận được 500 yêu cầu nhanh của riêng mình.  
  * **Định giá dựa trên mức sử dụng (Usage-Based Pricing):** Người dùng có thể chọn tham gia định giá dựa trên mức sử dụng từ bảng điều khiển của họ để tránh phải xếp hàng chờ và tiếp tục sử dụng các yêu cầu nhanh (hoặc để sử dụng chế độ Max sau khi đã sử dụng hết hạn ngạch). Điều này có nghĩa là họ sẽ bị tính phí cho các yêu cầu bổ sung vượt quá hạn ngạch của gói theo tỷ lệ tiêu chuẩn.78  
  * **Quyền sở hữu mã:** Người dùng sở hữu tất cả mã được tạo trong Cursor, bất kể gói đăng ký, và có thể sử dụng cho mục đích thương mại.77  
* **2.5. Ưu điểm so với các AI tool khác cùng loại (Advantages):**  
  * **Tích hợp AI sâu và hiệu suất vượt trội:** Người dùng mô tả Cursor là "ít nhất cải thiện gấp 2 lần so với Copilot" và "Copilot nên cảm thấy như thế nào". Nó được ghi nhận là "đi trước bộ não của tôi vài bước, đề xuất các chỉnh sửa nhiều dòng".51  
  * **Hiểu biết sâu sắc về codebase:** Không giống như một số công cụ chỉ phân tích ngữ cảnh tức thời, Cursor có thể "phân tích codebase" để thực hiện các tác vụ như viết README cho toàn bộ dự án, hoạt động ngay lần đầu tiên.51  
  * **Đề xuất chủ động và trực quan:** Tính năng "tự động hoàn thành thật điên rồ" và "dự đoán mã bạn muốn dựa trên những gì bạn đang làm", loại bỏ nhu cầu gợi ý rõ ràng. Nó cũng "đề xuất chỉnh sửa cho mã hiện có" và có thể nhận thấy sự không nhất quán.51  
  * **Quy trình làm việc liền mạch:** Người dùng đánh giá cao việc nó tích hợp GPT vào một trình soạn thảo mã "một cách liền mạch", loại bỏ nhu cầu "sao chép và dán". Nó được mô tả là "nhanh, tự động hoàn thành khi và nơi bạn cần, xử lý dấu ngoặc đúng cách, các phím tắt hợp lý, mang theo mô hình của riêng bạn... mọi thứ đều được kết hợp tốt".51  
  * **Hỗ trợ MCP:** Khả năng tích hợp với các máy chủ MCP như TalkToFigma mở rộng đáng kể chức năng của nó ra ngoài việc chỉ chỉnh sửa mã, cho phép tương tác trực tiếp với các công cụ thiết kế.72  
* **2.6. Ứng dụng trong các tác vụ thực tế nào (Real-world applications/use cases):**  
  * **Phát triển phần mềm nhanh chóng:** Tăng tốc độ viết mã thông qua các đề xuất thông minh và chỉnh sửa dự đoán.51  
  * **Tái cấu trúc và bảo trì mã:** Dễ dàng cập nhật các lớp hoặc hàm lớn bằng các lời nhắc bằng ngôn ngữ tự nhiên.51  
  * **Tìm hiểu và điều hướng codebase:** Nhanh chóng nhận câu trả lời và hiểu biết về các dự án hiện có bằng cách tham chiếu đến các tệp hoặc tài liệu cụ thể trong codebase.51  
  * **Tạo mã từ thiết kế:** Với các tích hợp MCP như TalkToFigma, nhà phát triển có thể trực tiếp chuyển đổi các yếu tố thiết kế hoặc nhận xét từ Figma thành mã.72  
  * **Gỡ lỗi và sửa lỗi:** Sử dụng khả năng nhận biết ngữ cảnh của AI để xác định và sửa lỗi hiệu quả hơn.  
* **2.7. Tài liệu hướng dẫn tìm hiểu sâu hơn (Documentation for further learning):**  
  * **Trang web chính thức:** [https://cursor.sh/](https://cursor.sh/) 51  
  * **Trang định giá:** [https://www.cursor.com/pricing](https://www.cursor.com/pricing) 77  
  * **Tài liệu về TalkToFigma MCP (ví dụ về tích hợp MCP):** [https://github.com/sonnylazuardi/cursor-talk-to-figma-mcp](https://github.com/sonnylazuardi/cursor-talk-to-figma-mcp) 72,  
    [https://playbooks.com/mcp/sonnylazuardi-talk-to-figma](https://playbooks.com/mcp/sonnylazuardi-talk-to-figma) 80

---

\*\*3. Augment (Augment Computing)\*\*

\*   \*\*3.1. Giới thiệu (Introduction):\*\*  
    \*   Augment Code là một nền tảng mã hóa được hỗ trợ bởi AI, được xây dựng cho các kỹ sư phần mềm chuyên nghiệp và các codebase lớn.\[52\]  
    \*   Nó sử dụng một "công cụ ngữ cảnh" (context engine) tiên tiến để hiểu toàn bộ codebase của người dùng, nhằm mục đích tăng năng suất của nhà phát triển và chất lượng mã.\[52, 82\]  
\*   \*\*3.2. Lịch sử phát triển (Development History):\*\*  
    \*   Augment Code được thành lập vào năm 2022.\[82\]  
    \*   Công ty đã nhanh chóng phát triển với các khoản đầu tư đáng kể và được công nhận, cho thấy những cải tiến đáng kể trong quy trình phát triển cho các công ty như Webflow và Kong.\[82\]  
\*   \*\*3.3. Các tính năng hiện tại đang có (Current Features) \[52, 69, 83\]:\*\*  
    \*   \*\*Kỹ thuật được hỗ trợ bởi Agent (Agent powered engineering):\*\* Hoàn thành tác vụ, xây dựng tính năng và giải quyết các vấn đề sản xuất với một agent hiểu rõ người dùng và codebase của họ. Sức mạnh của Agent được mở rộng thông qua các tích hợp gốc của Augment để đưa thêm ngữ cảnh vào mã.  
    \*   \*\*Chat thông minh với tích hợp sâu (Intelligent Chat):\*\* Nhận câu trả lời tức thì, lập kế hoạch dự án và xác định các thay đổi thông qua Chat. Sau đó, sử dụng "Smart Apply" để cập nhật mã một cách thông minh chỉ bằng một cú nhấp chuột.  
    \*   \*\*Hoàn thành mã (Code completions):\*\* Các đề xuất nhanh như chớp, nhận biết codebase khi người dùng gõ, được điều chỉnh cho phù hợp với cấu trúc, sự phụ thuộc và phong cách của dự án.  
    \*   \*\*Công cụ ngữ cảnh (Context Engine):\*\* Nền tảng của Augment, cho phép AI hiểu toàn bộ codebase, API, schema và các dependency để cung cấp hỗ trợ phù hợp.\[52, 69\]  
    \*   \*\*Hỗ trợ MCP & Công cụ gốc (MCP & Native Tools):\*\* Cho phép tích hợp với các giao thức và công cụ khác để mở rộng khả năng.\[69\] Context7 MCP có thể được cấu hình trong Augment Code.\[73\]  
    \*   \*\*Next Edits:\*\* Tính năng hoàn thành mã nâng cao, dự đoán các chỉnh sửa tiếp theo.  
    \*   \*\*Tích hợp IDE:\*\* Có sẵn dưới dạng plugin cho các IDE của JetBrains.\[52\]  
    \*   \*\*Giao diện người dùng nâng cao:\*\* Menu hamburger trong cửa sổ công cụ trò chuyện để truy cập nhanh vào Cài đặt, Trợ giúp & Tài liệu, Thanh toán, Đăng xuất.\[52\]  
    \*   \*\*Cải tiến hiệu suất:\*\* Quản lý trạng thái webview được cải thiện bằng cách lưu trữ JSON trực tiếp thay vì mã hóa base64, giảm mức sử dụng CPU khi khởi động plugin và khôi phục trạng thái, khởi tạo webview nhanh hơn.\[52\]  
\*   \*\*3.4. License / Subscription \[52, 69, 83\]:\*\*  
    \*   Augment Code sử dụng mô hình định giá dựa trên "User Messages" (Tin nhắn Người dùng). Một tin nhắn người dùng được tính khi người dùng gửi một lệnh, lời nhắc hoặc tin nhắn đến một agent IDE, một Instruction, một Remote Agent hoặc trong một cuộc trò chuyện.\[83\]  
    \*   \*\*Community Plan (Gói Cộng đồng):\*\*  
        \*   Chi phí: $0/tháng.  
        \*   Tin nhắn người dùng bao gồm: 50\.  
        \*   Tin nhắn người dùng bổ sung: $20/300.\[69, 83\]  
        \*   Tính năng: Context Engine, MCP & Native Tools, Next Edits & Completions không giới hạn.  
        \*   Hỗ trợ: Cộng đồng.  
        \*   Huấn luyện AI: Cho phép thu thập dữ liệu.  
    \*   \*\*Developer Plan (Gói Nhà phát triển):\*\*  
        \*   Chi phí: $50/tháng. (Khách hàng cũ trên gói $30 Developer có thể giữ nguyên mức giá này \[83\]).  
        \*   Tin nhắn người dùng bao gồm: 600\.  
        \*   Tin nhắn người dùng bổ sung: $30/300.  
        \*   Tính năng: Mọi thứ trong Community, cộng thêm Quản lý nhóm (lên đến 100 người dùng), Không huấn luyện AI trên dữ liệu của bạn, SOC 2 type II.  
        \*   Hỗ trợ: Cộng đồng.  
    \*   \*\*Pro Plan (Gói Chuyên nghiệp):\*\*  
        \*   Chi phí: $100/tháng.  
        \*   Tin nhắn người dùng bao gồm: 1.500.  
        \*   Tin nhắn người dùng bổ sung: $30/300.  
        \*   Tính năng: Mọi thứ trong Developer.  
        \*   Hỗ trợ: Cộng đồng & email.  
    \*   \*\*Max Plan (Gói Tối đa):\*\*  
        \*   Chi phí: $250/tháng.  
        \*   Tin nhắn người dùng bao gồm: 4.500.  
        \*   Tính năng: Mọi thứ trong Pro.  
        \*   Hỗ trợ: Cộng đồng & email.  
    \*   \*\*Enterprise Plan (Gói Doanh nghiệp):\*\*  
        \*   Chi phí: Giá tùy chỉnh.  
        \*   Tính năng: Định giá người dùng tùy chỉnh, giới hạn tin nhắn người dùng riêng, tích hợp Slack, giảm giá hàng năm dựa trên số lượng, hỗ trợ SSO, OIDC & SCIM, Báo cáo SOC 2 & Bảo mật, Hỗ trợ chuyên dụng.  
    \*   \*\*Dùng thử:\*\* 14 ngày, người dùng dùng thử nhận được Gói Developer (600 tin nhắn người dùng). Nếu mua vào cuối thời gian dùng thử, mức sử dụng sẽ được đặt lại.\[83\]  
    \*   \*\*Gộp tin nhắn người dùng:\*\* Tin nhắn người dùng được gộp ở cấp độ nhóm.\[83\]  
\*   \*\*3.5. Ưu điểm so với các AI tool khác cùng loại (Advantages):\*\*  
    \*   \*\*Context Engine mạnh mẽ:\*\* Khả năng hiểu toàn bộ codebase, bao gồm API, schema và các dependency, cho phép đưa ra các đề xuất và hỗ trợ chính xác hơn so với các công cụ chỉ phân tích tệp hiện tại.\[52\]  
    \*   \*\*Tập trung vào Kỹ sư Chuyên nghiệp và Codebase Lớn:\*\* Được thiết kế đặc biệt cho các dự án phần mềm quy mô lớn và phức tạp, nơi việc hiểu ngữ cảnh toàn diện là rất quan trọng.\[52, 82\]  
    \*   \*\*Agent-Powered Engineering:\*\* Cung cấp các agent AI có khả năng thực hiện các tác vụ phức tạp như xây dựng tính năng và giải quyết vấn đề sản xuất, không chỉ dừng lại ở việc hoàn thành mã.\[52\]  
    \*   \*\*Tích hợp sâu với IDE JetBrains:\*\* Cung cấp trải nghiệm liền mạch cho người dùng hệ sinh thái JetBrains.\[52\]  
    \*   \*\*Mô hình định giá dựa trên "User Messages":\*\* Cung cấp sự rõ ràng về chi phí, nơi mỗi tương tác có chủ đích với AI được tính là một tin nhắn.\[83\]  
\*   \*\*3.6. Ứng dụng trong các tác vụ thực tế nào (Real-world applications/use cases) \[52\]:\*\*  
    \*   \*\*Tìm hiểu hệ thống và codebase:\*\* Giúp nhà phát triển nhanh chóng làm quen với một hệ thống mới, điều tra lỗi hoặc học cách sử dụng API mới, ngay cả khi họ mới tham gia dự án hoặc làm việc trong một phần codebase không quen thuộc.  
    \*   \*\*Thực hiện cập nhật mã phức tạp:\*\* Hỗ trợ các thay đổi trong phần mềm cấp sản xuất bằng cách tự động hiểu mã, API, schema và các dependency, cung cấp mọi thứ cần thiết cho nhà phát triển.  
    \*   \*\*Hoàn thành tác vụ kỹ thuật:\*\* Xây dựng các tính năng mới, giải quyết các vấn đề sản xuất thông qua Agent.  
    \*   \*\*Lập kế hoạch dự án và xác định thay đổi:\*\* Sử dụng Chat thông minh để nhận câu trả lời tức thì, lập kế hoạch và áp dụng các thay đổi vào mã một cách thông minh.  
    \*   \*\*Tăng tốc độ viết mã:\*\* Cung cấp các đề xuất hoàn thành mã nhanh chóng và nhận biết ngữ cảnh.  
\*   \*\*3.7. Tài liệu hướng dẫn tìm hiểu sâu hơn (Documentation for further learning):\*\*  
    \*   \*\*Trang web chính thức:\*\* \[https://www.augmentcode.com/\](https://www.augmentcode.com/) \[69, 83\]  
    \*   \*\*Tài liệu (Docs):\*\* Có liên kết "Docs" trên trang định giá.\[69\]  
    \*   \*\*Trang plugin JetBrains:\*\* \[https://plugins.jetbrains.com/plugin/24072-augment\](https://plugins.jetbrains.com/plugin/24072-augment) \[52\]  
    \*   \*\*Trung tâm Tin cậy (Trust Center):\*\* Có liên kết "Trust Center" trên trang định giá.\[69\]

---

\*\*4. Windsurf\*\*

\*   \*\*4.1. Giới thiệu (Introduction):\*\*  
    \*   Windsurf là một trình soạn thảo mã nguồn mang tính "tác tử" (agentic IDE), được mô tả là tiên tiến hơn các công cụ như Cursor AI.\[53\]  
    \*   Nó được xây dựng để giữ cho nhà phát triển ở trạng thái "dòng chảy" (flow state) bằng cách kết hợp công việc của nhà phát triển và AI một cách liền mạch.\[62\]  
    \*   Windsurf không chỉ tự động hoàn thành hoặc tạo mã mà còn hiểu toàn bộ dự án trước khi đưa ra bất kỳ đề xuất nào, có thể tự động thực hiện thay đổi, gỡ lỗi và thậm chí chạy mã cho người dùng.\[53\]  
\*   \*\*4.2. Lịch sử phát triển (Development History):\*\*  
    \*   (Không có thông tin chi tiết về lịch sử phát triển trong các đoạn trích được cung cấp. Windsurf trước đây có thể được biết đến với tên Codeium \[62\]).  
\*   \*\*4.3. Các tính năng hiện tại đang có (Current Features) \[53, 62\]:\*\*  
    \*   \*\*Flows (Agent \+ Copilot):\*\* AI của Windsurf Editor có thể hoạt động như một Copilot (cộng tác với bạn) và như một Agent (độc lập giải quyết các tác vụ phức tạp). AI luôn đồng bộ với người dùng, cho phép nhà phát triển và AI hoạt động trên cùng một trạng thái.\[62\]  
    \*   \*\*Cascade (Tiến hóa của Chat):\*\*  
        \*   Kết hợp sự hiểu biết sâu sắc về codebase, một loạt các công cụ tiên tiến và nhận thức thời gian thực về hành động của người dùng thành một luồng làm việc mạnh mẽ, liền mạch và cộng tác.\[62\]  
        \*   \*\*Nhận thức ngữ cảnh đầy đủ:\*\* Cho phép chạy Cascade trên các codebase sản xuất và vẫn nhận được các đề xuất phù hợp.\[62\]  
        \*   \*\*Đề xuất và chạy lệnh:\*\* Các công cụ của Cascade bao gồm đề xuất và thực thi lệnh, cũng như phát hiện và gỡ lỗi sự cố.\[62\]  
        \*   \*\*Tiếp tục từ nơi bạn dừng lại:\*\* Tự động suy luận về các hành động rõ ràng của bạn, Cascade có thể tiếp tục công việc của bạn từ nơi bạn đã dừng lại.\[62\]  
        \*   \*\*Chỉnh sửa đa tệp:\*\* Chỉnh sửa đa tệp mạch lạc thông qua nhận thức ngữ cảnh, tích hợp công cụ và giải quyết vấn đề lặp đi lặp lại.\[62\]  
        \*   \*\*Các chế độ Cascade \[53\]:\*\*  
            \*   \*\*Cascade Write Mode:\*\* Hoạt động giống như AutoGPT, tạo nhiều tệp, chạy script, kiểm thử và gỡ lỗi. Tự động hóa khoảng 90% quy trình.  
            \*   \*\*Cascade Chat Mode:\*\* Kiểm tra ngữ cảnh, tạo mã và hướng dẫn sử dụng. Tự động hóa 50%, yêu cầu người dùng chỉnh sửa hoặc chèn đoạn mã, chạy mã thủ công.  
            \*   \*\*Cascade Legacy Mode:\*\* Hoạt động giống ChatGPT, yêu cầu cung cấp tất cả thông tin dưới dạng văn bản.  
    \*   \*\*Windsurf Previews:\*\* Xem trang web trực tiếp trong IDE, nhấp vào bất kỳ yếu tố nào và để Cascade định hình lại ngay lập tức. Có thể triển khai trực tiếp từ Windsurf.\[62\]  
    \*   \*\*Tích hợp Linter:\*\* Nếu Cascade tạo mã không vượt qua linter, nó sẽ tự động sửa lỗi.\[62\]  
    \*   \*\*Model Context Protocol (MCP):\*\* Nâng cao quy trình làm việc AI bằng cách kết nối với các công cụ và dịch vụ tùy chỉnh.\[62\]  
    \*   \*\*Tab to Jump:\*\* Dự đoán vị trí tiếp theo của con trỏ để điều hướng liền mạch qua tệp.\[62\]  
    \*   \*\*Supercomplete:\*\* Phân tích hành động tiếp theo của người dùng có thể là gì, vượt ra ngoài việc chỉ chèn đoạn mã tiếp theo.\[62\]  
    \*   \*\*Lệnh nội tuyến \+ Theo dõi (In-line Command \+ Follow ups):\*\* Nhấn Cmd \+ I trong trình soạn thảo để tạo hoặc tái cấu trúc mã nội tuyến bằng ngôn ngữ tự nhiên.\[62\]  
    \*   \*\*Lệnh trong Terminal (Command in Terminal):\*\* Nhấn Cmd \+ I trong terminal và nhập hướng dẫn terminal bằng ngôn ngữ tự nhiên.\[53, 62\]  
    \*   \*\*Codelenses:\*\* Có sẵn bên cạnh breadcrumbs, cho phép hiểu hoặc tái cấu trúc mã bằng một cú nhấp chuột.\[62\]  
    \*   \*\*Hành động mã được đánh dấu (Highlighted code actions):\*\* Đề cập trực tiếp đến mã được đánh dấu trong bảng Cascade hoặc tái cấu trúc nó bằng Lệnh.\[62\]  
    \*   \*\*@ mentions trong Cascade:\*\* Tham chiếu đến các hàm, lớp, tệp hoặc toàn bộ thư mục để hướng dẫn Cascade đến ngữ cảnh liên quan.\[62\]  
    \*   \*\*Tải lên hình ảnh:\*\* Tải lên hình ảnh của một trang web và yêu cầu xây dựng tệp CSS, HTML, JavaScript.\[53\]  
    \*   \*\*Ngữ cảnh cục bộ và bên ngoài:\*\* Cung cấp ngữ cảnh bổ sung như trang web, đoạn mã, tài liệu gói Python hoặc tệp/thư mục cụ thể khi viết lời nhắc.\[53\]  
    \*   \*\*Nhiều lựa chọn mô hình AI:\*\* Truy cập vào một loạt các mô hình AI thông minh, từ Deepseek R1 đến Gemini 2.0 Flash mới nhất. Claude 3.5 được khuyến nghị cho hầu hết các tác vụ tạo mã.\[53\]  
    \*   \*\*Chỉnh sửa nội tuyến (Inline edits):\*\* Nhấp vào phần cụ thể của mã và nhấn Ctrl \+ I để truy cập chỉnh sửa nội tuyến, cho phép chỉnh sửa các phần cụ thể của mã thay vì toàn bộ tệp.\[53\]  
\*   \*\*4.4. License / Subscription:\*\*  
    \*   Tải xuống miễn phí với 100 "prompt credits" và các tính năng Pro.\[62\]  
    \*   (Thông tin chi tiết về các gói trả phí không có trong các đoạn trích, nhưng việc đề cập đến "prompt credits" và "tính năng Pro" cho thấy có khả năng tồn tại các bậc trả phí).  
\*   \*\*4.5. Ưu điểm so với các AI tool khác cùng loại (Advantages):\*\*  
    \*   \*\*Agentic Capabilities Nâng cao:\*\* Windsurf tự định vị mình là một "agentic IDE" với các tính năng như Cascade Write Mode hoạt động giống AutoGPT, có khả năng tự động hóa một phần lớn quy trình tạo và gỡ lỗi mã.\[53, 62\]  
    \*   \*\*Hiểu biết toàn bộ dự án:\*\* Không chỉ hoàn thành mã, Windsurf được thiết kế để hiểu toàn bộ dự án trước khi đưa ra đề xuất, cho phép chỉnh sửa đa tệp mạch lạc.\[53, 62\]  
    \*   \*\*Tương tác và kiểm soát người dùng:\*\* Các tính năng như Cascade Chat Mode và khả năng phê duyệt các bước do AI đề xuất trước khi chạy chúng trong terminal mang lại sự cân bằng giữa tự động hóa và kiểm soát của người dùng.\[53\]  
    \*   \*\*Tích hợp Terminal và MCP:\*\* Khả năng tương tác với terminal bằng ngôn ngữ tự nhiên và hỗ trợ MCP để kết nối với các công cụ tùy chỉnh mở rộng đáng kể phạm vi hoạt động của nó.\[53, 62\]  
    \*   \*\*Đa dạng mô hình AI:\*\* Cung cấp quyền truy cập vào nhiều mô hình AI hàng đầu, cho phép người dùng chọn mô hình phù hợp nhất cho tác vụ cụ thể.\[53\]  
    \*   \*\*Trải nghiệm "Flow State":\*\* Toàn bộ thiết kế của Windsurf nhằm mục đích tạo ra một trải nghiệm mã hóa liền mạch, nơi AI và nhà phát triển hoạt động đồng bộ.\[62\]  
\*   \*\*4.6. Ứng dụng trong các tác vụ thực tế nào (Real-world applications/use cases) \[53, 62\]:\*\*  
    \*   \*\*Phát triển dự án từ đầu:\*\* Sử dụng Cascade Write Mode để AI tạo nhiều tệp, chạy script, kiểm thử và gỡ lỗi.  
    \*   \*\*Chỉnh sửa và tái cấu trúc mã hiện có:\*\* Sử dụng Inline AI và Cascade Chat Mode để thực hiện các thay đổi cụ thể, tạo docstring, tái cấu trúc các phần mã.  
    \*   \*\*Gỡ lỗi:\*\* Sử dụng AI Terminal để tạo mã hoặc khắc phục lỗi trực tiếp trong terminal.  
    \*   \*\*Xây dựng giao diện người dùng:\*\* Tải lên hình ảnh thiết kế web để AI tạo các tệp HTML, CSS, JavaScript tương ứng.  
    \*   \*\*Nghiên cứu và tích hợp tài liệu:\*\* Cung cấp ngữ cảnh từ các trang web, tài liệu để nhận được câu trả lời và mã phù hợp hơn.  
    \*   \*\*Tự động hóa các tác vụ dòng lệnh:\*\* Sử dụng Command in Terminal để AI tạo và chạy các lệnh.  
    \*   \*\*Triển khai ứng dụng web:\*\* Xem trước và triển khai ứng dụng trực tiếp từ IDE.  
\*   \*\*4.7. Tài liệu hướng dẫn tìm hiểu sâu hơn (Documentation for further learning):\*\*  
    \*   \*\*Trang web chính thức của Windsurf Editor:\*\* \[https://windsurf.com/editor\](https://windsurf.com/editor) \[62\]  
    \*   \*\*Tùy chọn tải xuống cho macOS, Linux, Windows:\*\* Có sẵn trên trang web chính thức.\[62\]  
    \*   \*\*Bài viết đánh giá (bên thứ ba):\*\* \[https://www.datacamp.com/tutorial/windsurf-ai-agentic-code-editor\](https://www.datacamp.com/tutorial/windsurf-ai-agentic-code-editor) \[53\]

---

\*\*5. ChatGPT Codex (OpenAI)\*\*

\*   \*\*5.1. Giới thiệu (Introduction):\*\*  
    \*   Codex là một tác tử kỹ thuật phần mềm dựa trên đám mây, được tích hợp vào ChatGPT, có khả năng làm việc song song trên nhiều tác vụ mã hóa.\[54, 55\]  
    \*   Nó được cung cấp bởi \`codex-1\`, một biến thể của mô hình suy luận o3 của OpenAI, được tối ưu hóa đáng kể cho kỹ thuật phần mềm.\[54, 55\]  
\*   \*\*5.2. Lịch sử phát triển (Development History):\*\*  
    \*   \*\*Giới thiệu:\*\* OpenAI công bố Codex vào ngày 16 tháng 5 năm 2025, ban đầu dưới dạng bản xem trước nghiên cứu.\[54\]  
    \*   \*\*Đào tạo:\*\* \`codex-1\` được đào tạo bằng cách sử dụng học tăng cường trên các tác vụ mã hóa trong thế giới thực trong nhiều môi trường khác nhau để tạo ra mã gần giống với phong cách của con người và các ưu tiên PR, tuân thủ chính xác các hướng dẫn và có thể chạy thử nghiệm lặp đi lặp lại cho đến khi nhận được kết quả đạt yêu cầu.\[54\]  
    \*   \*\*Triển khai:\*\* Bắt đầu triển khai cho người dùng ChatGPT Pro, Enterprise và Team vào ngày 16 tháng 5 năm 2025\. Hỗ trợ cho người dùng Plus và Edu được lên kế hoạch sau đó.\[54\] Đến ngày 3 tháng 6 năm 2025, Codex đã có sẵn cho người dùng ChatGPT Plus.\[54\] Người dùng cũng có thể cấp cho Codex quyền truy cập internet trong quá trình thực hiện tác vụ.\[54\]  
\*   \*\*5.3. Các tính năng hiện tại đang có (Current Features) \[54, 55\]:\*\*  
    \*   \*\*Thực hiện tác vụ mã hóa song song:\*\* Có thể làm việc trên nhiều tác vụ đồng thời.  
    \*   \*\*Môi trường sandbox dựa trên đám mây:\*\* Mỗi tác vụ được thực thi trong một sandbox dựa trên đám mây chuyên dụng, được thiết lập sẵn với kho lưu trữ của dự án người dùng.  
    \*   \*\*Đa dạng tác vụ:\*\*  
        \*   Triển khai tính năng mới.  
        \*   Sửa lỗi.  
        \*   Truy vấn codebase (trả lời các câu hỏi kỹ thuật về codebase của người dùng).  
        \*   Tạo yêu cầu kéo (pull requests) để xem xét.  
    \*   \*\*Tương tác với tệp và lệnh:\*\* Có thể đọc và sửa đổi tệp, cũng như thực thi các lệnh như chạy bộ kiểm thử, linter và trình kiểm tra kiểu.  
    \*   \*\*Minh bạch và có thể kiểm chứng:\*\* Cung cấp bằng chứng có thể kiểm chứng về các hành động của mình thông qua trích dẫn nhật ký terminal và kết quả kiểm thử.  
    \*   \*\*Quy trình làm việc:\*\* Sau khi hoàn thành một tác vụ, Codex cam kết các thay đổi của mình trong môi trường của nó. Người dùng có thể xem xét kết quả, yêu cầu sửa đổi thêm, mở một yêu cầu kéo GitHub hoặc tích hợp trực tiếp các thay đổi vào môi trường phát triển cục bộ của họ.  
    \*   \*\*Môi trường có thể cấu hình:\*\* Người dùng có thể cấu hình không gian làm việc của Codex để phản ánh thiết lập phát triển thực tế của họ càng sát càng tốt.  
    \*   \*\*Căn chỉnh theo sở thích của con người:\*\* \`codex-1\` được đào tạo để tạo ra các bản vá lỗi rõ ràng, sẵn sàng để con người xem xét và tích hợp ngay lập tức vào các quy trình làm việc tiêu chuẩn, tốt hơn so với OpenAI o3.  
    \*   \*\*Ngữ cảnh tối đa:\*\* \`codex-1\` được thử nghiệm với độ dài ngữ cảnh tối đa là 192k token và "nỗ lực suy luận" trung bình.  
\*   \*\*5.4. License / Subscription \[54, 55\]:\*\*  
    \*   \*\*Có sẵn cho:\*\*  
        \*   Người dùng ChatGPT Pro  
        \*   Người dùng ChatGPT Team  
        \*   Người dùng ChatGPT Enterprise  
        \*   Người dùng ChatGPT Plus (từ ngày 3 tháng 6 năm 2025\)  
    \*

#### **Nguồn trích dẫn**

1. ChatGPT | OpenAI, truy cập vào tháng 6 13, 2025, [https://openai.com/chatgpt/overview/](https://openai.com/chatgpt/overview/)  
2. ChatGPT \- Wikipedia, truy cập vào tháng 6 13, 2025, [https://en.wikipedia.org/wiki/ChatGPT](https://en.wikipedia.org/wiki/ChatGPT)  
3. A Complete Guide to Grok AI (xAI) \- Learn Prompting, truy cập vào tháng 6 13, 2025, [https://learnprompting.org/blog/guide-grok](https://learnprompting.org/blog/guide-grok)  
4. Google AI Pro & Ultra — get access to Gemini 2.5 Pro & more, truy cập vào tháng 6 13, 2025, [https://gemini.google/subscriptions/](https://gemini.google/subscriptions/)  
5. Gemini \- Google DeepMind, truy cập vào tháng 6 13, 2025, [https://deepmind.google/models/gemini/](https://deepmind.google/models/gemini/)  
6. Perplexity \- Ask Anything on the App Store \- Apple, truy cập vào tháng 6 13, 2025, [https://apps.apple.com/us/app/perplexity-ask-anything/id1668000334](https://apps.apple.com/us/app/perplexity-ask-anything/id1668000334)  
7. Perplexity \- AI: Artificial Intelligence Resources \- Research Guides, truy cập vào tháng 6 13, 2025, [https://libguides.und.edu/ai-resources/perplexity](https://libguides.und.edu/ai-resources/perplexity)  
8. ChatGPT Pricing Plans: Which Plan is Right for You?, truy cập vào tháng 6 13, 2025, [https://www.beingguru.com/chatgpt-pricing-plans-which-plan-is-right-for-you/](https://www.beingguru.com/chatgpt-pricing-plans-which-plan-is-right-for-you/)  
9. ChatGPT Pricing Breakdown (2025): Plans, Limits, API Costs ..., truy cập vào tháng 6 13, 2025, [https://www.brainchat.ai/blog/chatgpt-pricing](https://www.brainchat.ai/blog/chatgpt-pricing)  
10. Perplexity Enterprise Pro Pricing | Get Started Today, truy cập vào tháng 6 13, 2025, [https://www.perplexity.ai/enterprise/pricing](https://www.perplexity.ai/enterprise/pricing)  
11. Grok AI Pricing: How Much Does Grok Cost in 2025? \- Tech.co, truy cập vào tháng 6 13, 2025, [https://tech.co/news/grok-ai-pricing](https://tech.co/news/grok-ai-pricing)  
12. Grok-3: How to Access and Use It \- Chatbase, truy cập vào tháng 6 13, 2025, [https://www.chatbase.co/blog/grok-3](https://www.chatbase.co/blog/grok-3)  
13. What Can Grok AI Do? Key Features You Should Know \- AI Tools \- God of Prompt, truy cập vào tháng 6 13, 2025, [https://www.godofprompt.ai/blog/what-can-grok-ai-do](https://www.godofprompt.ai/blog/what-can-grok-ai-do)  
14. ChatGPT Capabilities Overview \- OpenAI Help Center, truy cập vào tháng 6 13, 2025, [https://help.openai.com/en/articles/9260256-chatgpt-capabilities-overview](https://help.openai.com/en/articles/9260256-chatgpt-capabilities-overview)  
15. History of Artificial Intelligence \- ChatGPT \- Research Guides at Black Hawk College, truy cập vào tháng 6 13, 2025, [https://bhc.libguides.com/c.php?g=1301302\&p=9561894](https://bhc.libguides.com/c.php?g=1301302&p=9561894)  
16. ChatGPT \- Apps on Google Play, truy cập vào tháng 6 13, 2025, [https://play.google.com/store/apps/details?id=com.openai.chatgpt](https://play.google.com/store/apps/details?id=com.openai.chatgpt)  
17. ChatGPT Deep Research \- Wikipedia, truy cập vào tháng 6 13, 2025, [https://en.wikipedia.org/wiki/ChatGPT\_Deep\_Research](https://en.wikipedia.org/wiki/ChatGPT_Deep_Research)  
18. Introducing deep research \- OpenAI, truy cập vào tháng 6 13, 2025, [https://openai.com/index/introducing-deep-research/](https://openai.com/index/introducing-deep-research/)  
19. API Reference \- OpenAI API, truy cập vào tháng 6 13, 2025, [https://platform.openai.com/docs/api-reference](https://platform.openai.com/docs/api-reference)  
20. 50 ChatGPT Use Cases with Real Life Examples in 2025 \- Research AIMultiple, truy cập vào tháng 6 13, 2025, [https://research.aimultiple.com/chatgpt-use-cases/](https://research.aimultiple.com/chatgpt-use-cases/)  
21. How to Use ChatGPT for Technical Documentation: A Beginner's Guide \- PaceAI, truy cập vào tháng 6 13, 2025, [https://paceai.co/chatgpt-for-technical-documentation/](https://paceai.co/chatgpt-for-technical-documentation/)  
22. en.wikipedia.org, truy cập vào tháng 6 13, 2025, [https://en.wikipedia.org/wiki/Perplexity\_AI\#:\~:text=Perplexity%20AI%2C%20Inc.%20was%20founded,app%20for%20iOS%20and%20Android.](https://en.wikipedia.org/wiki/Perplexity_AI#:~:text=Perplexity%20AI%2C%20Inc.%20was%20founded,app%20for%20iOS%20and%20Android.)  
23. en.wikipedia.org, truy cập vào tháng 6 13, 2025, [https://en.wikipedia.org/wiki/Aravind\_Srinivas\#:\~:text=of%20California%2C%20Berkeley.-,Career,Johnny%20Ho%2C%20and%20Andy%20Konwinski.](https://en.wikipedia.org/wiki/Aravind_Srinivas#:~:text=of%20California%2C%20Berkeley.-,Career,Johnny%20Ho%2C%20and%20Andy%20Konwinski.)  
24. Aravind Srinivas \- Wikipedia, truy cập vào tháng 6 13, 2025, [https://en.wikipedia.org/wiki/Aravind\_Srinivas](https://en.wikipedia.org/wiki/Aravind_Srinivas)  
25. Academic Filter Guide \- Perplexity, truy cập vào tháng 6 13, 2025, [https://docs.perplexity.ai/guides/academic-filter-guide](https://docs.perplexity.ai/guides/academic-filter-guide)  
26. 14 Perplexity AI Use Cases \- Learn Prompting, truy cập vào tháng 6 13, 2025, [https://learnprompting.org/blog/perplexity\_use\_cases](https://learnprompting.org/blog/perplexity_use_cases)  
27. Perplexity AI \- Apps Documentation, truy cập vào tháng 6 13, 2025, [https://apps.make.com/perplexity-ai](https://apps.make.com/perplexity-ai)  
28. Report: xAI Business Breakdown & Founding Story | Contrary Research, truy cập vào tháng 6 13, 2025, [https://research.contrary.com/company/xai](https://research.contrary.com/company/xai)  
29. What Is Grok AI | Features, Benefits and How It Works \- Simplilearn.com, truy cập vào tháng 6 13, 2025, [https://www.simplilearn.com/grok-ai-article](https://www.simplilearn.com/grok-ai-article)  
30. Understanding Grok: A Comprehensive Guide to Grok Websearch, Grok DeepSearch, truy cập vào tháng 6 13, 2025, [https://www.tryprofound.com/blog/understanding-grok-a-comprehensive-guide-to-grok-websearch-grok-deepsearch](https://www.tryprofound.com/blog/understanding-grok-a-comprehensive-guide-to-grok-websearch-grok-deepsearch)  
31. The Hitchhiker's Guide to Grok \- xAI Docs, truy cập vào tháng 6 13, 2025, [https://docs.x.ai/docs/tutorial](https://docs.x.ai/docs/tutorial)  
32. What is Grok AI? Is It Worth the Hype? \- TechRepublic, truy cập vào tháng 6 13, 2025, [https://www.techrepublic.com/article/what-is-grok-ai/](https://www.techrepublic.com/article/what-is-grok-ai/)  
33. What is Grok AI: Features And Benefits Explained \- igmGuru, truy cập vào tháng 6 13, 2025, [https://www.igmguru.com/blog/what-is-grok-ai](https://www.igmguru.com/blog/what-is-grok-ai)  
34. Top 10 Most Creative Use Cases of Grok 3 \- SentiSight.ai, truy cập vào tháng 6 13, 2025, [https://www.sentisight.ai/top-10-most-creative-use-cases-of-grok-3/](https://www.sentisight.ai/top-10-most-creative-use-cases-of-grok-3/)  
35. 10 Best Grok 3 Prompts for Deep Research \- AI Tools, truy cập vào tháng 6 13, 2025, [https://www.godofprompt.ai/blog/10-best-grok-3-prompts-for-deep-research](https://www.godofprompt.ai/blog/10-best-grok-3-prompts-for-deep-research)  
36. Introduction | xAI Docs, truy cập vào tháng 6 13, 2025, [https://docs.x.ai/docs/introduction](https://docs.x.ai/docs/introduction)  
37. What is Gemini and how it works \- Google Gemini, truy cập vào tháng 6 13, 2025, [https://gemini.google/overview/](https://gemini.google/overview/)  
38. AI Tools for Business | Google Workspace, truy cập vào tháng 6 13, 2025, [https://workspace.google.com/solutions/ai/](https://workspace.google.com/solutions/ai/)  
39. Gemini Developer API Pricing | Gemini API | Google AI for Developers, truy cập vào tháng 6 13, 2025, [https://ai.google.dev/pricing](https://ai.google.dev/pricing)  
40. Gemini Pricing: Everything You'll Pay for Google Gemini \- UC Today, truy cập vào tháng 6 13, 2025, [https://www.uctoday.com/collaboration/gemini-pricing-everything-youll-pay-for-google-gemini/](https://www.uctoday.com/collaboration/gemini-pricing-everything-youll-pay-for-google-gemini/)  
41. Gemini: Intro & Use Cases \- happtiq, truy cập vào tháng 6 13, 2025, [https://www.happtiq.com/blog/google-cloud-gemini](https://www.happtiq.com/blog/google-cloud-gemini)  
42. Create detailed reports with Deep Research | Google Workspace Blog, truy cập vào tháng 6 13, 2025, [https://workspace.google.com/blog/ai-and-machine-learning/meet-deep-research-your-new-ai-research-assistant](https://workspace.google.com/blog/ai-and-machine-learning/meet-deep-research-your-new-ai-research-assistant)  
43. Use Gemini Apps for in-depth research \- Android \- Google Help, truy cập vào tháng 6 13, 2025, [https://support.google.com/gemini/answer/15719111?hl=en](https://support.google.com/gemini/answer/15719111?hl=en)  
44. Gemini in Chrome | The next generation of AI in Chrome \- Google, truy cập vào tháng 6 13, 2025, [https://www.google.com/chrome/ai-innovations/](https://www.google.com/chrome/ai-innovations/)  
45. Gemini for Google Cloud Pricing, truy cập vào tháng 6 13, 2025, [https://cloud.google.com/products/gemini/pricing](https://cloud.google.com/products/gemini/pricing)  
46. 101 ways our customers are using AI for business | Google Workspace Blog, truy cập vào tháng 6 13, 2025, [https://workspace.google.com/blog/ai-and-machine-learning/how-our-customers-are-using-ai-for-business](https://workspace.google.com/blog/ai-and-machine-learning/how-our-customers-are-using-ai-for-business)  
47. Gemini for Google Cloud documentation, truy cập vào tháng 6 13, 2025, [https://cloud.google.com/gemini/docs](https://cloud.google.com/gemini/docs)  
48. Gemini API using Firebase AI Logic \- Google, truy cập vào tháng 6 13, 2025, [https://firebase.google.com/docs/ai-logic](https://firebase.google.com/docs/ai-logic)  
49. About Copilot agents \- GitHub Docs, truy cập vào tháng 6 13, 2025, [https://docs.github.com/en/copilot/building-copilot-extensions/building-a-copilot-agent-for-your-copilot-extension/about-copilot-agents](https://docs.github.com/en/copilot/building-copilot-extensions/building-a-copilot-agent-for-your-copilot-extension/about-copilot-agents)  
50. GitHub Copilot features, truy cập vào tháng 6 13, 2025, [https://docs.github.com/en/copilot/about-github-copilot/github-copilot-features](https://docs.github.com/en/copilot/about-github-copilot/github-copilot-features)  
51. Cursor \- The AI Code Editor, truy cập vào tháng 6 13, 2025, [https://cursor.sh/](https://cursor.sh/)  
52. Augment Plugin for JetBrains IDEs | JetBrains Marketplace, truy cập vào tháng 6 13, 2025, [https://plugins.jetbrains.com/plugin/24072-augment](https://plugins.jetbrains.com/plugin/24072-augment)  
53. Windsurf AI Agentic Code Editor: Features, Setup, and Use Cases | DataCamp, truy cập vào tháng 6 13, 2025, [https://www.datacamp.com/tutorial/windsurf-ai-agentic-code-editor](https://www.datacamp.com/tutorial/windsurf-ai-agentic-code-editor)  
54. Introducing Codex | OpenAI, truy cập vào tháng 6 13, 2025, [https://openai.com/index/introducing-codex/](https://openai.com/index/introducing-codex/)  
55. OpenAI made an AI software engineer, it is called Codex: Full story in 5 points \- India Today, truy cập vào tháng 6 13, 2025, [https://www.indiatoday.in/technology/news/story/openai-made-an-ai-software-engineer-it-is-called-codex-full-story-in-5-points-2726187-2025-05-17](https://www.indiatoday.in/technology/news/story/openai-made-an-ai-software-engineer-it-is-called-codex-full-story-in-5-points-2726187-2025-05-17)  
56. Build with Jules, your asynchronous coding agent \- Google Blog, truy cập vào tháng 6 13, 2025, [https://blog.google/technology/google-labs/jules/](https://blog.google/technology/google-labs/jules/)  
57. Google's Jules AI Coding Agent Can Assist – But Does Not Replace – Developers, truy cập vào tháng 6 13, 2025, [https://www.techrepublic.com/article/news-google-jules-ai-agent-public-beta/](https://www.techrepublic.com/article/news-google-jules-ai-agent-public-beta/)  
58. Home \- Anthropic, truy cập vào tháng 6 13, 2025, [https://docs.anthropic.com/en/docs/agents/claude-code/introduction](https://docs.anthropic.com/en/docs/agents/claude-code/introduction)  
59. Write beautiful code, ship powerful products | Claude by Anthropic, truy cập vào tháng 6 13, 2025, [https://www.anthropic.com/solutions/coding?ref=allthings.how](https://www.anthropic.com/solutions/coding?ref=allthings.how)  
60. Zencoder – The AI Coding Agent, truy cập vào tháng 6 13, 2025, [https://zencoder.ai/](https://zencoder.ai/)  
61. GitHub Copilot · Your AI pair programmer · GitHub, truy cập vào tháng 6 13, 2025, [https://github.com/features/copilot](https://github.com/features/copilot)  
62. Windsurf Editor | Windsurf (formerly Codeium), truy cập vào tháng 6 13, 2025, [https://windsurf.com/editor](https://windsurf.com/editor)  
63. www.trae.ai, truy cập vào tháng 6 13, 2025, [https://www.trae.ai/\#:\~:text=Trae%20is%20an%20AI%2Dpowered,efficient%20development%20of%20complete%20projects.\&text=Trae%20enhances%20coding%20efficiency%20with,making%20development%20faster%20and%20smoother\!\&text=Writing%20code%20has%20never%20been%20that%20easier\!](https://www.trae.ai/#:~:text=Trae%20is%20an%20AI%2Dpowered,efficient%20development%20of%20complete%20projects.&text=Trae%20enhances%20coding%20efficiency%20with,making%20development%20faster%20and%20smoother!&text=Writing%20code%20has%20never%20been%20that%20easier!)  
64. Trae: A New Free AI-Powered Code Editor from ByteDance | DigitalOcean, truy cập vào tháng 6 13, 2025, [https://www.digitalocean.com/community/tutorials/trae-free-ai-code-editor](https://www.digitalocean.com/community/tutorials/trae-free-ai-code-editor)  
65. Void \+ Ollama \+ LLMs: How I Turned My Code Editor into a Full ..., truy cập vào tháng 6 13, 2025, [https://dev.to/nodeshiftcloud/void-ollama-llms-how-i-turned-my-code-editor-into-a-full-blown-ai-workbench-eop](https://dev.to/nodeshiftcloud/void-ollama-llms-how-i-turned-my-code-editor-into-a-full-blown-ai-workbench-eop)  
66. 10 Best Generative AI Code Generation Tools to Consider in 2025 \- Zencoder, truy cập vào tháng 6 13, 2025, [https://zencoder.ai/blog/generative-ai-code-generation-tools](https://zencoder.ai/blog/generative-ai-code-generation-tools)  
67. LM Studio \- Docs | Continue, truy cập vào tháng 6 13, 2025, [https://docs.continue.dev/customize/model-providers/more/lmstudio](https://docs.continue.dev/customize/model-providers/more/lmstudio)  
68. Releases · ollama/ollama \- GitHub, truy cập vào tháng 6 13, 2025, [https://github.com/ollama/ollama/releases](https://github.com/ollama/ollama/releases)  
69. Pricing \- Augment Code, truy cập vào tháng 6 13, 2025, [https://www.augmentcode.com/pricing](https://www.augmentcode.com/pricing)  
70. A beginners Guide on Model Context Protocol (MCP) \- OpenCV, truy cập vào tháng 6 13, 2025, [https://opencv.org/blog/model-context-protocol/](https://opencv.org/blog/model-context-protocol/)  
71. Model Context Protocol (MCP) \- Anthropic API, truy cập vào tháng 6 13, 2025, [https://docs.anthropic.com/en/docs/agents-and-tools/mcp](https://docs.anthropic.com/en/docs/agents-and-tools/mcp)  
72. sonnylazuardi/cursor-talk-to-figma-mcp \- GitHub, truy cập vào tháng 6 13, 2025, [https://github.com/sonnylazuardi/cursor-talk-to-figma-mcp](https://github.com/sonnylazuardi/cursor-talk-to-figma-mcp)  
73. upstash/context7: Context7 MCP Server \-- Up-to-date code ... \- GitHub, truy cập vào tháng 6 13, 2025, [https://github.com/upstash/context7](https://github.com/upstash/context7)  
74. TaskMaster | Glama, truy cập vào tháng 6 13, 2025, [https://glama.ai/mcp/servers/@mingolladaniele/taskMaster-todoist-mcp](https://glama.ai/mcp/servers/@mingolladaniele/taskMaster-todoist-mcp)  
75. Playwright MCP \- Browser Rendering \- Cloudflare Docs, truy cập vào tháng 6 13, 2025, [https://developers.cloudflare.com/browser-rendering/platform/playwright-mcp/](https://developers.cloudflare.com/browser-rendering/platform/playwright-mcp/)  
76. Remote GitHub MCP Server is now in public preview \- GitHub Changelog, truy cập vào tháng 6 13, 2025, [https://github.blog/changelog/2025-06-12-remote-github-mcp-server-is-now-available-in-public-preview/](https://github.blog/changelog/2025-06-12-remote-github-mcp-server-is-now-available-in-public-preview/)  
77. Pricing | Cursor \- The AI Code Editor, truy cập vào tháng 6 13, 2025, [https://www.cursor.com/pricing](https://www.cursor.com/pricing)  
78. A Complete Guide to Cursor's New Pricing: Subscriptions and Request Quotas \- Apidog, truy cập vào tháng 6 13, 2025, [https://apidog.com/blog/cursor-pricing-guide/](https://apidog.com/blog/cursor-pricing-guide/)  
79. Cursor Talk to Figma MCP Server | GenAI Works, truy cập vào tháng 6 13, 2025, [https://genai.works/mcp-servers/Cursor-Talk-to-Figma-MCP-Server](https://genai.works/mcp-servers/Cursor-Talk-to-Figma-MCP-Server)  
80. Talk to Figma MCP server for AI agents \- Playbooks, truy cập vào tháng 6 13, 2025, [https://playbooks.com/mcp/sonnylazuardi-talk-to-figma](https://playbooks.com/mcp/sonnylazuardi-talk-to-figma)  
81. Talk to Figma MCP server for AI agents \- Playbooks, truy cập vào tháng 6 13, 2025, [https://playbooks.com/mcp/yhc984-talk-to-figma](https://playbooks.com/mcp/yhc984-talk-to-figma)  
82. Augment AI- Coding assistant Startup Secures $252M, Rivals GitHub Copilot \- DhiWise, truy cập vào tháng 6 13, 2025, [https://www.dhiwise.com/post/augment-ai-coding-assistant-startup](https://www.dhiwise.com/post/augment-ai-coding-assistant-startup)  
83. New, simpler pricing: Pay for what you control. Enjoy everything else. \- Augment Code, truy cập vào tháng 6 13, 2025, [https://www.augmentcode.com/blog/new-simpler-pricing-with-user-messages](https://www.augmentcode.com/blog/new-simpler-pricing-with-user-messages)